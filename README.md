<div align="center">
  <img src="https://github.com/user-attachments/assets/92fd93ed-e71b-4b94-b270-50684323dd00" alt="<PERSON>" width="120" height="120">

  <a href="https://claudiacode.com"><h1>Claudia</h1></a>
  
  <p>
    <strong>A powerful GUI app and Toolkit for Claude Code</strong>
  </p>
  <p>
    <strong>Create custom agents, manage interactive Claude Code sessions, run secure background agents, and more.</strong>
  </p>
  
  <p>
    <a href="#features"><img src="https://img.shields.io/badge/Features-✨-blue?style=for-the-badge" alt="Features"></a>
    <a href="#installation"><img src="https://img.shields.io/badge/Install-🚀-green?style=for-the-badge" alt="Installation"></a>
    <a href="#usage"><img src="https://img.shields.io/badge/Usage-📖-purple?style=for-the-badge" alt="Usage"></a>
    <a href="#development"><img src="https://img.shields.io/badge/Develop-🛠️-orange?style=for-the-badge" alt="Development"></a>
  </p>
</div>

![457013521-6133a738-d0cb-4d3e-8746-c6768c82672c](https://github.com/user-attachments/assets/a028de9e-d881-44d8-bae5-7326ab3558b9)

https://github.com/user-attachments/assets/bf0bdf9d-ba91-45af-9ac4-7274f57075cf

> [!TIP]
> **⭐ Star the repo and follow [@getAsterisk](https://x.com/getAsterisk) on X for early access to `asteria-swe-v0`**.

## 🌟 Overview

**Claudia** is a powerful desktop application that transforms how you interact with Claude Code. Built with Tauri 2, it provides a beautiful GUI for managing your Claude Code sessions, creating custom agents, tracking usage, and much more.

This enhanced version includes **8 major new features**: AI-powered code review, voice control integration, real-time collaboration, smart templates, visual code flow representation, performance profiling, advanced theming, and a plugin system.

Think of Claudia as your command center for Claude Code - bridging the gap between the command-line tool and a visual experience that makes AI-assisted development more intuitive and productive.

## 📋 Table of Contents

- [🌟 Overview](#-overview)
- [✨ Features](#-features)
  - [🗂️ Project & Session Management](#️-project--session-management)
  - [🤖 CC Agents](#-cc-agents)
  - [🔍 AI-Powered Code Review](#-ai-powered-code-review)
  - [🎤 Voice Control Integration](#-voice-control-integration)
  - [👥 Real-time Collaboration](#-real-time-collaboration)
  - [📋 Smart Templates System](#-smart-templates-system)
  - [🌊 Visual Code Flow Representation](#-visual-code-flow-representation)
  - [⚡ Performance Profiler](#-performance-profiler)
  - [🎨 Advanced Theme System](#-advanced-theme-system)
  - [🧩 Plugin Manager](#-plugin-manager)
  - [📊 Usage Analytics Dashboard](#-usage-analytics-dashboard)
  - [🔌 MCP Server Management](#-mcp-server-management)
  - [⏰ Timeline & Checkpoints](#-timeline--checkpoints)
  - [📝 CLAUDE.md Management](#-claudemd-management)
- [📖 Usage](#-usage)
  - [Getting Started](#getting-started)
  - [Managing Projects](#managing-projects)
  - [Creating Agents](#creating-agents)
  - [Using Voice Control](#using-voice-control)
  - [Code Review & Collaboration](#code-review--collaboration)
  - [Performance Monitoring](#performance-monitoring)
  - [Customizing Themes & Plugins](#customizing-themes--plugins)
  - [Tracking Usage](#tracking-usage)
  - [Working with MCP Servers](#working-with-mcp-servers)
- [🚀 Installation](#-installation)
- [🔨 Build from Source](#-build-from-source)
- [🛠️ Development](#️-development)
- [🔒 Security](#-security)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [🙏 Acknowledgments](#-acknowledgments)

## ✨ Features

### 🗂️ **Project & Session Management**
- **Visual Project Browser**: Navigate through all your Claude Code projects in `~/.claude/projects/`
- **Session History**: View and resume past coding sessions with full context
- **Smart Search**: Find projects and sessions quickly with built-in search
- **Session Insights**: See first messages, timestamps, and session metadata at a glance

### 🤖 **CC Agents**
- **Custom AI Agents**: Create specialized agents with custom system prompts and behaviors
- **Agent Library**: Build a collection of purpose-built agents for different tasks
- **Background Execution**: Run agents in separate processes for non-blocking operations
- **Execution History**: Track all agent runs with detailed logs and performance metrics

### 🔍 **AI-Powered Code Review**
- **Intelligent Analysis**: Automated code review using Claude's advanced reasoning capabilities
- **Multi-Language Support**: Review code across different programming languages and frameworks
- **Security Scanning**: Identify potential security vulnerabilities and suggest fixes
- **Best Practices**: Get recommendations for code quality, performance, and maintainability
- **Custom Rules**: Configure review criteria specific to your project requirements
- **Detailed Reports**: Export comprehensive review reports in multiple formats

### 🎤 **Voice Control Integration**
- **Hands-Free Operation**: Control Claudia using natural voice commands
- **Smart Commands**: Pre-built voice patterns for common actions (code review, checkpoints, etc.)
- **Multi-Language Support**: Recognition in multiple languages with configurable settings
- **Audio Feedback**: Sound cues for successful command recognition and execution
- **Continuous Mode**: Keep voice control active for seamless interaction
- **Command Reference**: Built-in help for all available voice commands

### 👥 **Real-time Collaboration**
- **Live Sessions**: Share coding sessions with team members in real-time
- **Role-Based Access**: Owner, editor, and viewer permissions for secure collaboration
- **Cursor Tracking**: See where collaborators are working with live cursor positions
- **Instant Invitations**: Send session invites via email with secure access codes
- **Session Management**: Start, stop, and manage collaborative sessions with ease
- **WebSocket Integration**: Real-time updates with minimal latency

### 📋 **Smart Templates System**
- **Project Scaffolding**: Quick-start templates for common project types and frameworks
- **Custom Templates**: Create and share your own project templates
- **Variable Substitution**: Dynamic templates with configurable parameters
- **Template Library**: Browse and discover community-contributed templates
- **Usage Analytics**: Track which templates work best for your workflow
- **Import/Export**: Share templates across teams and organizations

### 🌊 **Visual Code Flow Representation**
- **Interactive Diagrams**: Visualize code structure and dependencies as dynamic graphs
- **Dependency Mapping**: See how files, functions, and modules connect
- **Complexity Analysis**: Identify code hotspots and complexity bottlenecks
- **Interactive Navigation**: Click on nodes to navigate directly to code locations
- **Real-time Updates**: Flow diagrams update as your code changes
- **Export Options**: Save diagrams for documentation and presentations

### ⚡ **Performance Profiler**
- **API Usage Monitoring**: Track Claude API calls, tokens, and costs in real-time
- **Performance Metrics**: Detailed analytics on latency, error rates, and throughput
- **Cost Optimization**: Identify expensive operations and optimization opportunities
- **Usage Patterns**: Visualize API usage trends with interactive charts
- **Bottleneck Detection**: Automatic identification of performance issues
- **Export Reports**: Generate detailed performance reports for analysis

### 🎨 **Advanced Theme System**
- **Multiple Color Schemes**: Choose from built-in themes (ocean, forest, sunset, midnight, etc.)
- **Dynamic Switching**: Switch between light, dark, and system themes instantly
- **Accessibility Options**: High contrast mode and reduced motion support
- **Color Blind Support**: Specialized color schemes for different types of color blindness
- **Custom Fonts**: Adjustable font sizes for optimal readability
- **CSS Variables**: Consistent theming across all interface components

### 🧩 **Plugin Manager**
- **Extensible Architecture**: Extend Claudia's functionality with custom plugins
- **Plugin Registry**: Browse and install plugins from the community marketplace
- **Easy Installation**: One-click plugin installation and management
- **Developer Tools**: Built-in tools for creating and testing custom plugins
- **Secure Execution**: Sandboxed plugin environment for security
- **Settings Management**: Configure plugin settings through intuitive UI

### 📊 **Usage Analytics Dashboard**
- **Cost Tracking**: Monitor your Claude API usage and costs in real-time
- **Token Analytics**: Detailed breakdown by model, project, and time period
- **Visual Charts**: Beautiful charts showing usage trends and patterns
- **Export Data**: Export usage data for accounting and analysis

### 🔌 **Enhanced MCP Server Management**
- **Comprehensive Marketplace**: 25+ pre-configured MCP servers for various use cases
- **One-Click Installation**: Automated setup script for essential MCP servers
- **Server Categories**: Organized by Development Tools, AI Enhancement, Web & Data, Database, Cloud Services, and Security
- **Easy Configuration**: Add servers via UI, JSON import, or Claude Desktop configs
- **Connection Testing**: Verify server connectivity and validate configurations
- **Environment Management**: Secure handling of API keys and environment variables
- **Usage Analytics**: Track MCP server performance and usage patterns

### ⏰ **Timeline & Checkpoints**
- **Session Versioning**: Create checkpoints at any point in your coding session
- **Visual Timeline**: Navigate through your session history with a branching timeline
- **Instant Restore**: Jump back to any checkpoint with one click
- **Fork Sessions**: Create new branches from existing checkpoints
- **Diff Viewer**: See exactly what changed between checkpoints

### 📝 **CLAUDE.md Management**
- **Built-in Editor**: Edit CLAUDE.md files directly within the app
- **Live Preview**: See your markdown rendered in real-time
- **Project Scanner**: Find all CLAUDE.md files in your projects
- **Syntax Highlighting**: Full markdown support with syntax highlighting

## 🔧 Environment Setup

### MCP Server Configuration

Before using MCP servers with Claudia Enhanced, you need to set up environment variables:

1. **Copy the environment template**:
   ```bash
   cp .env.mcp.template .env.mcp
   ```

2. **Edit `.env.mcp`** and fill in your API keys and configuration values:
   - API keys for services (OpenAI, Anthropic, GitHub, etc.)
   - Database connection strings
   - Cloud service credentials
   - Other service-specific settings

3. **Keep your `.env.mcp` file secure** - it contains sensitive credentials and should never be committed to version control.

> [!IMPORTANT]
> Many MCP servers require API keys or authentication tokens to function. Make sure to obtain the necessary credentials from each service provider before enabling their respective MCP servers.

## 📖 Usage

### Getting Started

1. **Launch Claudia**: Open the application after installation
2. **Welcome Screen**: Choose between CC Agents or CC Projects
3. **First Time Setup**: Claudia will automatically detect your `~/.claude` directory

### Managing Projects

```
CC Projects → Select Project → View Sessions → Resume or Start New
```

- Click on any project to view its sessions
- Each session shows the first message and timestamp
- Resume sessions directly or start new ones

### Creating Agents

```
CC Agents → Create Agent → Configure → Execute
```

1. **Design Your Agent**: Set name, icon, and system prompt
2. **Configure Model**: Choose between available Claude models
3. **Set Permissions**: Configure file read/write and network access
4. **Execute Tasks**: Run your agent on any project

### Using Voice Control

```
Session → Click Microphone → Say Command → Execute
```

**Available Commands:**
- "Run code review" - Starts AI-powered code analysis
- "Show timeline" - Opens session timeline view
- "Create checkpoint" - Saves current session state
- "Send prompt [your text]" - Sends a voice-to-text prompt
- "Switch to opus/sonnet" - Changes AI model
- "Cancel execution" - Stops current operation

**Settings:**
- Enable continuous listening mode
- Choose recognition language
- Configure audio feedback

### Code Review & Collaboration

**AI Code Review:**
```
Session → Code Review Button → Configure Rules → Run Analysis
```

**Real-time Collaboration:**
```
Session → Collaborate Button → Share Link → Invite Team
```

1. **Start Collaboration**: Click the collaborate button in any active session
2. **Invite Members**: Share the generated session code or send email invites
3. **Manage Permissions**: Set collaborators as editors or viewers
4. **Live Interaction**: See real-time cursor positions and changes

### Performance Monitoring

```
Menu → Performance → View Metrics → Analyze Usage
```

**Key Metrics:**
- API call frequency and latency
- Token consumption by model
- Cost tracking and optimization suggestions
- Error rates and bottleneck identification
- Usage patterns and trends

**Optimization:**
- Export detailed reports for analysis
- Set up alerts for unusual usage
- Identify expensive operations

### Customizing Themes & Plugins

**Theme Customization:**
```
Top Bar → Theme Switcher → Choose Options → Apply
```

- Switch between light/dark/system themes
- Select color schemes (ocean, forest, sunset, etc.)
- Adjust font sizes and accessibility options
- Configure color blind-friendly palettes

**Plugin Management:**
```
Menu → Plugins → Browse Registry → Install/Configure
```

- Browse available plugins in the marketplace
- Install plugins with one click
- Configure plugin settings through UI
- Create custom plugins with developer tools

### Tracking Usage

```
Menu → Usage Dashboard → View Analytics
```

- Monitor costs by model, project, and date
- Export data for reports
- Set up usage alerts (coming soon)

### Working with MCP Servers

**Quick Setup (Recommended):**
```bash
# Run the automated setup script
./scripts/setup-essential-mcps.sh
```

**Manual Setup:**
```
Menu → MCP Manager → Browse Marketplace → Install → Configure
```

**Available MCP Server Categories:**

🛠️ **Development Tools**
- **Filesystem**: Local file operations and management
- **Git**: Version control operations and repository management
- **GitHub**: Repository management, issues, and pull requests
- **Docker**: Container management and deployment
- **Kubernetes**: Cluster management and orchestration

🤖 **AI Enhancement**
- **Memory**: Persistent context and conversation history
- **Sequential Thinking**: Advanced reasoning and problem-solving
- **OpenAI**: Integration with OpenAI models and APIs

🌐 **Web & Data**
- **Fetch**: HTTP client for API interactions
- **Brave Search**: Web search capabilities
- **Puppeteer**: Browser automation and web scraping
- **AgentQL**: Intelligent web data extraction

💾 **Database**
- **SQLite**: Local database operations
- **PostgreSQL**: Advanced database management
- **AWS Bedrock KB**: Knowledge base integration

☁️ **Cloud Services**
- **Google Drive**: File storage and collaboration
- **Slack**: Team communication and notifications
- **Stripe**: Payment processing and e-commerce

🔒 **Security & Utilities**
- **Vault**: Secret management and secure storage
- **Time**: Time operations and scheduling
- **Everything**: Windows file search (Windows only)
- **Obsidian Vault**: Note management and knowledge base

**Configuration Steps:**
1. **Install Prerequisites**: Ensure Node.js and required tools are installed
2. **Set Environment Variables**: Configure API keys in `.env.mcp` file
3. **Add to Claudia**: Use MCP Manager to add configured servers
4. **Test Connection**: Verify server connectivity before use
5. **Start Using**: Access MCP capabilities in your Claude Code sessions

**Environment Variables Setup:**
```bash
# Copy the template and configure your keys
cp .env.mcp.template .env.mcp
# Edit .env.mcp with your API keys and tokens
```

**Troubleshooting:**
- Check server logs in MCP Manager for connection issues
- Verify API keys and environment variables are correctly set
- Ensure required dependencies are installed for each server
- Use the connection test feature to diagnose problems

## 🚀 Installation

### Prerequisites

- **Claude Code CLI**: Install from [Claude's official site](https://claude.ai/code)

### Release Executables Will Be Published Soon

## 🔨 Build from Source

### Prerequisites

Before building Claudia from source, ensure you have the following installed:

#### System Requirements

- **Operating System**: Windows 10/11, macOS 11+, or Linux (Ubuntu 20.04+)
- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: At least 1GB free space

#### Required Tools

1. **Rust** (1.70.0 or later)
   ```bash
   # Install via rustup
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   ```

2. **Bun** (latest version)
   ```bash
   # Install bun
   curl -fsSL https://bun.sh/install | bash
   ```

3. **Git**
   ```bash
   # Usually pre-installed, but if not:
   # Ubuntu/Debian: sudo apt install git
   # macOS: brew install git
   # Windows: Download from https://git-scm.com
   ```

4. **Claude Code CLI**
   - Download and install from [Claude's official site](https://claude.ai/code)
   - Ensure `claude` is available in your PATH

#### Platform-Specific Dependencies

**Linux (Ubuntu/Debian)**
```bash
# Install system dependencies
sudo apt update
sudo apt install -y \
  libwebkit2gtk-4.1-dev \
  libgtk-3-dev \
  libayatana-appindicator3-dev \
  librsvg2-dev \
  patchelf \
  build-essential \
  curl \
  wget \
  file \
  libssl-dev \
  libxdo-dev \
  libsoup-3.0-dev \
  libjavascriptcoregtk-4.1-dev
```

**macOS**
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install additional dependencies via Homebrew (optional)
brew install pkg-config
```

**Windows**
- Install [Microsoft C++ Build Tools](https://visualstudio.microsoft.com/visual-cpp-build-tools/)
- Install [WebView2](https://developer.microsoft.com/microsoft-edge/webview2/) (usually pre-installed on Windows 11)

### Build Steps

1. **Clone the Repository**
   ```bash
   git clone https://github.com/getAsterisk/claudia.git
   cd claudia
   ```

2. **Install Frontend Dependencies**
   ```bash
   bun install
   ```

3. **Build the Application**
   
   **For Development (with hot reload)**
   ```bash
   bun run tauri dev
   ```
   
   **For Production Build**
   ```bash
   # Build the application
   bun run tauri build
   
   # The built executable will be in:
   # - Linux: src-tauri/target/release/bundle/
   # - macOS: src-tauri/target/release/bundle/
   # - Windows: src-tauri/target/release/bundle/
   ```

4. **Platform-Specific Build Options**
   
   **Debug Build (faster compilation, larger binary)**
   ```bash
   bun run tauri build --debug
   ```
   
   **Build without bundling (creates just the executable)**
   ```bash
   bun run tauri build --no-bundle
   ```
   
   **Universal Binary for macOS (Intel + Apple Silicon)**
   ```bash
   bun run tauri build --target universal-apple-darwin
   ```

### Troubleshooting

#### Common Issues

1. **"cargo not found" error**
   - Ensure Rust is installed and `~/.cargo/bin` is in your PATH
   - Run `source ~/.cargo/env` or restart your terminal

2. **Linux: "webkit2gtk not found" error**
   - Install the webkit2gtk development packages listed above
   - On newer Ubuntu versions, you might need `libwebkit2gtk-4.0-dev`

3. **Windows: "MSVC not found" error**
   - Install Visual Studio Build Tools with C++ support
   - Restart your terminal after installation

4. **"claude command not found" error**
   - Ensure Claude Code CLI is installed and in your PATH
   - Test with `claude --version`

5. **Build fails with "out of memory"**
   - Try building with fewer parallel jobs: `cargo build -j 2`
   - Close other applications to free up RAM

#### Verify Your Build

After building, you can verify the application works:

```bash
# Run the built executable directly
# Linux/macOS
./src-tauri/target/release/claudia

# Windows
./src-tauri/target/release/claudia.exe
```

### Build Artifacts

The build process creates several artifacts:

- **Executable**: The main Claudia application
- **Installers** (when using `tauri build`):
  - `.deb` package (Linux)
  - `.AppImage` (Linux)
  - `.dmg` installer (macOS)
  - `.msi` installer (Windows)
  - `.exe` installer (Windows)

All artifacts are located in `src-tauri/target/release/bundle/`.

## 🛠️ Development

### Tech Stack

- **Frontend**: React 18 + TypeScript + Vite 6
- **Backend**: Rust with Tauri 2
- **UI Framework**: Tailwind CSS v4 + shadcn/ui
- **Database**: SQLite (via rusqlite)
- **Package Manager**: Bun

### Project Structure

```
claudia/
├── src/                   # React frontend
│   ├── components/        # UI components
│   ├── lib/               # API client & utilities
│   └── assets/            # Static assets
├── src-tauri/             # Rust backend
│   ├── src/
│   │   ├── commands/      # Tauri command handlers
│   │   ├── checkpoint/    # Timeline management
│   │   └── process/       # Process management
│   └── tests/             # Rust test suite
└── public/                # Public assets
```

### Development Commands

```bash
# Start development server
bun run tauri dev

# Run frontend only
bun run dev

# Type checking
bunx tsc --noEmit

# Run Rust tests
cd src-tauri && cargo test

# Format code
cd src-tauri && cargo fmt
```

## 🔒 Security

Claudia prioritizes your privacy and security:

1. **Process Isolation**: Agents run in separate processes
2. **Permission Control**: Configure file and network access per agent
3. **Local Storage**: All data stays on your machine
4. **No Telemetry**: No data collection or tracking
5. **Open Source**: Full transparency through open source code

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Areas for Contribution

- 🐛 Bug fixes and improvements
- ✨ New features and enhancements
- 📚 Documentation improvements
- 🎨 UI/UX enhancements
- 🧪 Test coverage
- 🌐 Internationalization

## 📄 License

This project is licensed under the AGPL License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with [Tauri](https://tauri.app/) - The secure framework for building desktop apps
- [Claude](https://claude.ai) by Anthropic

---

<div align="center">
  <p>
    <strong>Made with ❤️ by the <a href="https://asterisk.so/">Asterisk</a></strong>
  </p>
  <p>
    <a href="https://github.com/getAsterisk/claudia/issues">Report Bug</a>
    ·
    <a href="https://github.com/getAsterisk/claudia/issues">Request Feature</a>
  </p>
</div>


## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=getAsterisk/claudia&type=Date)](https://www.star-history.com/#getAsterisk/claudia&Date)
