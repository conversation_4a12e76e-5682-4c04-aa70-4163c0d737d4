import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Settings,
  Zap,
  Network,
  Code,
  Shield,
  Gauge,
  Bot,
  Globe,
  ChevronDown,
  ChevronUp,
  Play,
  Pause,
  Eye,
  EyeOff,
  Sparkles,
  Brain,
  Clock,
  Database,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSuperClaude } from '@/hooks/useSuperClaude';
import { useFeatureFlags } from '@/lib/featureFlags';
import { useFocusMode } from '@/hooks/useFocusMode';

interface QuickCustomizationBarProps {
  onOpenFullCustomizer: () => void;
  className?: string;
}

const QUICK_MODES = [
  { id: 'development', name: 'Dev', icon: Code, color: 'bg-blue-500' },
  { id: 'analysis', name: 'Analysis', icon: Gauge, color: 'bg-green-500' },
  { id: 'creative', name: 'Creative', icon: Sparkles, color: 'bg-purple-500' },
  { id: 'secure', name: 'Secure', icon: Shield, color: 'bg-red-500' },
];

const QUICK_FEATURES = [
  { key: 'voiceControl', name: 'Voice', icon: Bot },
  { key: 'collaboration', name: 'Collab', icon: Globe },
  { key: 'codeFlow', name: 'Flow', icon: Network },
  { key: 'performanceProfiler', name: 'Perf', icon: Gauge },
];

export const QuickCustomizationBar: React.FC<QuickCustomizationBarProps> = ({
  onOpenFullCustomizer,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeMode, setActiveMode] = useState('development');
  
  const { settings: superClaudeSettings, updateSettings: updateSuperClaudeSettings } = useSuperClaude();
  const { flags: featureFlags, updateFlags: updateFeatureFlags } = useFeatureFlags();
  const { config: focusMode, toggleFocusMode, applyPreset } = useFocusMode();

  const handleModeChange = (mode: string) => {
    setActiveMode(mode);
    
    // Apply preset based on mode
    switch (mode) {
      case 'development':
        applyPreset('development');
        updateSuperClaudeSettings({
          ...superClaudeSettings,
          coreModes: {
            ...superClaudeSettings.coreModes,
            tokenEconomy: true,
            introspection: false,
          },
        });
        break;
      case 'analysis':
        applyPreset('analysis');
        updateSuperClaudeSettings({
          ...superClaudeSettings,
          coreModes: {
            ...superClaudeSettings.coreModes,
            tokenEconomy: false,
            introspection: true,
          },
        });
        break;
      case 'creative':
        updateSuperClaudeSettings({
          ...superClaudeSettings,
          coreModes: {
            ...superClaudeSettings.coreModes,
            tokenEconomy: false,
            introspection: false,
          },
          thinkingDepth: {
            ...superClaudeSettings.thinkingDepth,
            level: 'deep',
          },
        });
        break;
      case 'secure':
        applyPreset('minimal');
        updateSuperClaudeSettings({
          ...superClaudeSettings,
          coreModes: {
            ...superClaudeSettings.coreModes,
            tokenEconomy: true,
            introspection: false,
          },
        });
        break;
    }
  };

  const handleFeatureToggle = (featureKey: string, enabled: boolean) => {
    updateFeatureFlags({
      [featureKey]: { enabled },
    });
  };

  const activeModeConfig = QUICK_MODES.find(m => m.id === activeMode);

  return (
    <TooltipProvider>
      <motion.div
        className={`fixed bottom-20 left-1/2 -translate-x-1/2 z-40 ${className}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
      >
        <Card className="bg-background/95 backdrop-blur-md border shadow-lg overflow-hidden">
          <div className="p-3">
            {/* Collapsed View */}
            <AnimatePresence>
              {!isExpanded && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="flex items-center gap-3"
                >
                  {/* Active Mode Indicator */}
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${activeModeConfig?.color}`} />
                    <span className="text-xs font-medium">{activeModeConfig?.name}</span>
                  </div>

                  <Separator orientation="vertical" className="h-4" />

                  {/* Quick Feature Toggles */}
                  <div className="flex items-center gap-1">
                    {QUICK_FEATURES.slice(0, 3).map((feature) => {
                      const isEnabled = featureFlags[feature.key as keyof typeof featureFlags]?.enabled;
                      const Icon = feature.icon;
                      return (
                        <Tooltip key={feature.key}>
                          <TooltipTrigger asChild>
                            <Button
                              variant={isEnabled ? "default" : "ghost"}
                              size="sm"
                              className="h-6 w-6 p-0"
                              onClick={() => handleFeatureToggle(feature.key, !isEnabled)}
                            >
                              <Icon className="h-3 w-3" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {feature.name} {isEnabled ? 'Enabled' : 'Disabled'}
                          </TooltipContent>
                        </Tooltip>
                      );
                    })}
                  </div>

                  <Separator orientation="vertical" className="h-4" />

                  {/* Focus Mode Toggle */}
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={focusMode.enabled ? "default" : "ghost"}
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={toggleFocusMode}
                      >
                        {focusMode.enabled ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      Focus Mode {focusMode.enabled ? 'On' : 'Off'}
                    </TooltipContent>
                  </Tooltip>

                  {/* Expand Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => setIsExpanded(true)}
                  >
                    <ChevronUp className="h-3 w-3" />
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Expanded View */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3"
                >
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">Quick Customization</h3>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={onOpenFullCustomizer}
                        className="text-xs h-6"
                      >
                        <Settings className="h-3 w-3 mr-1" />
                        Full
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => setIsExpanded(false)}
                      >
                        <ChevronDown className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Mode Selection */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Mode</Label>
                    <div className="flex gap-1 mt-1">
                      {QUICK_MODES.map((mode) => {
                        const Icon = mode.icon;
                        return (
                          <Tooltip key={mode.id}>
                            <TooltipTrigger asChild>
                              <Button
                                variant={activeMode === mode.id ? "default" : "outline"}
                                size="sm"
                                className="flex-1 h-8 text-xs"
                                onClick={() => handleModeChange(mode.id)}
                              >
                                <Icon className="h-3 w-3 mr-1" />
                                {mode.name}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              Switch to {mode.name} mode
                            </TooltipContent>
                          </Tooltip>
                        );
                      })}
                    </div>
                  </div>

                  {/* Feature Toggles */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Features</Label>
                    <div className="grid grid-cols-2 gap-2 mt-1">
                      {QUICK_FEATURES.map((feature) => {
                        const isEnabled = featureFlags[feature.key as keyof typeof featureFlags]?.enabled;
                        const Icon = feature.icon;
                        return (
                          <div key={feature.key} className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              <Icon className="h-3 w-3" />
                              <span className="text-xs">{feature.name}</span>
                            </div>
                            <Switch
                              checked={isEnabled}
                              onCheckedChange={(checked) => handleFeatureToggle(feature.key, checked)}
                              className="scale-75"
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* SuperClaude Quick Settings */}
                  <div>
                    <Label className="text-xs text-muted-foreground">AI Settings</Label>
                    <div className="space-y-2 mt-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1">
                          <Brain className="h-3 w-3" />
                          <span className="text-xs">Introspection</span>
                        </div>
                        <Switch
                          checked={superClaudeSettings.coreModes.introspection}
                          onCheckedChange={(checked) => 
                            updateSuperClaudeSettings({
                              ...superClaudeSettings,
                              coreModes: { ...superClaudeSettings.coreModes, introspection: checked }
                            })
                          }
                          className="scale-75"
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1">
                          <Zap className="h-3 w-3" />
                          <span className="text-xs">Token Economy</span>
                        </div>
                        <Switch
                          checked={superClaudeSettings.coreModes.tokenEconomy}
                          onCheckedChange={(checked) => 
                            updateSuperClaudeSettings({
                              ...superClaudeSettings,
                              coreModes: { ...superClaudeSettings.coreModes, tokenEconomy: checked }
                            })
                          }
                          className="scale-75"
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span className="text-xs">Thinking Depth</span>
                        </div>
                        <Select
                          value={superClaudeSettings.thinkingDepth.level}
                          onValueChange={(value: 'minimal' | 'standard' | 'deep' | 'exhaustive') =>
                            updateSuperClaudeSettings({
                              ...superClaudeSettings,
                              thinkingDepth: { ...superClaudeSettings.thinkingDepth, level: value }
                            })
                          }
                        >
                          <SelectTrigger className="h-6 text-xs w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="minimal">Min</SelectItem>
                            <SelectItem value="standard">Std</SelectItem>
                            <SelectItem value="deep">Deep</SelectItem>
                            <SelectItem value="exhaustive">Max</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Active MCP Servers Indicator */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Active MCPs</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {Object.entries(superClaudeSettings.mcpServers)
                        .filter(([_, enabled]) => enabled)
                        .slice(0, 4)
                        .map(([server]) => (
                          <Badge key={server} variant="secondary" className="text-xs h-5">
                            <Database className="h-2 w-2 mr-1" />
                            {server}
                          </Badge>
                        ))}
                      {Object.values(superClaudeSettings.mcpServers).filter(Boolean).length > 4 && (
                        <Badge variant="outline" className="text-xs h-5">
                          +{Object.values(superClaudeSettings.mcpServers).filter(Boolean).length - 4}
                        </Badge>
                      )}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </Card>
      </motion.div>
    </TooltipProvider>
  );
};