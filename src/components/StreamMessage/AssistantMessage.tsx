import React from 'react';
import { Bot } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { claudeSyntaxTheme } from '@/lib/claudeSyntaxTheme';
import {
  ThinkingWidget,
  TaskWidget,
  EditWidget,
  MultiEditWidget,
  MCPWidget,
  TodoWidget,
  TodoReadWidget,
  LSWidget,
  ReadWidget,
  GlobWidget,
  BashWidget,
  WriteWidget,
  GrepWidget,
  WebSearchWidget,
  WebFetchWidget,
} from '../ToolWidgets';
import type { ClaudeStreamMessage } from '../AgentExecution';

interface AssistantMessageProps {
  message: ClaudeStreamMessage;
  className?: string;
  getToolResult: (toolId: string | undefined) => any;
}

export const AssistantMessage: React.FC<AssistantMessageProps> = ({
  message,
  className,
  getToolResult,
}) => {
  if (message.type !== "assistant" || !message.message) {
    return null;
  }

  const msg = message.message;
  let renderedSomething = false;

  const renderToolWidget = (content: any, toolResult: any) => {
    const toolName = content.name?.toLowerCase();
    const input = content.input;

    // Task tool - for sub-agent tasks
    if (toolName === "task" && input) {
      renderedSomething = true;
      return <TaskWidget description={input.description} prompt={input.prompt} result={toolResult} />;
    }

    // Edit tool
    if (toolName === "edit" && input?.file_path) {
      renderedSomething = true;
      return <EditWidget {...input} result={toolResult} />;
    }

    // MultiEdit tool
    if (toolName === "multiedit" && input?.file_path && input?.edits) {
      renderedSomething = true;
      return <MultiEditWidget {...input} result={toolResult} />;
    }

    // MCP tools (starting with mcp__)
    if (content.name?.startsWith("mcp__")) {
      renderedSomething = true;
      return <MCPWidget toolName={content.name} input={input} result={toolResult} />;
    }

    // TodoWrite tool
    if (toolName === "todowrite" && input?.todos) {
      renderedSomething = true;
      return <TodoWidget todos={input.todos} result={toolResult} />;
    }

    // TodoRead tool
    if (toolName === "todoread") {
      renderedSomething = true;
      return <TodoReadWidget todos={input?.todos} result={toolResult} />;
    }

    // LS tool
    if (toolName === "ls" && input?.path) {
      renderedSomething = true;
      return <LSWidget path={input.path} result={toolResult} />;
    }

    // Read tool
    if (toolName === "read" && input?.file_path) {
      renderedSomething = true;
      return <ReadWidget filePath={input.file_path} result={toolResult} />;
    }

    // Glob tool
    if (toolName === "glob" && input?.pattern) {
      renderedSomething = true;
      return <GlobWidget pattern={input.pattern} result={toolResult} />;
    }

    // Bash tool
    if (toolName === "bash" && input?.command) {
      renderedSomething = true;
      return <BashWidget command={input.command} description={input.description} result={toolResult} />;
    }

    // Write tool
    if (toolName === "write" && input?.file_path && input?.content) {
      renderedSomething = true;
      return <WriteWidget filePath={input.file_path} content={input.content} result={toolResult} />;
    }

    // Grep tool
    if (toolName === "grep" && input?.pattern) {
      renderedSomething = true;
      return <GrepWidget pattern={input.pattern} include={input.include} path={input.path} exclude={input.exclude} result={toolResult} />;
    }

    // WebSearch tool
    if (toolName === "websearch" && input?.query) {
      renderedSomething = true;
      return <WebSearchWidget query={input.query} result={toolResult} />;
    }

    // WebFetch tool
    if (toolName === "webfetch" && input?.url) {
      renderedSomething = true;
      return <WebFetchWidget url={input.url} prompt={input.prompt} result={toolResult} />;
    }

    // Default - fallback to basic tool display
    return null;
  };

  const renderedCard = (
    <Card className={cn("border-primary/20 bg-primary/5", className)}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Bot className="h-5 w-5 text-primary mt-0.5" />
          <div className="flex-1 space-y-2 min-w-0">
            {msg.content && Array.isArray(msg.content) && msg.content.map((content: any, idx: number) => {
              // Text content - render as markdown
              if (content.type === "text") {
                const textContent = typeof content.text === 'string' 
                  ? content.text 
                  : (content.text?.text || JSON.stringify(content.text || content));
                
                renderedSomething = true;
                return (
                  <div key={idx} className="prose prose-sm dark:prose-invert max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        code({ node, inline, className, children, ...props }: any) {
                          const match = /language-(\w+)/.exec(className || '');
                          return !inline && match ? (
                            <SyntaxHighlighter
                              style={claudeSyntaxTheme}
                              language={match[1]}
                              PreTag="div"
                              {...props}
                            >
                              {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                          ) : (
                            <code className={className} {...props}>
                              {children}
                            </code>
                          );
                        }
                      }}
                    >
                      {textContent}
                    </ReactMarkdown>
                  </div>
                );
              }
              
              // Thinking content - render with ThinkingWidget
              if (content.type === "thinking") {
                renderedSomething = true;
                return (
                  <div key={idx}>
                    <ThinkingWidget 
                      thinking={content.thinking || ''} 
                      signature={content.signature}
                    />
                  </div>
                );
              }
              
              // Tool use - render custom widgets based on tool name
              if (content.type === "tool_use") {
                const toolId = content.id;
                const toolResult = getToolResult(toolId);
                
                const widget = renderToolWidget(content, toolResult);
                if (widget) {
                  renderedSomething = true;
                  return <div key={idx}>{widget}</div>;
                }
                
                // Fallback to basic tool display if no specific widget
                renderedSomething = true;
                return (
                  <div key={idx} className="space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        Using tool: <code className="font-mono">{content.name}</code>
                      </span>
                    </div>
                    {content.input && (
                      <div className="ml-6 p-2 bg-background rounded-md border">
                        <pre className="text-xs font-mono overflow-x-auto">
                          {JSON.stringify(content.input, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                );
              }
              
              return null;
            })}
            
            {msg.usage && (
              <div className="text-xs text-muted-foreground mt-2">
                Tokens: {msg.usage.input_tokens} in, {msg.usage.output_tokens} out
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
  
  if (!renderedSomething) return null;
  return renderedCard;
};