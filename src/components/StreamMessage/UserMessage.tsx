import React from 'react';
import { User, CheckCircle2, AlertCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import {
  CommandWidget,
  CommandOutputWidget,
  SystemReminderWidget,
  EditResultWidget,
  MultiEditResultWidget,
  LSResultWidget,
  ReadResultWidget,
} from '../ToolWidgets';
import type { ClaudeStreamMessage } from '../AgentExecution';

interface UserMessageProps {
  message: ClaudeStreamMessage;
  className?: string;
  streamMessages: ClaudeStreamMessage[];
  onLinkDetected?: (url: string) => void;
}

export const UserMessage: React.FC<UserMessageProps> = ({
  message,
  className,
  streamMessages,
  onLinkDetected,
}) => {
  if (message.type !== "user") {
    return null;
  }

  // Don't render meta messages, which are for system use
  if (message.isMeta) return null;

  // Handle different message structures
  const msg = message.message || message;
  let renderedSomething = false;

  const renderedCard = (
    <Card className={cn("border-muted-foreground/20 bg-muted/20", className)}>
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <User className="h-5 w-5 text-muted-foreground mt-0.5" />
          <div className="flex-1 space-y-2 min-w-0">
            {/* Handle content that is a simple string (e.g. from user commands) */}
            {(typeof msg.content === 'string' || (msg.content && !Array.isArray(msg.content))) && (
              (() => {
                const contentStr = typeof msg.content === 'string' ? msg.content : String(msg.content);
                if (contentStr.trim() === '') return null;
                renderedSomething = true;
                
                // Check if it's a command message
                const commandMatch = contentStr.match(/<command-name>(.+?)<\/command-name>[\s\S]*?<command-message>(.+?)<\/command-message>[\s\S]*?<command-args>(.*?)<\/command-args>/);
                if (commandMatch) {
                  const [, commandName, commandMessage, commandArgs] = commandMatch;
                  return (
                    <CommandWidget 
                      commandName={commandName.trim()} 
                      commandMessage={commandMessage.trim()}
                      commandArgs={commandArgs?.trim()}
                    />
                  );
                }
                
                // Check if it's command output
                const stdoutMatch = contentStr.match(/<local-command-stdout>([\s\S]*?)<\/local-command-stdout>/);
                if (stdoutMatch) {
                  const [, output] = stdoutMatch;
                  return <CommandOutputWidget output={output} onLinkDetected={onLinkDetected} />;
                }
                
                // Otherwise render as plain text
                return (
                  <div className="text-sm">
                    {contentStr}
                  </div>
                );
              })()
            )}

            {/* Handle content that is an array of parts */}
            {Array.isArray(msg.content) && msg.content.map((content: any, idx: number) => {
              // Tool result
              if (content.type === "tool_result") {
                // Skip duplicate tool_result if a dedicated widget is present
                let hasCorrespondingWidget = false;
                if (content.tool_use_id && streamMessages) {
                  for (let i = streamMessages.length - 1; i >= 0; i--) {
                    const prevMsg = streamMessages[i];
                    if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                      const toolUse = prevMsg.message.content.find((c: any) => c.type === 'tool_use' && c.id === content.tool_use_id);
                      if (toolUse) {
                        const toolName = toolUse.name?.toLowerCase();
                        const toolsWithWidgets = ['task','edit','multiedit','todowrite','todoread','ls','read','glob','bash','write','grep','websearch','webfetch'];
                        if (toolsWithWidgets.includes(toolName) || toolUse.name?.startsWith('mcp__')) {
                          hasCorrespondingWidget = true;
                        }
                        break;
                      }
                    }
                  }
                }

                if (hasCorrespondingWidget) {
                  return null;
                }

                // Extract the actual content string
                let contentText = '';
                if (typeof content.content === 'string') {
                  contentText = content.content;
                } else if (content.content && typeof content.content === 'object') {
                  // Handle object with text property
                  if (content.content.text) {
                    contentText = content.content.text;
                  } else if (Array.isArray(content.content)) {
                    // Handle array of content blocks
                    contentText = content.content
                      .map((c: any) => (typeof c === 'string' ? c : c.text || JSON.stringify(c)))
                      .join('\n');
                  } else {
                    // Fallback to JSON stringify
                    contentText = JSON.stringify(content.content, null, 2);
                  }
                }
                
                // Always show system reminders regardless of widget status
                const reminderMatch = contentText.match(/<system-reminder>(.*?)<\/system-reminder>/s);
                if (reminderMatch) {
                  const reminderMessage = reminderMatch[1].trim();
                  const beforeReminder = contentText.substring(0, reminderMatch.index || 0).trim();
                  const afterReminder = contentText.substring((reminderMatch.index || 0) + reminderMatch[0].length).trim();
                  
                  renderedSomething = true;
                  return (
                    <div key={idx} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">Tool Result</span>
                      </div>
                      
                      {beforeReminder && (
                        <div className="ml-6 p-2 bg-background rounded-md border">
                          <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                            {beforeReminder}
                          </pre>
                        </div>
                      )}
                      
                      <div className="ml-6">
                        <SystemReminderWidget message={reminderMessage} />
                      </div>
                      
                      {afterReminder && (
                        <div className="ml-6 p-2 bg-background rounded-md border">
                          <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                            {afterReminder}
                          </pre>
                        </div>
                      )}
                    </div>
                  );
                }
                
                // Check if this is an Edit tool result
                const isEditResult = contentText.includes("has been updated. Here's the result of running `cat -n`");
                if (isEditResult) {
                  renderedSomething = true;
                  return (
                    <div key={idx} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">Edit Result</span>
                      </div>
                      <EditResultWidget content={contentText} />
                    </div>
                  );
                }
                
                // Check if this is a MultiEdit tool result
                const isMultiEditResult = contentText.includes("has been updated with multiple edits") || 
                                         contentText.includes("MultiEdit completed successfully") ||
                                         contentText.includes("Applied multiple edits to");
                if (isMultiEditResult) {
                  renderedSomething = true;
                  return (
                    <div key={idx} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">MultiEdit Result</span>
                      </div>
                      <MultiEditResultWidget content={contentText} />
                    </div>
                  );
                }
                
                // Check if this is an LS tool result (directory tree structure)
                const isLSResult = (() => {
                  if (!content.tool_use_id || typeof contentText !== 'string') return false;
                  
                  // Check if this result came from an LS tool by looking for the tool call
                  let isFromLSTool = false;
                  
                  // Search in previous assistant messages for the matching tool_use
                  if (streamMessages) {
                    for (let i = streamMessages.length - 1; i >= 0; i--) {
                      const prevMsg = streamMessages[i];
                      // Only check assistant messages
                      if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                        const toolUse = prevMsg.message.content.find((c: any) => 
                          c.type === 'tool_use' && 
                          c.id === content.tool_use_id &&
                          c.name?.toLowerCase() === 'ls'
                        );
                        if (toolUse) {
                          isFromLSTool = true;
                          break;
                        }
                      }
                    }
                  }
                  
                  if (!isFromLSTool) return false;
                  
                  // Additional validation: check for tree structure pattern
                  const lines = contentText.split('\n');
                  const hasTreeStructure = lines.some(line => /^\s*-\s+/.test(line));
                  const hasNoteAtEnd = lines.some(line => line.trim().startsWith('NOTE: do any of the files'));
                  
                  return hasTreeStructure || hasNoteAtEnd;
                })();
                
                if (isLSResult) {
                  renderedSomething = true;
                  return (
                    <div key={idx} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">Directory Contents</span>
                      </div>
                      <LSResultWidget content={contentText} />
                    </div>
                  );
                }
                
                // Check if this is a Read tool result (contains line numbers with arrow separator)
                const isReadResult = content.tool_use_id && typeof contentText === 'string' && 
                  /^\s*\d+→/.test(contentText);
                
                if (isReadResult) {
                  // Try to find the corresponding Read tool call to get the file path
                  let filePath: string | undefined;
                  
                  // Search in previous assistant messages for the matching tool_use
                  if (streamMessages) {
                    for (let i = streamMessages.length - 1; i >= 0; i--) {
                      const prevMsg = streamMessages[i];
                      if (prevMsg.type === 'assistant' && prevMsg.message?.content && Array.isArray(prevMsg.message.content)) {
                        const toolUse = prevMsg.message.content.find((c: any) => 
                          c.type === 'tool_use' && 
                          c.id === content.tool_use_id &&
                          c.name?.toLowerCase() === 'read'
                        );
                        if (toolUse?.input?.file_path) {
                          filePath = toolUse.input.file_path;
                          break;
                        }
                      }
                    }
                  }
                  
                  renderedSomething = true;
                  return (
                    <div key={idx} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">Read Result</span>
                      </div>
                      <ReadResultWidget content={contentText} filePath={filePath} />
                    </div>
                  );
                }
                
                // Handle empty tool results
                if (!contentText || contentText.trim() === '') {
                  renderedSomething = true;
                  return (
                    <div key={idx} className="space-y-2">
                      <div className="flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">Tool Result</span>
                      </div>
                      <div className="ml-6 p-3 bg-muted/50 rounded-md border text-sm text-muted-foreground italic">
                        Tool did not return any output
                      </div>
                    </div>
                  );
                }
                
                renderedSomething = true;
                return (
                  <div key={idx} className="space-y-2">
                    <div className="flex items-center gap-2">
                      {content.is_error ? (
                        <AlertCircle className="h-4 w-4 text-destructive" />
                      ) : (
                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                      )}
                      <span className="text-sm font-medium">Tool Result</span>
                    </div>
                    <div className="ml-6 p-2 bg-background rounded-md border">
                      <pre className="text-xs font-mono overflow-x-auto whitespace-pre-wrap">
                        {contentText}
                      </pre>
                    </div>
                  </div>
                );
              }
              
              // Text content
              if (content.type === "text") {
                // Handle both string and object formats
                const textContent = typeof content.text === 'string' 
                  ? content.text 
                  : (content.text?.text || JSON.stringify(content.text));
                
                renderedSomething = true;
                return (
                  <div key={idx} className="text-sm">
                    {textContent}
                  </div>
                );
              }
              
              return null;
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (!renderedSomething) return null;
  return renderedCard;
};