import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  SuperClaudeSettings,
  defaultSuperClaudeSettings,
  saveSettings,
  loadSettings,
  applyPreset,
  estimateTokenUsage,
  exportSettings,
  importSettings,
} from '@/lib/superClaude';
import {
  Brain,
  Zap,
  Shield,
  Code,
  Server,
  Users,
  ClipboardList,
  Terminal,
  FileText,
  Settings2,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Download,
  Upload,
  Info,
  Gauge,
  DollarSign,
  Bot,
  GitBranch,
  Search,
  Globe,
  Database,
  Palette,
  Lock,
  Cpu,
  Save,
  RefreshCw,
} from 'lucide-react';

interface SuperClaudeSettingsProps {
  onSettingsChange?: (settings: SuperClaudeSettings) => void;
  className?: string;
}

export const SuperClaudeSettingsComponent: React.FC<SuperClaudeSettingsProps> = ({ 
  onSettingsChange, 
  className 
}: SuperClaudeSettingsProps) => {
  const [settings, setSettings] = useState<SuperClaudeSettings>(defaultSuperClaudeSettings);
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('quick');
  const [importError, setImportError] = useState<string | null>(null);

  // Load settings on mount
  useEffect(() => {
    const loaded = loadSettings();
    setSettings(loaded);
    onSettingsChange?.(loaded);
  }, []);

  // Save and notify on settings change
  const updateSettings = (updates: Partial<SuperClaudeSettings>) => {
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    saveSettings(newSettings);
    onSettingsChange?.(newSettings);
  };

  // Handle preset selection
  const handlePresetSelect = (preset: 'efficient' | 'thorough' | 'creative' | 'secure') => {
    const presetSettings = applyPreset(preset);
    setSettings(presetSettings);
    saveSettings(presetSettings);
    onSettingsChange?.(presetSettings);
  };

  // Calculate token usage
  const tokenUsage = estimateTokenUsage(settings);

  // Handle import/export
  const handleExport = () => {
    const json = exportSettings(settings);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'superclaude-settings.json';
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      const imported = importSettings(content);
      if (imported) {
        setSettings(imported);
        saveSettings(imported);
        onSettingsChange?.(imported);
        setImportError(null);
      } else {
        setImportError('Invalid settings file');
      }
    };
    reader.readAsText(file);
  };

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <CardTitle>SuperClaude Settings</CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronUp /> : <ChevronDown />}
            </Button>
          </div>
          <CardDescription>
            Configure advanced AI capabilities and optimizations
          </CardDescription>
          
          {/* Token Usage Indicator */}
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-1">
                <Gauge className="h-3 w-3" />
                Token Efficiency
              </span>
              <span className="text-muted-foreground">
                {Math.round((2 - tokenUsage.estimatedCostMultiplier) * 50)}%
              </span>
            </div>
            <Progress 
              value={Math.round((2 - tokenUsage.estimatedCostMultiplier) * 50)} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Input: {tokenUsage.inputMultiplier.toFixed(1)}x</span>
              <span>Output: {tokenUsage.outputMultiplier.toFixed(1)}x</span>
              <span className="flex items-center gap-1">
                <DollarSign className="h-3 w-3" />
                Cost: {tokenUsage.estimatedCostMultiplier.toFixed(1)}x
              </span>
            </div>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-6 w-full">
                <TabsTrigger value="quick">Quick</TabsTrigger>
                <TabsTrigger value="core">Core</TabsTrigger>
                <TabsTrigger value="mcp">MCP</TabsTrigger>
                <TabsTrigger value="thinking">Thinking</TabsTrigger>
                <TabsTrigger value="output">Output</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>

              {/* Quick Presets Tab */}
              <TabsContent value="quick" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium">Quick Presets</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <Card 
                      className="cursor-pointer hover:bg-accent"
                      onClick={() => handlePresetSelect('efficient')}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-yellow-500" />
                          <CardTitle className="text-sm">Efficient Mode</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="text-xs text-muted-foreground">
                        Optimized for speed and token usage. Minimal explanations.
                      </CardContent>
                    </Card>

                    <Card 
                      className="cursor-pointer hover:bg-accent"
                      onClick={() => handlePresetSelect('thorough')}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Brain className="h-4 w-4 text-blue-500" />
                          <CardTitle className="text-sm">Thorough Mode</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="text-xs text-muted-foreground">
                        Deep analysis with exhaustive reasoning and planning.
                      </CardContent>
                    </Card>

                    <Card 
                      className="cursor-pointer hover:bg-accent"
                      onClick={() => handlePresetSelect('creative')}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Palette className="h-4 w-4 text-purple-500" />
                          <CardTitle className="text-sm">Creative Mode</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="text-xs text-muted-foreground">
                        Enhanced creativity with all MCP servers and collaboration.
                      </CardContent>
                    </Card>

                    <Card 
                      className="cursor-pointer hover:bg-accent"
                      onClick={() => handlePresetSelect('secure')}
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          <CardTitle className="text-sm">Secure Mode</CardTitle>
                        </div>
                      </CardHeader>
                      <CardContent className="text-xs text-muted-foreground">
                        Maximum security with OWASP compliance and scanning.
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleExport}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-3 w-3" />
                      Export Settings
                    </Button>
                    <div className="relative">
                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImport}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <Upload className="h-3 w-3" />
                        Import Settings
                      </Button>
                    </div>
                    {importError && (
                      <span className="text-xs text-destructive">{importError}</span>
                    )}
                  </div>
                </div>
              </TabsContent>

              {/* Core Modes Tab */}
              <TabsContent value="core" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    Core Modes
                  </h4>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="introspection" className="text-sm font-medium">
                          Introspection Mode
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Deep self-analysis and reasoning transparency
                        </p>
                      </div>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center gap-2">
                            <Info className="h-3 w-3 text-muted-foreground" />
                            <Switch
                              id="introspection"
                              checked={settings.coreModes.introspection}
                              onCheckedChange={(checked) =>
                                updateSettings({
                                  coreModes: { ...settings.coreModes, introspection: checked },
                                })
                              }
                            />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs text-xs">
                            Enables detailed reasoning traces and self-reflection. 
                            Increases token usage by ~30%.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="ultraCompressed" className="text-sm font-medium">
                          Ultra Compressed Mode
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Minimal tokens, maximum efficiency
                        </p>
                      </div>
                      <Switch
                        id="ultraCompressed"
                        checked={settings.coreModes.ultraCompressed}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            coreModes: { ...settings.coreModes, ultraCompressed: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="tokenEconomy" className="text-sm font-medium">
                          Token Economy
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Smart token usage optimization
                        </p>
                      </div>
                      <Switch
                        id="tokenEconomy"
                        checked={settings.coreModes.tokenEconomy}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            coreModes: { ...settings.coreModes, tokenEconomy: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label htmlFor="costOptimization" className="text-sm font-medium">
                          Cost Optimization
                        </Label>
                        <p className="text-xs text-muted-foreground">
                          Minimize API costs intelligently
                        </p>
                      </div>
                      <Switch
                        id="costOptimization"
                        checked={settings.coreModes.costOptimization}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            coreModes: { ...settings.coreModes, costOptimization: checked },
                          })
                        }
                      />
                    </div>
                  </div>

                  <Separator />

                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Bot className="h-4 w-4" />
                    Personas
                  </h4>

                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-sm">Active Persona</Label>
                      <RadioGroup
                        value={settings.personas.active}
                        onValueChange={(value) =>
                          updateSettings({
                            personas: { ...settings.personas, active: value as any },
                          })
                        }
                      >
                        <div className="grid grid-cols-2 gap-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="default" id="default" />
                            <Label htmlFor="default" className="text-xs">Default</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="architect" id="architect" />
                            <Label htmlFor="architect" className="text-xs">Architect</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="debugger" id="debugger" />
                            <Label htmlFor="debugger" className="text-xs">Debugger</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="reviewer" id="reviewer" />
                            <Label htmlFor="reviewer" className="text-xs">Reviewer</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="researcher" id="researcher" />
                            <Label htmlFor="researcher" className="text-xs">Researcher</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="optimizer" id="optimizer" />
                            <Label htmlFor="optimizer" className="text-xs">Optimizer</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="documenter" id="documenter" />
                            <Label htmlFor="documenter" className="text-xs">Documenter</Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="autoSwitch" className="text-sm">
                        Auto-switch Personas
                      </Label>
                      <Switch
                        id="autoSwitch"
                        checked={settings.personas.autoSwitch}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            personas: { ...settings.personas, autoSwitch: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="collaboration" className="text-sm">
                        Collaboration Mode
                      </Label>
                      <Switch
                        id="collaboration"
                        checked={settings.personas.collaborationMode}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            personas: { ...settings.personas, collaborationMode: checked },
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* MCP Servers Tab */}
              <TabsContent value="mcp" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Server className="h-4 w-4" />
                    Model Context Protocol Servers
                  </h4>
                  
                  <div className="grid grid-cols-2 gap-3">
                    {Object.entries(settings.mcpServers).map(([server, enabled]) => (
                      <div key={server} className="flex items-center justify-between">
                        <Label htmlFor={server} className="text-sm capitalize">
                          {server.replace(/_/g, ' ')}
                        </Label>
                        <Switch
                          id={server}
                          checked={enabled}
                          onCheckedChange={(checked) =>
                            updateSettings({
                              mcpServers: { ...settings.mcpServers, [server]: checked },
                            })
                          }
                        />
                      </div>
                    ))}
                  </div>

                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <p className="text-xs text-muted-foreground">
                      Active servers: {Object.values(settings.mcpServers).filter(Boolean).length}/
                      {Object.keys(settings.mcpServers).length}
                    </p>
                  </div>
                </div>
              </TabsContent>

              {/* Thinking Depth Tab */}
              <TabsContent value="thinking" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    Thinking Depth & Reasoning
                  </h4>

                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-sm">Reasoning Level</Label>
                      <RadioGroup
                        value={settings.thinkingDepth.level}
                        onValueChange={(value) =>
                          updateSettings({
                            thinkingDepth: { 
                              ...settings.thinkingDepth, 
                              level: value as any 
                            },
                          })
                        }
                      >
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="minimal" id="minimal" />
                            <Label htmlFor="minimal" className="text-xs">
                              Minimal - Quick responses, basic reasoning
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="standard" id="standard" />
                            <Label htmlFor="standard" className="text-xs">
                              Standard - Balanced reasoning and speed
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="deep" id="deep" />
                            <Label htmlFor="deep" className="text-xs">
                              Deep - Thorough analysis and consideration
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="exhaustive" id="exhaustive" />
                            <Label htmlFor="exhaustive" className="text-xs">
                              Exhaustive - Maximum depth and exploration
                            </Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    <Separator />

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="chainOfThought" className="text-sm">
                            Chain of Thought
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Step-by-step reasoning process
                          </p>
                        </div>
                        <Switch
                          id="chainOfThought"
                          checked={settings.thinkingDepth.chainOfThought}
                          onCheckedChange={(checked) =>
                            updateSettings({
                              thinkingDepth: { 
                                ...settings.thinkingDepth, 
                                chainOfThought: checked 
                              },
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="multiStep" className="text-sm">
                            Multi-step Reasoning
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Complex problem decomposition
                          </p>
                        </div>
                        <Switch
                          id="multiStep"
                          checked={settings.thinkingDepth.multiStepReasoning}
                          onCheckedChange={(checked) =>
                            updateSettings({
                              thinkingDepth: { 
                                ...settings.thinkingDepth, 
                                multiStepReasoning: checked 
                              },
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="selfCorrection" className="text-sm">
                            Self-correction
                          </Label>
                          <p className="text-xs text-muted-foreground">
                            Automatic error detection and fixing
                          </p>
                        </div>
                        <Switch
                          id="selfCorrection"
                          checked={settings.thinkingDepth.selfCorrection}
                          onCheckedChange={(checked) =>
                            updateSettings({
                              thinkingDepth: { 
                                ...settings.thinkingDepth, 
                                selfCorrection: checked 
                              },
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <ClipboardList className="h-4 w-4" />
                    Planning & Task Management
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="planning" className="text-sm">
                        Enable Planning
                      </Label>
                      <Switch
                        id="planning"
                        checked={settings.planning.enabled}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            planning: { ...settings.planning, enabled: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="autoPlanning" className="text-sm">
                        Auto-planning
                      </Label>
                      <Switch
                        id="autoPlanning"
                        checked={settings.planning.autoPlanning}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            planning: { ...settings.planning, autoPlanning: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="taskBreakdown" className="text-sm">
                        Task Breakdown
                      </Label>
                      <Switch
                        id="taskBreakdown"
                        checked={settings.planning.taskBreakdown}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            planning: { ...settings.planning, taskBreakdown: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="milestones" className="text-sm">
                        Milestone Tracking
                      </Label>
                      <Switch
                        id="milestones"
                        checked={settings.planning.milestoneTracking}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            planning: { ...settings.planning, milestoneTracking: checked },
                          })
                        }
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Output Tab */}
              <TabsContent value="output" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Output Preferences
                  </h4>

                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-sm">Output Format</Label>
                      <RadioGroup
                        value={settings.output.format}
                        onValueChange={(value) =>
                          updateSettings({
                            output: { ...settings.output, format: value as any },
                          })
                        }
                      >
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="minimal" id="out-minimal" />
                            <Label htmlFor="out-minimal" className="text-xs">
                              Minimal - Essential information only
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="compressed" id="out-compressed" />
                            <Label htmlFor="out-compressed" className="text-xs">
                              Compressed - Concise but complete
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="standard" id="out-standard" />
                            <Label htmlFor="out-standard" className="text-xs">
                              Standard - Balanced detail
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="verbose" id="out-verbose" />
                            <Label htmlFor="out-verbose" className="text-xs">
                              Verbose - Maximum detail
                            </Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <Label className="text-sm">Code Comments</Label>
                      <RadioGroup
                        value={settings.output.codeComments}
                        onValueChange={(value) =>
                          updateSettings({
                            output: { ...settings.output, codeComments: value as any },
                          })
                        }
                      >
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="minimal" id="com-minimal" />
                            <Label htmlFor="com-minimal" className="text-xs">
                              Minimal - Essential comments only
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="standard" id="com-standard" />
                            <Label htmlFor="com-standard" className="text-xs">
                              Standard - Key functions documented
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="detailed" id="com-detailed" />
                            <Label htmlFor="com-detailed" className="text-xs">
                              Detailed - Comprehensive documentation
                            </Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    <Separator />

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="explanations" className="text-sm">
                          Include Explanations
                        </Label>
                        <Switch
                          id="explanations"
                          checked={settings.output.explanations}
                          onCheckedChange={(checked) =>
                            updateSettings({
                              output: { ...settings.output, explanations: checked },
                            })
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="tokenTracking" className="text-sm">
                          Track Token Usage
                        </Label>
                        <Switch
                          id="tokenTracking"
                          checked={settings.output.tokenUsageTracking}
                          onCheckedChange={(checked) =>
                            updateSettings({
                              output: { ...settings.output, tokenUsageTracking: checked },
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Advanced Tab */}
              <TabsContent value="advanced" className="space-y-4">
                <div className="space-y-4">
                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Security & Quality
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="owasp" className="text-sm">
                        OWASP Compliance
                      </Label>
                      <Switch
                        id="owasp"
                        checked={settings.security.owaspCompliance}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            security: { ...settings.security, owaspCompliance: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="codeReview" className="text-sm">
                        Automatic Code Review
                      </Label>
                      <Switch
                        id="codeReview"
                        checked={settings.security.codeReview}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            security: { ...settings.security, codeReview: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="vulnScan" className="text-sm">
                        Vulnerability Scanning
                      </Label>
                      <Switch
                        id="vulnScan"
                        checked={settings.security.vulnerabilityScanning}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            security: { ...settings.security, vulnerabilityScanning: checked },
                          })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">Input Sanitization</Label>
                      <RadioGroup
                        value={settings.security.sanitization}
                        onValueChange={(value) =>
                          updateSettings({
                            security: { ...settings.security, sanitization: value as any },
                          })
                        }
                      >
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="relaxed" id="san-relaxed" />
                            <Label htmlFor="san-relaxed" className="text-xs">Relaxed</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="standard" id="san-standard" />
                            <Label htmlFor="san-standard" className="text-xs">Standard</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="strict" id="san-strict" />
                            <Label htmlFor="san-strict" className="text-xs">Strict</Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>

                  <Separator />

                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Cpu className="h-4 w-4" />
                    Performance & Execution
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="caching" className="text-sm">
                        Enable Caching
                      </Label>
                      <Switch
                        id="caching"
                        checked={settings.performance.caching}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            performance: { ...settings.performance, caching: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="lazy" className="text-sm">
                        Lazy Loading
                      </Label>
                      <Switch
                        id="lazy"
                        checked={settings.performance.lazyLoading}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            performance: { ...settings.performance, lazyLoading: checked },
                          })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="batchSize" className="text-sm">
                        Batch Size: {settings.performance.batchSize}
                      </Label>
                      <Input
                        id="batchSize"
                        type="range"
                        min="1"
                        max="50"
                        value={settings.performance.batchSize}
                        onChange={(e) =>
                          updateSettings({
                            performance: { 
                              ...settings.performance, 
                              batchSize: parseInt(e.target.value) 
                            },
                          })
                        }
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="parallelism" className="text-sm">
                        Parallelism: {settings.performance.parallelism}
                      </Label>
                      <Input
                        id="parallelism"
                        type="range"
                        min="1"
                        max="16"
                        value={settings.performance.parallelism}
                        onChange={(e) =>
                          updateSettings({
                            performance: { 
                              ...settings.performance, 
                              parallelism: parseInt(e.target.value) 
                            },
                          })
                        }
                        className="w-full"
                      />
                    </div>
                  </div>

                  <Separator />

                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Terminal className="h-4 w-4" />
                    Commands & Workflows
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="batch" className="text-sm">
                        Batch Operations
                      </Label>
                      <Switch
                        id="batch"
                        checked={settings.commands.batchOperations}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            commands: { ...settings.commands, batchOperations: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="parallel" className="text-sm">
                        Parallel Execution
                      </Label>
                      <Switch
                        id="parallel"
                        checked={settings.commands.parallelExecution}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            commands: { ...settings.commands, parallelExecution: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="retry" className="text-sm">
                        Auto-retry Failed Commands
                      </Label>
                      <Switch
                        id="retry"
                        checked={settings.commands.autoRetry}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            commands: { ...settings.commands, autoRetry: checked },
                          })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="recovery" className="text-sm">
                        Error Recovery
                      </Label>
                      <Switch
                        id="recovery"
                        checked={settings.commands.errorRecovery}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            commands: { ...settings.commands, errorRecovery: checked },
                          })
                        }
                      />
                    </div>
                  </div>

                  <Separator />

                  <h4 className="text-sm font-medium flex items-center gap-2">
                    <Save className="h-4 w-4" />
                    Session Management
                  </h4>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="autoSave" className="text-sm">
                        Auto-save Session
                      </Label>
                      <Switch
                        id="autoSave"
                        checked={settings.session.autoSave}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            session: { ...settings.session, autoSave: checked },
                          })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">Context Retention</Label>
                      <RadioGroup
                        value={settings.session.contextRetention}
                        onValueChange={(value) =>
                          updateSettings({
                            session: { ...settings.session, contextRetention: value as any },
                          })
                        }
                      >
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="minimal" id="ctx-minimal" />
                            <Label htmlFor="ctx-minimal" className="text-xs">
                              Minimal - Essential context only
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="smart" id="ctx-smart" />
                            <Label htmlFor="ctx-smart" className="text-xs">
                              Smart - Intelligent pruning
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="full" id="ctx-full" />
                            <Label htmlFor="ctx-full" className="text-xs">
                              Full - Keep everything
                            </Label>
                          </div>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="historySize" className="text-sm">
                        History Size: {settings.session.historySize}
                      </Label>
                      <Input
                        id="historySize"
                        type="range"
                        min="10"
                        max="500"
                        step="10"
                        value={settings.session.historySize}
                        onChange={(e) =>
                          updateSettings({
                            session: { 
                              ...settings.session, 
                              historySize: parseInt(e.target.value) 
                            },
                          })
                        }
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Current Configuration Badge */}
            <div className="mt-4 flex items-center gap-2 flex-wrap">
              <Badge variant="secondary" className="text-xs">
                {settings.preset || 'custom'}
              </Badge>
              {settings.coreModes.introspection && (
                <Badge variant="outline" className="text-xs">
                  Introspection
                </Badge>
              )}
              {settings.coreModes.ultraCompressed && (
                <Badge variant="outline" className="text-xs">
                  Ultra Compressed
                </Badge>
              )}
              {settings.personas.collaborationMode && (
                <Badge variant="outline" className="text-xs">
                  Collaboration
                </Badge>
              )}
              {settings.security.vulnerabilityScanning && (
                <Badge variant="outline" className="text-xs">
                  Security Scan
                </Badge>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </TooltipProvider>
  );
};

// Default export
export default SuperClaudeSettingsComponent;