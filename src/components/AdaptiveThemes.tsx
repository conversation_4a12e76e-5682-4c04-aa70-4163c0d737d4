import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  MapPin, 
  Zap, 
  Eye,
  Palette,
  Activity
} from 'lucide-react';

interface AdaptiveThemeConfig {
  enabled: boolean;
  timeBasedThemes: boolean;
  locationBasedThemes: boolean;
  activityBasedThemes: boolean;
  workingHours: {
    start: string;
    end: string;
  };
  themes: {
    morning: string;
    afternoon: string;
    evening: string;
    night: string;
    coding: string;
    reviewing: string;
    debugging: string;
  };
}

interface ContextualThemeData {
  currentTime: Date;
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
  isWorkingHours: boolean;
  currentActivity: 'coding' | 'reviewing' | 'debugging' | 'idle';
  location?: {
    timezone: string;
    isDaytime: boolean;
  };
}

export function AdaptiveThemes() {
  const [config, setConfig] = useState<AdaptiveThemeConfig>({
    enabled: false,
    timeBasedThemes: true,
    locationBasedThemes: false,
    activityBasedThemes: true,
    workingHours: {
      start: '09:00',
      end: '17:00'
    },
    themes: {
      morning: 'light',
      afternoon: 'light',
      evening: 'dark',
      night: 'midnight',
      coding: 'forest',
      reviewing: 'ocean',
      debugging: 'sunset'
    }
  });

  const [contextData, setContextData] = useState<ContextualThemeData>({
    currentTime: new Date(),
    timeOfDay: 'morning',
    isWorkingHours: true,
    currentActivity: 'idle'
  });

  const [currentTheme, setCurrentTheme] = useState<string>('default');
  const [themeReason, setThemeReason] = useState<string>('Manual selection');

  // Update context data every minute
  useEffect(() => {
    const updateContext = () => {
      const now = new Date();
      const hour = now.getHours();
      
      let timeOfDay: ContextualThemeData['timeOfDay'] = 'morning';
      if (hour >= 6 && hour < 12) timeOfDay = 'morning';
      else if (hour >= 12 && hour < 17) timeOfDay = 'afternoon';
      else if (hour >= 17 && hour < 21) timeOfDay = 'evening';
      else timeOfDay = 'night';

      const workStart = parseInt(config.workingHours.start.split(':')[0]);
      const workEnd = parseInt(config.workingHours.end.split(':')[0]);
      const isWorkingHours = hour >= workStart && hour < workEnd;

      setContextData(prev => ({
        ...prev,
        currentTime: now,
        timeOfDay,
        isWorkingHours,
        location: {
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          isDaytime: hour >= 6 && hour < 18
        }
      }));
    };

    updateContext();
    const interval = setInterval(updateContext, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [config.workingHours]);

  // Apply adaptive theme based on context
  useEffect(() => {
    if (!config.enabled) return;

    let selectedTheme = 'default';
    let reason = 'Default theme';

    // Priority: Activity > Time > Location
    if (config.activityBasedThemes && contextData.currentActivity !== 'idle') {
      selectedTheme = config.themes[contextData.currentActivity];
      reason = `Activity-based: ${contextData.currentActivity}`;
    } else if (config.timeBasedThemes) {
      selectedTheme = config.themes[contextData.timeOfDay];
      reason = `Time-based: ${contextData.timeOfDay}`;
    } else if (config.locationBasedThemes && contextData.location) {
      selectedTheme = contextData.location.isDaytime ? 'light' : 'dark';
      reason = `Location-based: ${contextData.location.isDaytime ? 'daytime' : 'nighttime'}`;
    }

    setCurrentTheme(selectedTheme);
    setThemeReason(reason);

    // Apply theme to document
    document.documentElement.className = selectedTheme;
  }, [config, contextData]);

  const simulateActivity = (activity: ContextualThemeData['currentActivity']) => {
    setContextData(prev => ({ ...prev, currentActivity: activity }));
    setTimeout(() => {
      setContextData(prev => ({ ...prev, currentActivity: 'idle' }));
    }, 5000); // Reset after 5 seconds for demo
  };

  const getThemePreview = (themeName: string) => {
    const themeColors = {
      light: { bg: '#ffffff', text: '#000000', accent: '#3b82f6' },
      dark: { bg: '#1f2937', text: '#ffffff', accent: '#60a5fa' },
      midnight: { bg: '#1e1b4b', text: '#e0e7ff', accent: '#8b5cf6' },
      forest: { bg: '#f0fdf4', text: '#166534', accent: '#10b981' },
      ocean: { bg: '#f0f9ff', text: '#0c4a6e', accent: '#0ea5e9' },
      sunset: { bg: '#fff7ed', text: '#9a3412', accent: '#f59e0b' }
    };
    return themeColors[themeName as keyof typeof themeColors] || themeColors.light;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Adaptive Themes
            <Badge variant={config.enabled ? "default" : "secondary"}>
              {config.enabled ? "Active" : "Inactive"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Main Toggle */}
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="adaptive-enabled" className="text-base font-medium">
                Enable Adaptive Themes
              </Label>
              <p className="text-sm text-muted-foreground">
                Automatically change themes based on time, context, and activity
              </p>
            </div>
            <Switch
              id="adaptive-enabled"
              checked={config.enabled}
              onCheckedChange={(enabled) => setConfig(prev => ({ ...prev, enabled }))}
            />
          </div>

          {config.enabled && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="space-y-4"
            >
              {/* Current Status */}
              <Card className="bg-muted/50">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium">Current Theme</h4>
                    <div 
                      className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                      style={{ backgroundColor: getThemePreview(currentTheme).accent }}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="w-4 h-4" />
                      <span>{contextData.currentTime.toLocaleTimeString()}</span>
                      <Badge variant="outline">{contextData.timeOfDay}</Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Activity className="w-4 h-4" />
                      <span>Activity: {contextData.currentActivity}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Reason: {themeReason}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Adaptation Settings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span className="font-medium">Time-based</span>
                      </div>
                      <Switch
                        checked={config.timeBasedThemes}
                        onCheckedChange={(checked) => 
                          setConfig(prev => ({ ...prev, timeBasedThemes: checked }))
                        }
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Change themes based on time of day
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        <span className="font-medium">Location-based</span>
                      </div>
                      <Switch
                        checked={config.locationBasedThemes}
                        onCheckedChange={(checked) => 
                          setConfig(prev => ({ ...prev, locationBasedThemes: checked }))
                        }
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Adapt to your timezone and daylight
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4" />
                        <span className="font-medium">Activity-based</span>
                      </div>
                      <Switch
                        checked={config.activityBasedThemes}
                        onCheckedChange={(checked) => 
                          setConfig(prev => ({ ...prev, activityBasedThemes: checked }))
                        }
                      />
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Switch themes based on what you're doing
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Demo Activity Buttons */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Test Activity Detection</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => simulateActivity('coding')}
                      className="flex items-center gap-2"
                    >
                      <Eye className="w-4 h-4" />
                      Simulate Coding
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => simulateActivity('reviewing')}
                      className="flex items-center gap-2"
                    >
                      <Eye className="w-4 h-4" />
                      Simulate Review
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => simulateActivity('debugging')}
                      className="flex items-center gap-2"
                    >
                      <Zap className="w-4 h-4" />
                      Simulate Debug
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Click to simulate different activities and see theme changes
                  </p>
                </CardContent>
              </Card>

              {/* Theme Mappings Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Theme Mappings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {Object.entries(config.themes).map(([context, theme]) => {
                      const preview = getThemePreview(theme);
                      return (
                        <div key={context} className="text-center">
                          <div 
                            className="w-full h-12 rounded-lg border-2 border-white shadow-sm mb-2"
                            style={{ 
                              backgroundColor: preview.bg,
                              border: `2px solid ${preview.accent}`
                            }}
                          />
                          <div className="text-xs font-medium capitalize">{context}</div>
                          <div className="text-xs text-muted-foreground">{theme}</div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}