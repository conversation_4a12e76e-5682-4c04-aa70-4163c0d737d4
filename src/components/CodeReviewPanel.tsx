import { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { api } from '@/lib/api';
import { 
  FileCode2, 
  AlertCircle, 
  CheckCircle2, 
  Info, 
  Lightbulb,
  Play,
  Loader2,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface CodeReviewResult {
  file: string;
  issues: ReviewIssue[];
  suggestions: ReviewSuggestion[];
  metrics: ReviewMetrics;
}

interface ReviewIssue {
  severity: 'error' | 'warning' | 'info';
  line: number;
  message: string;
  rule?: string;
}

interface ReviewSuggestion {
  type: 'performance' | 'security' | 'best-practice' | 'refactor';
  description: string;
  code?: string;
}

interface ReviewMetrics {
  complexity: number;
  maintainability: number;
  coverage?: number;
  duplicateLines?: number;
}

export function CodeReviewPanel({ sessionId }: { sessionId: string }) {
  const [reviewResults, setReviewResults] = useState<CodeReviewResult[]>([]);
  const [isReviewing, setIsReviewing] = useState(false);
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set());
  const [customRules, setCustomRules] = useState('');
  const [activeTab, setActiveTab] = useState('issues');

  const runCodeReview = useCallback(async () => {
    setIsReviewing(true);
    try {
      const results = await api.runCodeReview(sessionId, customRules);
      setReviewResults(results);
      if (results.length > 0) {
        setExpandedFiles(new Set(results.map(r => r.file)));
      }
    } catch (error) {
      console.error('Code review failed:', error);
    } finally {
      setIsReviewing(false);
    }
  }, [sessionId, customRules]);

  const toggleFileExpansion = (file: string) => {
    const newExpanded = new Set(expandedFiles);
    if (newExpanded.has(file)) {
      newExpanded.delete(file);
    } else {
      newExpanded.add(file);
    }
    setExpandedFiles(newExpanded);
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <CheckCircle2 className="w-4 h-4 text-green-500" />;
    }
  };

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'performance':
        return <Lightbulb className="w-4 h-4 text-orange-500" />;
      case 'security':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'best-practice':
        return <CheckCircle2 className="w-4 h-4 text-green-500" />;
      case 'refactor':
        return <FileCode2 className="w-4 h-4 text-purple-500" />;
      default:
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileCode2 className="w-5 h-5" />
              AI Code Review
            </CardTitle>
            <Button 
              onClick={runCodeReview} 
              disabled={isReviewing}
              size="sm"
            >
              {isReviewing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Reviewing...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Run Review
                </>
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="issues">Issues</TabsTrigger>
              <TabsTrigger value="suggestions">Suggestions</TabsTrigger>
              <TabsTrigger value="metrics">Metrics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="issues" className="flex-1 overflow-auto">
              <div className="space-y-2">
                {reviewResults.map((result) => (
                  <div key={result.file} className="border rounded-lg">
                    <button
                      onClick={() => toggleFileExpansion(result.file)}
                      className="w-full flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-900"
                    >
                      <div className="flex items-center gap-2">
                        {expandedFiles.has(result.file) ? 
                          <ChevronDown className="w-4 h-4" /> : 
                          <ChevronRight className="w-4 h-4" />
                        }
                        <FileCode2 className="w-4 h-4" />
                        <span className="text-sm font-medium">{result.file}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          {result.issues.filter(i => i.severity === 'error').length} errors
                        </Badge>
                        <Badge variant="secondary">
                          {result.issues.filter(i => i.severity === 'warning').length} warnings
                        </Badge>
                      </div>
                    </button>
                    
                    {expandedFiles.has(result.file) && (
                      <div className="border-t p-3 space-y-2">
                        {result.issues.map((issue, idx) => (
                          <div key={idx} className="flex items-start gap-2 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-900">
                            {getSeverityIcon(issue.severity)}
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <span className="text-sm font-medium">Line {issue.line}</span>
                                {issue.rule && (
                                  <Badge variant="outline" className="text-xs">
                                    {issue.rule}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {issue.message}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="suggestions" className="flex-1 overflow-auto">
              <div className="space-y-4">
                {reviewResults.map((result) => (
                  <div key={result.file}>
                    <h4 className="text-sm font-medium mb-2">{result.file}</h4>
                    <div className="space-y-2">
                      {result.suggestions.map((suggestion, idx) => (
                        <div key={idx} className="border rounded-lg p-3">
                          <div className="flex items-start gap-2">
                            {getSuggestionIcon(suggestion.type)}
                            <div className="flex-1">
                              <Badge variant="outline" className="mb-1">
                                {suggestion.type}
                              </Badge>
                              <p className="text-sm mt-1">{suggestion.description}</p>
                              {suggestion.code && (
                                <pre className="mt-2 p-2 bg-gray-100 dark:bg-gray-900 rounded text-xs overflow-x-auto">
                                  <code>{suggestion.code}</code>
                                </pre>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="metrics" className="flex-1 overflow-auto">
              <div className="space-y-4">
                {reviewResults.map((result) => (
                  <Card key={result.file}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">{result.file}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs text-gray-500">Complexity</p>
                          <p className="text-2xl font-bold">{result.metrics.complexity}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Maintainability</p>
                          <p className="text-2xl font-bold">{result.metrics.maintainability}%</p>
                        </div>
                        {result.metrics.coverage !== undefined && (
                          <div>
                            <p className="text-xs text-gray-500">Coverage</p>
                            <p className="text-2xl font-bold">{result.metrics.coverage}%</p>
                          </div>
                        )}
                        {result.metrics.duplicateLines !== undefined && (
                          <div>
                            <p className="text-xs text-gray-500">Duplicate Lines</p>
                            <p className="text-2xl font-bold">{result.metrics.duplicateLines}</p>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="mt-4 pt-4 border-t">
            <label className="text-sm font-medium">Custom Review Rules</label>
            <Textarea
              value={customRules}
              onChange={(e) => setCustomRules(e.target.value)}
              placeholder="Add custom review rules or focus areas..."
              className="mt-1"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}