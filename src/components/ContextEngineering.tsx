import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from './ui/card';
import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from './ui/tabs';
import { 
  Brain, 
  Zap, 
  Network, 
  ArrowLeft, 
  Sparkles, 
  Bot, 
  Code2, 
  Target, 
  Layers, 
  GitBranch, 
  Settings, 
  BookOpen, 
  Lightbulb, 
  Play, 
  FileText, 
  Database, 
  Workflow, 
  Atom,
  Beaker,
  Cpu,
  Eye,
  Filter,
  Share2,
  ChevronRight,
  ExternalLink
} from 'lucide-react';

interface ContextEngineeringProps {
  onBack: () => void;
}

interface CognitiveToolTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  template: string;
  icon: React.ElementType;
  difficulty: 'basic' | 'intermediate' | 'advanced';
  tags: string[];
}

interface ContextField {
  id: string;
  name: string;
  description: string;
  type: 'memory' | 'reasoning' | 'tool' | 'protocol';
  active: boolean;
  resonance: number;
}

const cognitiveTools: CognitiveToolTemplate[] = [
  {
    id: 'understanding',
    name: 'Deep Understanding',
    category: 'comprehension',
    description: 'Systematic approach to understanding complex topics',
    template: `# Deep Understanding Framework

## Context Analysis
- **Domain**: [Specify the domain/field]
- **Scope**: [Define the boundaries of understanding]
- **Prerequisites**: [List required background knowledge]

## Structured Exploration
1. **Core Concepts**: What are the fundamental building blocks?
2. **Relationships**: How do these concepts interconnect?
3. **Patterns**: What recurring themes or structures emerge?
4. **Contradictions**: What seems paradoxical or conflicting?

## Mental Models
- **Analogies**: What familiar concepts can we map this to?
- **Visualizations**: How can we represent this spatially?
- **Hierarchies**: What are the levels of abstraction?

## Verification
- **Examples**: Can you provide concrete instances?
- **Counter-examples**: What would violate these principles?
- **Predictions**: What should we expect based on this understanding?`,
    icon: Brain,
    difficulty: 'basic',
    tags: ['comprehension', 'analysis', 'mental-models']
  },
  {
    id: 'reasoning',
    name: 'Chain of Reasoning',
    category: 'analysis',
    description: 'Step-by-step logical reasoning with verification',
    template: `# Chain of Reasoning Framework

## Problem Definition
- **Question**: [State the problem clearly]
- **Context**: [Provide relevant background]
- **Constraints**: [List limitations or assumptions]

## Reasoning Chain
1. **Premise 1**: [State first logical step]
   - Evidence: [Supporting information]
   - Confidence: [High/Medium/Low]

2. **Premise 2**: [State second logical step]
   - Evidence: [Supporting information]
   - Confidence: [High/Medium/Low]

3. **Intermediate Conclusion**: [Logical deduction]
   - Reasoning: [Explain the logical connection]

## Verification Steps
- **Internal Consistency**: Do all steps follow logically?
- **External Validation**: Do conclusions match known facts?
- **Alternative Paths**: Are there other valid reasoning routes?

## Final Conclusion
- **Answer**: [Clear, definitive response]
- **Confidence Level**: [Overall confidence assessment]
- **Limitations**: [Known gaps or uncertainties]`,
    icon: GitBranch,
    difficulty: 'intermediate',
    tags: ['logic', 'reasoning', 'verification']
  },
  {
    id: 'field-protocol',
    name: 'Context Field Protocol',
    category: 'advanced',
    description: 'Neural field theory for context orchestration',
    template: `# Context Field Protocol

## Field Initialization
- **Field Type**: [Memory/Reasoning/Tool/Protocol]
- **Resonance Frequency**: [Activation threshold]
- **Boundary Conditions**: [Field limits and constraints]

## Field Dynamics
\`\`\`
Field State = {
  active_contexts: [],
  resonance_level: 0.0,
  field_strength: 1.0,
  interference_patterns: [],
  attractor_states: []
}
\`\`\`

## Attractor Formation
1. **Seed Patterns**: [Initial context nuclei]
2. **Reinforcement**: [Feedback loops]
3. **Stability**: [Self-maintaining structures]

## Field Interactions
- **Constructive Interference**: [Amplifying patterns]
- **Destructive Interference**: [Canceling patterns]
- **Phase Transitions**: [State changes]

## Emergence Detection
- **Threshold Monitoring**: [Activation levels]
- **Pattern Recognition**: [Emergent structures]
- **Symbolic Residue**: [Persistent traces]

## Field Orchestration
- **Multi-field Coordination**: [Parallel processing]
- **Hierarchical Organization**: [Nested structures]
- **Dynamic Adaptation**: [Real-time adjustment]`,
    icon: Network,
    difficulty: 'advanced',
    tags: ['field-theory', 'orchestration', 'emergence']
  },
  {
    id: 'cognitive-architecture',
    name: 'Cognitive Architecture',
    category: 'system',
    description: 'Complete reasoning system architecture',
    template: `# Cognitive Architecture Framework

## Architecture Overview
\`\`\`
Cognitive System = {
  perception_layer: {},
  working_memory: {},
  long_term_memory: {},
  reasoning_engine: {},
  metacognition: {}
}
\`\`\`

## Layer Specifications

### Perception Layer
- **Input Processing**: [Sensory data handling]
- **Pattern Recognition**: [Feature extraction]
- **Attention Mechanisms**: [Focus allocation]

### Working Memory
- **Capacity**: [7±2 items]
- **Maintenance**: [Active rehearsal]
- **Manipulation**: [Cognitive operations]

### Long-term Memory
- **Declarative**: [Facts and events]
- **Procedural**: [Skills and habits]
- **Episodic**: [Personal experiences]

### Reasoning Engine
- **Deductive**: [Logical inference]
- **Inductive**: [Pattern generalization]
- **Abductive**: [Best explanation]

### Metacognition
- **Monitoring**: [Self-awareness]
- **Control**: [Strategic regulation]
- **Evaluation**: [Performance assessment]

## Integration Patterns
- **Bottom-up**: [Data-driven processing]
- **Top-down**: [Goal-driven processing]
- **Feedback Loops**: [Recursive refinement]`,
    icon: Cpu,
    difficulty: 'advanced',
    tags: ['architecture', 'system-design', 'cognition']
  }
];

const contextFields: ContextField[] = [
  {
    id: 'memory-field',
    name: 'Memory Field',
    description: 'Persistent context and state management',
    type: 'memory',
    active: true,
    resonance: 0.8
  },
  {
    id: 'reasoning-field', 
    name: 'Reasoning Field',
    description: 'Logical inference and problem-solving',
    type: 'reasoning',
    active: true,
    resonance: 0.9
  },
  {
    id: 'tool-field',
    name: 'Tool Field',
    description: 'External tool integration and orchestration',
    type: 'tool',
    active: false,
    resonance: 0.3
  },
  {
    id: 'protocol-field',
    name: 'Protocol Field',
    description: 'Meta-level coordination and emergence',
    type: 'protocol',
    active: true,
    resonance: 0.7
  }
];

export default function ContextEngineering({ onBack }: ContextEngineeringProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTool, setSelectedTool] = useState<CognitiveToolTemplate | null>(null);
  const [fields, setFields] = useState<ContextField[]>(contextFields);
  const [showTemplatePreview, setShowTemplatePreview] = useState(false);

  const toggleField = (fieldId: string) => {
    setFields(fields.map(field => 
      field.id === fieldId 
        ? { ...field, active: !field.active }
        : field
    ));
  };

  const getFieldColor = (type: string) => {
    switch (type) {
      case 'memory': return 'bg-blue-500/10 border-blue-500/20 text-blue-600';
      case 'reasoning': return 'bg-purple-500/10 border-purple-500/20 text-purple-600';
      case 'tool': return 'bg-green-500/10 border-green-500/20 text-green-600';
      case 'protocol': return 'bg-amber-500/10 border-amber-500/20 text-amber-600';
      default: return 'bg-gray-500/10 border-gray-500/20 text-gray-600';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'basic': return 'bg-green-500/10 text-green-600';
      case 'intermediate': return 'bg-yellow-500/10 text-yellow-600';
      case 'advanced': return 'bg-red-500/10 text-red-600';
      default: return 'bg-gray-500/10 text-gray-600';
    }
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <motion.div 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between p-6 border-b bg-card/50 backdrop-blur-sm"
      >
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Home
          </Button>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
              <Brain className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Context Engineering
              </h1>
              <p className="text-sm text-muted-foreground">
                Advanced AI context design and orchestration
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-0">
            <Sparkles className="w-3 h-3 mr-1" />
            v2.0.1
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://github.com/davidkimai/Context-Engineering', '_blank')}
            className="flex items-center gap-2"
          >
            <ExternalLink className="w-4 h-4" />
            Original Repository
          </Button>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-5 m-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="tools" className="flex items-center gap-2">
              <Beaker className="w-4 h-4" />
              Cognitive Tools
            </TabsTrigger>
            <TabsTrigger value="fields" className="flex items-center gap-2">
              <Network className="w-4 h-4" />
              Context Fields
            </TabsTrigger>
            <TabsTrigger value="protocols" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Protocols
            </TabsTrigger>
            <TabsTrigger value="examples" className="flex items-center gap-2">
              <Play className="w-4 h-4" />
              Examples
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-auto p-4 space-y-6">
            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border-2 border-blue-200/50 dark:border-blue-800/50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-3 text-2xl">
                      <Brain className="w-8 h-8 text-blue-600" />
                      Context Engineering Framework
                    </CardTitle>
                    <CardDescription className="text-lg">
                      "Context engineering is the delicate art and science of filling the context window with just the right information for the next step." — Andrej Karpathy
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold mb-3">Biological Progression</h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center gap-2">
                            <Atom className="w-4 h-4 text-blue-500" />
                            <span><strong>Atoms:</strong> Single instructions</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Layers className="w-4 h-4 text-green-500" />
                            <span><strong>Molecules:</strong> Few-shot examples</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Database className="w-4 h-4 text-purple-500" />
                            <span><strong>Cells:</strong> Memory + state</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Workflow className="w-4 h-4 text-amber-500" />
                            <span><strong>Organs:</strong> Multi-agent systems</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Network className="w-4 h-4 text-red-500" />
                            <span><strong>Neural Fields:</strong> Continuous meaning</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold mb-3">Key Principles</h3>
                        <ul className="space-y-2 text-sm">
                          <li className="flex items-start gap-2">
                            <ChevronRight className="w-4 h-4 mt-0.5 text-blue-500" />
                            <span>Field theory for context orchestration</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <ChevronRight className="w-4 h-4 mt-0.5 text-green-500" />
                            <span>Attractor dynamics and resonance</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <ChevronRight className="w-4 h-4 mt-0.5 text-purple-500" />
                            <span>Symbolic residue tracking</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <ChevronRight className="w-4 h-4 mt-0.5 text-amber-500" />
                            <span>Emergent cognitive patterns</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Beaker className="w-5 h-5 text-green-500" />
                        Cognitive Tools
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="mb-4">
                        {cognitiveTools.length} advanced reasoning templates and frameworks
                      </CardDescription>
                      <Button 
                        onClick={() => setActiveTab('tools')}
                        className="w-full"
                      >
                        Explore Tools
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Network className="w-5 h-5 text-blue-500" />
                        Context Fields
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="mb-4">
                        {fields.filter(f => f.active).length} active fields with dynamic resonance
                      </CardDescription>
                      <Button 
                        onClick={() => setActiveTab('fields')}
                        className="w-full"
                      >
                        Manage Fields
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="w-5 h-5 text-purple-500" />
                        Protocol Shells
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="mb-4">
                        Advanced protocol orchestration and emergence detection
                      </CardDescription>
                      <Button 
                        onClick={() => setActiveTab('protocols')}
                        className="w-full"
                      >
                        View Protocols
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>
            </TabsContent>

            {/* Cognitive Tools Tab */}
            <TabsContent value="tools" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {cognitiveTools.map((tool, index) => (
                  <motion.div
                    key={tool.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card 
                      className="cursor-pointer hover:shadow-lg transition-all duration-300 hover:scale-105 h-full"
                      onClick={() => setSelectedTool(tool)}
                    >
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                              <tool.icon className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <CardTitle className="text-lg">{tool.name}</CardTitle>
                              <Badge 
                                variant="secondary" 
                                className={getDifficultyColor(tool.difficulty)}
                              >
                                {tool.difficulty}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="mb-3">
                          {tool.description}
                        </CardDescription>
                        <div className="flex flex-wrap gap-1">
                          {tool.tags.map(tag => (
                            <Badge 
                              key={tag}
                              variant="outline"
                              className="text-xs"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Tool Preview Modal */}
              <AnimatePresence>
                {selectedTool && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
                    onClick={() => setSelectedTool(null)}
                  >
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.9, opacity: 0 }}
                      className="bg-background border rounded-lg p-6 max-w-4xl w-full max-h-[80vh] overflow-auto"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <selectedTool.icon className="w-6 h-6 text-blue-600" />
                          <h2 className="text-2xl font-bold">{selectedTool.name}</h2>
                        </div>
                        <Button
                          variant="ghost"
                          onClick={() => setSelectedTool(null)}
                        >
                          ×
                        </Button>
                      </div>
                      <div className="prose dark:prose-invert max-w-none">
                        <pre className="whitespace-pre-wrap text-sm bg-muted p-4 rounded-lg">
                          {selectedTool.template}
                        </pre>
                      </div>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </TabsContent>

            {/* Context Fields Tab */}
            <TabsContent value="fields" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {fields.map((field, index) => (
                  <motion.div
                    key={field.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className={`${getFieldColor(field.type)} border-2 transition-all duration-300`}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${field.active ? 'bg-green-500' : 'bg-gray-500'}`}>
                              <Network className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <CardTitle className="text-lg">{field.name}</CardTitle>
                              <Badge variant="outline" className="text-xs">
                                {field.type}
                              </Badge>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleField(field.id)}
                          >
                            {field.active ? 'Disable' : 'Enable'}
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="mb-3">
                          {field.description}
                        </CardDescription>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Resonance Level:</span>
                            <span className="font-mono">{field.resonance.toFixed(2)}</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${field.resonance * 100}%` }}
                            />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </TabsContent>

            {/* Protocols Tab */}
            <TabsContent value="protocols" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5" />
                      Protocol Shells
                    </CardTitle>
                    <CardDescription>
                      Advanced protocol orchestration and emergence detection systems
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <h3 className="font-semibold">Available Protocols</h3>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 p-2 bg-muted rounded">
                            <Zap className="w-4 h-4 text-amber-500" />
                            <span className="text-sm">Attractor Co-emergence</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-muted rounded">
                            <Filter className="w-4 h-4 text-blue-500" />
                            <span className="text-sm">Recursive Memory</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-muted rounded">
                            <Share2 className="w-4 h-4 text-green-500" />
                            <span className="text-sm">Field Resonance</span>
                          </div>
                          <div className="flex items-center gap-2 p-2 bg-muted rounded">
                            <Target className="w-4 h-4 text-purple-500" />
                            <span className="text-sm">Self-repair Loop</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <h3 className="font-semibold">Protocol Status</h3>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-950/20 rounded">
                            <span className="text-sm">Memory Persistence</span>
                            <Badge variant="secondary" className="bg-green-500/20 text-green-600">
                              Active
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-blue-50 dark:bg-blue-950/20 rounded">
                            <span className="text-sm">Field Orchestration</span>
                            <Badge variant="secondary" className="bg-blue-500/20 text-blue-600">
                              Active
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-950/20 rounded">
                            <span className="text-sm">Emergence Detection</span>
                            <Badge variant="secondary" className="bg-gray-500/20 text-gray-600">
                              Standby
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            {/* Examples Tab */}
            <TabsContent value="examples" className="space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Play className="w-5 h-5" />
                      Interactive Examples
                    </CardTitle>
                    <CardDescription>
                      Hands-on demonstrations of context engineering principles
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button 
                        variant="outline" 
                        className="h-20 flex flex-col items-center justify-center gap-2"
                      >
                        <Code2 className="w-6 h-6" />
                        <span>Minimal Context Example</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-20 flex flex-col items-center justify-center gap-2"
                      >
                        <Workflow className="w-6 h-6" />
                        <span>Field Protocol Demo</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-20 flex flex-col items-center justify-center gap-2"
                      >
                        <Network className="w-6 h-6" />
                        <span>Attractor Visualization</span>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="h-20 flex flex-col items-center justify-center gap-2"
                      >
                        <Bot className="w-6 h-6" />
                        <span>Multi-Agent Context</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}