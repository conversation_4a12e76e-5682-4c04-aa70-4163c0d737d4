import { motion, AnimatePresence } from 'framer-motion';
import { Focus, Minimize2, Check, Settings, Plus, X, Edit2, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { useFocusMode } from '@/hooks/useFocusMode';
import { cn } from '@/lib/utils';
import { useState } from 'react';

export function FocusModeToggle() {
  const { 
    enabled, 
    toggleFocusMode, 
    applyPreset, 
    activePreset, 
    presets,
    saveCustomPreset,
    deleteCustomPreset
  } = useFocusMode();

  const [showCustomizeDialog, setShowCustomizeDialog] = useState(false);
  const [editingPreset, setEditingPreset] = useState<string | null>(null);
  const [presetForm, setPresetForm] = useState({
    name: '',
    description: '',
    features: [] as string[]
  });

  const availableFeatures = [
    'projects', 'agents', 'templates', 'performance', 'plugins', 
    'mcp', 'usage-dashboard', 'settings', 'agent-system'
  ];

  const handleCreatePreset = () => {
    setEditingPreset(null);
    setPresetForm({
      name: '',
      description: '',
      features: []
    });
    setShowCustomizeDialog(true);
  };

  const handleEditPreset = (preset: any) => {
    setEditingPreset(preset.id);
    setPresetForm({
      name: preset.name,
      description: preset.description,
      features: preset.features
    });
    setShowCustomizeDialog(true);
  };

  const handleSavePreset = () => {
    if (!presetForm.name.trim()) return;

    const preset = {
      id: editingPreset || `custom-${Date.now()}`,
      name: presetForm.name,
      description: presetForm.description,
      features: presetForm.features,
      isCustom: true
    };

    saveCustomPreset(preset);
    setShowCustomizeDialog(false);
    setEditingPreset(null);
  };

  const handleDeletePreset = (presetId: string) => {
    deleteCustomPreset(presetId);
  };

  const toggleFeature = (feature: string) => {
    setPresetForm(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }));
  };

  return (
    <TooltipProvider>
      <div className="flex items-center gap-2">
        {/* Focus Mode Toggle Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={enabled ? "default" : "outline"}
              size="sm"
              onClick={toggleFocusMode}
              className={cn(
                "relative overflow-hidden transition-all duration-300",
                enabled && "bg-primary text-primary-foreground"
              )}
            >
              <AnimatePresence mode="wait">
                {enabled ? (
                  <motion.div
                    key="focused"
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.5, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center gap-2"
                  >
                    <Focus className="w-4 h-4" />
                    <span className="text-sm font-medium">Focus Mode</span>
                  </motion.div>
                ) : (
                  <motion.div
                    key="normal"
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.5, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center gap-2"
                  >
                    <Minimize2 className="w-4 h-4" />
                    <span className="text-sm font-medium">Focus</span>
                  </motion.div>
                )}
              </AnimatePresence>
              
              {/* Active indicator */}
              {enabled && (
                <motion.div
                  className="absolute inset-0 bg-white/20"
                  initial={{ x: '-100%' }}
                  animate={{ x: '100%' }}
                  transition={{
                    repeat: Infinity,
                    duration: 3,
                    ease: "linear"
                  }}
                />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{enabled ? 'Exit focus mode' : 'Enter focus mode'}</p>
          </TooltipContent>
        </Tooltip>

        {/* Preset Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2"
              disabled={!enabled}
            >
              {activePreset ? (
                <Badge variant="secondary" className="text-xs">
                  {presets.find(p => p.id === activePreset)?.name}
                </Badge>
              ) : (
                <span className="text-xs text-muted-foreground">Presets</span>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[200px]">
            <DropdownMenuLabel>Focus Presets</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
{presets.map((preset) => (
              <DropdownMenuItem
                key={preset.id}
                onClick={() => applyPreset(preset.id)}
                className="flex items-center justify-between group"
              >
                <div className="flex flex-col flex-1">
                  <span className="font-medium">{preset.name}</span>
                  <span className="text-xs text-muted-foreground">
                    {preset.description}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  {activePreset === preset.id && (
                    <Check className="w-4 h-4 text-primary" />
                  )}
                  {preset.isCustom && (
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditPreset(preset);
                        }}
                      >
                        <Edit2 className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePreset(preset.id);
                        }}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </DropdownMenuItem>
            ))}
            
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={handleCreatePreset}
              className="text-xs"
            >
              <Plus className="w-3 h-3 mr-2" />
              Create custom preset...
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Customize Presets Dialog */}
      <Dialog open={showCustomizeDialog} onOpenChange={setShowCustomizeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingPreset ? 'Edit Preset' : 'Create Custom Preset'}
            </DialogTitle>
            <DialogDescription>
              {editingPreset ? 'Modify your existing preset settings' : 'Create a new focus mode preset with your preferred features'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="preset-name">Name</Label>
              <Input
                id="preset-name"
                value={presetForm.name}
                onChange={(e) => setPresetForm(prev => ({...prev, name: e.target.value}))}
                placeholder="Enter preset name"
              />
            </div>
            
            <div>
              <Label htmlFor="preset-description">Description</Label>
              <Textarea
                id="preset-description"
                value={presetForm.description}
                onChange={(e) => setPresetForm(prev => ({...prev, description: e.target.value}))}
                placeholder="Describe when to use this preset"
                rows={2}
              />
            </div>
            
            <div>
              <Label>Features to Show</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {availableFeatures.map(feature => (
                  <Card
                    key={feature}
                    className={cn(
                      "p-3 cursor-pointer transition-all hover:bg-accent",
                      presetForm.features.includes(feature) && "border-primary bg-primary/5"
                    )}
                    onClick={() => toggleFeature(feature)}
                  >
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "w-4 h-4 rounded-sm border-2 flex items-center justify-center",
                        presetForm.features.includes(feature) 
                          ? "bg-primary border-primary" 
                          : "border-muted-foreground"
                      )}>
                        {presetForm.features.includes(feature) && (
                          <Check className="w-3 h-3 text-primary-foreground" />
                        )}
                      </div>
                      <span className="text-sm capitalize">{feature.replace('-', ' ')}</span>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 mt-6">
            <Button 
              variant="outline" 
              onClick={() => setShowCustomizeDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSavePreset}
              disabled={!presetForm.name.trim() || presetForm.features.length === 0}
            >
              {editingPreset ? 'Save Changes' : 'Create Preset'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
}