import React, { useState, useEffect, useRef } from 'react';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { api } from '@/lib/api';
import { motion } from 'framer-motion';
import { 
  Bot, 
  Brain, 
  Code2, 
  Search, 
  Shield, 
  Zap, 
  MessageSquare,
  Play,
  Settings,
  Plus,
  Edit3,
  RefreshCw,
  Clock,
  CheckCircle,
  Star,
  Database,
  Globe,
  FileText,
  Camera,
  Terminal,
  Target,
  Users,
  Layers,
  Cpu,
  Network,
  Eye,
  BarChart3,
  TrendingUp,
  Sparkles,
  Workflow,
  Rocket,
  Heart
} from 'lucide-react';

interface AgentSystemProps {
  sessionId: string;
  projectPath: string;
  onClose?: () => void;
}

interface Agent {
  id: string;
  name: string;
  type: AgentType;
  description: string;
  capabilities: string[];
  status: AgentStatus;
  avatar: string;
  personality: AgentPersonality;
  metrics: AgentMetrics;
  configuration: AgentConfiguration;
  isActive: boolean;
  createdAt: Date;
  lastUsed: Date;
  version: string;
  specialty: string[];
  // Additional properties needed for compatibility
  tasksCompleted: number;
  accuracy: number;
  lastActive: Date;
  memory: any;
  settings: any;
}

interface AgentMetrics {
  tasksCompleted: number;
  successRate: number;
  averageResponseTime: number;
  userSatisfaction: number;
  tokensUsed: number;
  costEfficiency: number;
  learningProgress: number;
  collaborationScore: number;
}

interface AgentConfiguration {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  tools: string[];
  permissions: AgentPermissions;
  autonomyLevel: number;
  memorySize: number;
  learningEnabled: boolean;
}

interface AgentPermissions {
  canReadFiles: boolean;
  canWriteFiles: boolean;
  canExecuteCode: boolean;
  canAccessInternet: boolean;
  canModifySystem: boolean;
  canCollaborate: boolean;
  canLearn: boolean;
  maxCostPerTask: number;
}

interface AgentPersonality {
  communication_style: 'formal' | 'casual' | 'technical' | 'friendly' | 'concise';
  expertise_level: 'beginner' | 'intermediate' | 'expert' | 'master';
  response_speed: 'fast' | 'balanced' | 'thorough';
  creativity_level: number; // 0-100
  detail_orientation: number; // 0-100
  collaboration_preference: 'independent' | 'cooperative' | 'leader' | 'follower';
}

interface AgentTask {
  id: string;
  agentId: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: TaskPriority;
  progress: number;
  startTime: Date;
  estimatedCompletion: Date;
  actualCompletion?: Date;
  result?: string;
  feedback?: AgentFeedback;
  dependencies: string[];
  tags: string[];
  // Additional properties needed for compatibility
  createdAt: Date;
  subtasks: any[];
}

interface AgentFeedback {
  rating: number;
  comments: string;
  improvements: string[];
  timestamp: Date;
}

interface AgentConversation {
  id: string;
  agentId: string;
  messages: AgentMessage[];
  context: string;
  startTime: Date;
  isActive: boolean;
}

interface AgentMessage {
  id: string;
  role: 'system' | 'agent' | 'user';
  content: string;
  timestamp: Date;
  metadata?: {
    tokensUsed?: number;
    responseTime?: number;
    confidence?: number;
    sources?: string[];
  };
}

type AgentType = 
  | 'coding_expert'
  | 'code_reviewer'
  | 'security_analyst'
  | 'performance_optimizer'
  | 'test_engineer'
  | 'documentation_writer'
  | 'architect'
  | 'devops_engineer'
  | 'data_analyst'
  | 'ui_ux_designer'
  | 'project_manager'
  | 'researcher'
  | 'debugger'
  | 'mentor'
  | 'translator'
  | 'api_specialist'
  | 'database_expert'
  | 'mobile_developer'
  | 'web3_specialist'
  | 'ai_ml_engineer'
  | 'quality_assurance'
  | 'business_analyst'
  | 'technical_writer'
  | 'system_integrator';

type AgentStatus = 'idle' | 'working' | 'paused' | 'error' | 'learning' | 'collaborating';
type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled';
type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

const agentTypeData: Record<AgentType, {
  icon: any;
  color: string;
  defaultCapabilities: string[];
  systemPrompt: string;
  specialty: string[];
}> = {
  coding_expert: {
    icon: Code2,
    color: '#3B82F6',
    defaultCapabilities: ['code_generation', 'refactoring', 'optimization', 'debugging'],
    systemPrompt: 'You are an expert software engineer with deep knowledge across multiple programming languages and frameworks. You write clean, efficient, and maintainable code while following best practices.',
    specialty: ['algorithms', 'data_structures', 'design_patterns', 'clean_code']
  },
  code_reviewer: {
    icon: Search,
    color: '#10B981',
    defaultCapabilities: ['code_analysis', 'security_review', 'performance_review', 'best_practices'],
    systemPrompt: 'You are a senior code reviewer with expertise in identifying bugs, security vulnerabilities, performance issues, and code quality improvements.',
    specialty: ['static_analysis', 'security', 'performance', 'maintainability']
  },
  security_analyst: {
    icon: Shield,
    color: '#EF4444',
    defaultCapabilities: ['vulnerability_scanning', 'penetration_testing', 'compliance_check', 'threat_modeling'],
    systemPrompt: 'You are a cybersecurity expert specializing in application security, vulnerability assessment, and secure coding practices.',
    specialty: ['owasp', 'penetration_testing', 'compliance', 'encryption']
  },
  performance_optimizer: {
    icon: Zap,
    color: '#F59E0B',
    defaultCapabilities: ['performance_analysis', 'bottleneck_detection', 'optimization', 'monitoring'],
    systemPrompt: 'You are a performance optimization specialist focused on improving application speed, efficiency, and resource utilization.',
    specialty: ['profiling', 'caching', 'database_optimization', 'load_testing']
  },
  test_engineer: {
    icon: CheckCircle,
    color: '#8B5CF6',
    defaultCapabilities: ['test_planning', 'test_automation', 'quality_assurance', 'bug_tracking'],
    systemPrompt: 'You are a quality assurance engineer specializing in comprehensive testing strategies, automation, and ensuring software reliability.',
    specialty: ['unit_testing', 'integration_testing', 'e2e_testing', 'test_automation']
  },
  documentation_writer: {
    icon: FileText,
    color: '#06B6D4',
    defaultCapabilities: ['technical_writing', 'api_documentation', 'user_guides', 'knowledge_management'],
    systemPrompt: 'You are a technical writer who creates clear, comprehensive, and user-friendly documentation for software projects.',
    specialty: ['api_docs', 'user_manuals', 'tutorials', 'knowledge_base']
  },
  architect: {
    icon: Layers,
    color: '#EC4899',
    defaultCapabilities: ['system_design', 'architecture_review', 'scalability_planning', 'technology_selection'],
    systemPrompt: 'You are a software architect with expertise in designing scalable, maintainable, and robust software systems.',
    specialty: ['microservices', 'distributed_systems', 'cloud_architecture', 'scalability']
  },
  devops_engineer: {
    icon: Terminal,
    color: '#84CC16',
    defaultCapabilities: ['ci_cd', 'infrastructure_automation', 'monitoring', 'deployment'],
    systemPrompt: 'You are a DevOps engineer specializing in automation, infrastructure as code, and continuous integration/deployment.',
    specialty: ['docker', 'kubernetes', 'terraform', 'ci_cd_pipelines']
  },
  data_analyst: {
    icon: BarChart3,
    color: '#F97316',
    defaultCapabilities: ['data_analysis', 'visualization', 'reporting', 'insights'],
    systemPrompt: 'You are a data analyst who transforms raw data into actionable insights through analysis, visualization, and reporting.',
    specialty: ['sql', 'python', 'data_visualization', 'statistical_analysis']
  },
  ui_ux_designer: {
    icon: Camera,
    color: '#DB2777',
    defaultCapabilities: ['ui_design', 'ux_research', 'prototyping', 'accessibility'],
    systemPrompt: 'You are a UI/UX designer focused on creating intuitive, accessible, and visually appealing user interfaces.',
    specialty: ['design_systems', 'accessibility', 'user_research', 'prototyping']
  },
  project_manager: {
    icon: Users,
    color: '#7C3AED',
    defaultCapabilities: ['project_planning', 'resource_management', 'risk_assessment', 'stakeholder_communication'],
    systemPrompt: 'You are a project manager who coordinates development efforts, manages timelines, and ensures successful project delivery.',
    specialty: ['agile', 'scrum', 'risk_management', 'stakeholder_management']
  },
  researcher: {
    icon: Brain,
    color: '#0EA5E9',
    defaultCapabilities: ['technology_research', 'market_analysis', 'trend_identification', 'competitive_analysis'],
    systemPrompt: 'You are a technology researcher who stays current with industry trends, evaluates new technologies, and provides strategic insights.',
    specialty: ['technology_trends', 'market_research', 'competitive_analysis', 'innovation']
  },
  debugger: {
    icon: Bot,
    color: '#DC2626',
    defaultCapabilities: ['bug_detection', 'root_cause_analysis', 'debugging_assistance', 'error_tracking'],
    systemPrompt: 'You are a debugging specialist who excels at identifying, analyzing, and resolving complex software issues.',
    specialty: ['debugging_techniques', 'error_analysis', 'troubleshooting', 'performance_debugging']
  },
  mentor: {
    icon: Heart,
    color: '#059669',
    defaultCapabilities: ['code_mentoring', 'skill_development', 'best_practices_guidance', 'career_advice'],
    systemPrompt: 'You are a senior developer mentor who guides junior developers, provides constructive feedback, and promotes learning.',
    specialty: ['mentoring', 'skill_development', 'code_review', 'career_guidance']
  },
  translator: {
    icon: Globe,
    color: '#7C2D12',
    defaultCapabilities: ['code_translation', 'language_porting', 'framework_migration', 'documentation_translation'],
    systemPrompt: 'You are a code translation specialist who converts code between languages, frameworks, and platforms while maintaining functionality.',
    specialty: ['language_conversion', 'framework_migration', 'platform_porting', 'legacy_modernization']
  },
  api_specialist: {
    icon: Network,
    color: '#1D4ED8',
    defaultCapabilities: ['api_design', 'api_testing', 'integration', 'documentation'],
    systemPrompt: 'You are an API specialist focused on designing, implementing, and maintaining robust, scalable APIs.',
    specialty: ['rest_apis', 'graphql', 'api_security', 'microservices']
  },
  database_expert: {
    icon: Database,
    color: '#B45309',
    defaultCapabilities: ['database_design', 'query_optimization', 'data_modeling', 'migration'],
    systemPrompt: 'You are a database expert specializing in design, optimization, and management of database systems.',
    specialty: ['sql_optimization', 'database_design', 'data_modeling', 'performance_tuning']
  },
  mobile_developer: {
    icon: Cpu,
    color: '#BE185D',
    defaultCapabilities: ['mobile_development', 'cross_platform', 'native_optimization', 'app_store_optimization'],
    systemPrompt: 'You are a mobile development expert specializing in iOS, Android, and cross-platform mobile applications.',
    specialty: ['react_native', 'flutter', 'ios_development', 'android_development']
  },
  web3_specialist: {
    icon: Sparkles,
    color: '#7C3AED',
    defaultCapabilities: ['smart_contracts', 'blockchain_integration', 'defi_protocols', 'nft_development'],
    systemPrompt: 'You are a Web3 specialist with expertise in blockchain technology, smart contracts, and decentralized applications.',
    specialty: ['solidity', 'ethereum', 'smart_contracts', 'defi']
  },
  ai_ml_engineer: {
    icon: Workflow,
    color: '#059669',
    defaultCapabilities: ['model_development', 'data_preprocessing', 'ml_optimization', 'ai_integration'],
    systemPrompt: 'You are an AI/ML engineer specializing in machine learning model development, training, and deployment.',
    specialty: ['tensorflow', 'pytorch', 'model_optimization', 'data_science']
  },
  quality_assurance: {
    icon: Target,
    color: '#DC2626',
    defaultCapabilities: ['quality_planning', 'test_strategy', 'defect_management', 'process_improvement'],
    systemPrompt: 'You are a QA specialist focused on ensuring software quality through comprehensive testing and process improvement.',
    specialty: ['test_planning', 'quality_metrics', 'process_improvement', 'defect_analysis']
  },
  business_analyst: {
    icon: TrendingUp,
    color: '#0891B2',
    defaultCapabilities: ['requirements_analysis', 'business_process_modeling', 'stakeholder_management', 'solution_design'],
    systemPrompt: 'You are a business analyst who bridges technical and business requirements to deliver optimal solutions.',
    specialty: ['requirements_gathering', 'process_modeling', 'stakeholder_analysis', 'solution_architecture']
  },
  technical_writer: {
    icon: Edit3,
    color: '#0D9488',
    defaultCapabilities: ['technical_documentation', 'content_strategy', 'information_architecture', 'user_communication'],
    systemPrompt: 'You are a technical writer who creates clear, accurate, and user-friendly technical content.',
    specialty: ['technical_writing', 'content_strategy', 'documentation_systems', 'user_experience']
  },
  system_integrator: {
    icon: Rocket,
    color: '#7C2D12',
    defaultCapabilities: ['system_integration', 'api_orchestration', 'data_synchronization', 'workflow_automation'],
    systemPrompt: 'You are a system integration specialist who connects disparate systems and automates complex workflows.',
    specialty: ['system_integration', 'api_orchestration', 'workflow_automation', 'data_synchronization']
  }
};

export function AgentSystem({ sessionId, projectPath, onClose }: AgentSystemProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);
  // const [isConfiguring, setIsConfiguring] = useState(false);
  const [currentTask, setCurrentTask] = useState('');
  const [tasks, setTasks] = useState<AgentTask[]>([]);
  const [conversations, setConversations] = useState<AgentConversation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showMetrics, setShowMetrics] = useState(false);
  const [agentForm, setAgentForm] = useState({
    name: '',
    type: 'coding_expert' as AgentType,
    personality: {
      communication_style: 'professional' as const,
      expertise_level: 'expert' as const,
      response_speed: 'balanced' as const,
      creativity_level: 50,
      detail_orientation: 80,
      collaboration_preference: 'cooperative' as const
    },
    configuration: {
      model: 'claude-3-opus',
      temperature: 0.7,
      maxTokens: 4000,
      autonomyLevel: 60,
      memorySize: 1000,
      learningEnabled: true
    }
  });

  const chatScrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initializeAgentSystem();
  }, [sessionId]);

  const initializeAgentSystem = async () => {
    setIsLoading(true);
    try {
      // Load existing agents or create default ones
      const savedAgents = await api.getAgents(sessionId);
      if (savedAgents.length === 0) {
        const defaultAgents = createDefaultAgents();
        setAgents(defaultAgents);
        await api.saveAgents(sessionId, defaultAgents as any);
      } else {
        // Convert EnhancedAgent to Agent by adding missing properties
        const convertedAgents: Agent[] = savedAgents.map(agent => ({
          ...agent,
          type: agent.type as AgentType,
          status: agent.status as AgentStatus,
          personality: {
            ...agent.personality,
            communication_style: agent.personality.communication_style as 'formal' | 'casual' | 'technical' | 'friendly' | 'concise',
            expertise_level: agent.personality.expertise_level as 'beginner' | 'intermediate' | 'expert' | 'master',
            response_speed: agent.personality.response_speed as 'fast' | 'balanced' | 'thorough',
            collaboration_preference: agent.personality.collaboration_preference as 'independent' | 'cooperative' | 'leader' | 'follower'
          },
          tasksCompleted: agent.metrics?.tasksCompleted || 0,
          accuracy: agent.metrics?.successRate || 100,
          lastActive: new Date(agent.lastUsed),
          memory: {},
          settings: {}
        }));
        setAgents(convertedAgents);
      }
      
      // Load tasks and conversations
      const agentTasks = await api.getAgentTasks(sessionId);
      const agentConversations = await api.getAgentConversations(sessionId);
      
      // Convert EnhancedAgentTask to AgentTask
      const convertedTasks: AgentTask[] = agentTasks.map(task => ({
        ...task,
        status: task.status as TaskStatus,
        priority: task.priority as TaskPriority,
        createdAt: new Date(task.startTime),
        subtasks: []
      }));
      setTasks(convertedTasks);
      
      // Convert EnhancedAgentConversation to AgentConversation
      const convertedConversations: AgentConversation[] = agentConversations.map(conv => ({
        ...conv,
        messages: conv.messages.map(msg => ({
          ...msg,
          role: msg.role as 'system' | 'agent' | 'user'
        }))
      }));
      setConversations(convertedConversations);
    } catch (error) {
      console.error('Failed to initialize agent system:', error);
      // Use mock data for demonstration
      const defaultAgents = createDefaultAgents();
      setAgents(defaultAgents);
      setTasks(generateMockTasks());
      setConversations(generateMockConversations());
    } finally {
      setIsLoading(false);
    }
  };

  const createDefaultAgents = (): Agent[] => {
    const defaultTypes: AgentType[] = [
      'coding_expert',
      'code_reviewer', 
      'security_analyst',
      'performance_optimizer',
      'test_engineer',
      'documentation_writer',
      'architect',
      'debugger'
    ];

    return defaultTypes.map((type, index) => ({
      id: `agent-${type}-${Date.now()}-${index}`,
      name: `${type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())} Agent`,
      type,
      description: agentTypeData[type].systemPrompt.substring(0, 100) + '...',
      capabilities: agentTypeData[type].defaultCapabilities,
      status: 'idle' as AgentStatus,
      avatar: `https://api.dicebear.com/7.x/bottts/svg?seed=${type}`,
      personality: {
        communication_style: 'technical',
        expertise_level: 'expert',
        response_speed: 'balanced',
        creativity_level: 70,
        detail_orientation: 85,
        collaboration_preference: 'cooperative'
      },
      metrics: {
        tasksCompleted: Math.floor(Math.random() * 50) + 10,
        successRate: 85 + Math.random() * 15,
        averageResponseTime: 2000 + Math.random() * 3000,
        userSatisfaction: 4.2 + Math.random() * 0.8,
        tokensUsed: Math.floor(Math.random() * 100000) + 50000,
        costEfficiency: 80 + Math.random() * 20,
        learningProgress: Math.random() * 100,
        collaborationScore: 75 + Math.random() * 25
      },
      configuration: {
        model: 'claude-3-opus',
        temperature: 0.7,
        maxTokens: 4000,
        systemPrompt: agentTypeData[type].systemPrompt,
        tools: agentTypeData[type].defaultCapabilities,
        permissions: {
          canReadFiles: true,
          canWriteFiles: type === 'coding_expert' || type === 'documentation_writer',
          canExecuteCode: type === 'coding_expert' || type === 'test_engineer',
          canAccessInternet: type === 'researcher' || type === 'security_analyst',
          canModifySystem: false,
          canCollaborate: true,
          canLearn: true,
          maxCostPerTask: 5.0
        },
        autonomyLevel: 60,
        memorySize: 1000,
        learningEnabled: true
      },
      isActive: Math.random() > 0.5,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      lastUsed: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      version: '1.0.0',
      specialty: agentTypeData[type].defaultCapabilities,
      // Additional required properties
      tasksCompleted: Math.floor(Math.random() * 50) + 10,
      accuracy: 85 + Math.random() * 15,
      lastActive: new Date(),
      memory: {},
      settings: {}
    }));
  };

  const generateMockTasks = (): AgentTask[] => {
    const taskTitles = [
      'Optimize database queries for better performance',
      'Review security vulnerabilities in authentication system',
      'Write comprehensive unit tests for API endpoints',
      'Refactor legacy code to improve maintainability',
      'Create technical documentation for new features',
      'Analyze code architecture and suggest improvements',
      'Debug memory leak in React component',
      'Implement automated testing pipeline'
    ];

    return taskTitles.map((title, index) => ({
      id: `task-${Date.now()}-${index}`,
      agentId: agents[index % agents.length]?.id || 'unknown',
      title,
      description: `Detailed description for: ${title}`,
      status: ['pending', 'in_progress', 'completed'][Math.floor(Math.random() * 3)] as TaskStatus,
      priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as TaskPriority,
      progress: Math.floor(Math.random() * 100),
      startTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      estimatedCompletion: new Date(Date.now() + Math.random() * 48 * 60 * 60 * 1000),
      dependencies: [],
      tags: ['development', 'optimization', 'testing'].slice(0, Math.floor(Math.random() * 3) + 1),
      // Additional required properties
      createdAt: new Date(),
      subtasks: []
    }));
  };

  const generateMockConversations = (): AgentConversation[] => {
    return agents.slice(0, 3).map((agent, index) => ({
      id: `conv-${agent.id}-${Date.now()}`,
      agentId: agent.id,
      messages: [
        {
          id: `msg-1-${index}`,
          role: 'user',
          content: 'Can you help me optimize this React component?',
          timestamp: new Date(Date.now() - 30 * 60 * 1000)
        },
        {
          id: `msg-2-${index}`,
          role: 'agent',
          content: 'I\'d be happy to help optimize your React component. Please share the code and I\'ll analyze it for performance improvements.',
          timestamp: new Date(Date.now() - 29 * 60 * 1000),
          metadata: {
            tokensUsed: 45,
            responseTime: 1200,
            confidence: 0.95
          }
        }
      ],
      context: projectPath,
      startTime: new Date(Date.now() - 30 * 60 * 1000),
      isActive: index === 0
    }));
  };

  const handleCreateAgent = async () => {
    try {
      const newAgent: Agent = {
        id: `agent-${Date.now()}`,
        name: agentForm.name,
        type: agentForm.type,
        description: agentTypeData[agentForm.type].systemPrompt.substring(0, 100) + '...',
        capabilities: agentTypeData[agentForm.type].defaultCapabilities,
        status: 'idle',
        avatar: `https://api.dicebear.com/7.x/bottts/svg?seed=${agentForm.name}`,
        personality: {
          communication_style: 'professional' as 'formal',
          expertise_level: 'expert',
          response_speed: 'balanced',
          creativity_level: agentForm.personality.creativity_level,
          detail_orientation: agentForm.personality.detail_orientation,
          collaboration_preference: 'cooperative'
        },
        metrics: {
          tasksCompleted: 0,
          successRate: 100,
          averageResponseTime: 0,
          userSatisfaction: 5.0,
          tokensUsed: 0,
          costEfficiency: 100,
          learningProgress: 0,
          collaborationScore: 100
        },
        configuration: {
          ...agentForm.configuration,
          systemPrompt: agentTypeData[agentForm.type].systemPrompt,
          tools: agentTypeData[agentForm.type].defaultCapabilities,
          permissions: {
            canReadFiles: true,
            canWriteFiles: agentForm.type === 'coding_expert' || agentForm.type === 'documentation_writer',
            canExecuteCode: agentForm.type === 'coding_expert' || agentForm.type === 'test_engineer',
            canAccessInternet: agentForm.type === 'researcher' || agentForm.type === 'security_analyst',
            canModifySystem: false,
            canCollaborate: true,
            canLearn: agentForm.configuration.learningEnabled,
            maxCostPerTask: 5.0
          }
        },
        isActive: true,
        createdAt: new Date(),
        lastUsed: new Date(),
        version: '1.0.0',
        specialty: agentTypeData[agentForm.type].defaultCapabilities,
        // Additional required properties
        tasksCompleted: 0,
        accuracy: 100,
        lastActive: new Date(),
        memory: {},
        settings: {}
      };

      const updatedAgents = [...agents, newAgent];
      setAgents(updatedAgents);
      await api.saveAgents(sessionId, updatedAgents);
      setIsCreatingAgent(false);
      
      // Reset form
      setAgentForm({
        name: '',
        type: 'coding_expert',
        personality: {
          communication_style: 'professional',
          expertise_level: 'expert',
          response_speed: 'balanced',
          creativity_level: 50,
          detail_orientation: 80,
          collaboration_preference: 'cooperative'
        },
        configuration: {
          model: 'claude-3-opus',
          temperature: 0.7,
          maxTokens: 4000,
          autonomyLevel: 60,
          memorySize: 1000,
          learningEnabled: true
        }
      });
    } catch (error) {
      console.error('Failed to create agent:', error);
    }
  };

  const handleSendTask = async (agentId: string, task: string) => {
    if (!task.trim()) return;

    try {
      const agent = agents.find(a => a.id === agentId);
      if (!agent) return;

      // Create new task
      const newTask: AgentTask = {
        id: `task-${Date.now()}`,
        agentId,
        title: task.substring(0, 50) + (task.length > 50 ? '...' : ''),
        description: task,
        status: 'in_progress',
        priority: 'medium',
        progress: 0,
        startTime: new Date(),
        estimatedCompletion: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours
        dependencies: [],
        tags: ['user_request'],
        // Additional required properties
        createdAt: new Date(),
        subtasks: []
      };

      setTasks(prev => [...prev, newTask]);

      // Update agent status
      setAgents(prev => prev.map(a => 
        a.id === agentId 
          ? { ...a, status: 'working' as AgentStatus, lastUsed: new Date() }
          : a
      ));

      // Simulate agent response
      setTimeout(async () => {
        const response = await simulateAgentResponse(agent, task);
        
        // Update task with completion
        setTasks(prev => prev.map(t => 
          t.id === newTask.id 
            ? { ...t, status: 'completed', progress: 100, actualCompletion: new Date(), result: response }
            : t
        ));

        // Update agent status and metrics
        setAgents(prev => prev.map(a => 
          a.id === agentId 
            ? { 
                ...a, 
                status: 'idle',
                metrics: {
                  ...a.metrics,
                  tasksCompleted: a.metrics.tasksCompleted + 1,
                  tokensUsed: a.metrics.tokensUsed + Math.floor(Math.random() * 1000) + 200
                }
              }
            : a
        ));
      }, 3000 + Math.random() * 5000);

      setCurrentTask('');
    } catch (error) {
      console.error('Failed to send task to agent:', error);
    }
  };

  const simulateAgentResponse = async (agent: Agent, _task: string): Promise<string> => {
    // Simulate calling Claude API with agent's configuration
    const responses = {
      coding_expert: `I've analyzed your request and created an optimized solution. Here's the implementation with improved performance and clean code structure.`,
      code_reviewer: `I've completed the code review and identified several areas for improvement including security enhancements and performance optimizations.`,
      security_analyst: `Security analysis complete. I found potential vulnerabilities and have provided recommendations for remediation.`,
      performance_optimizer: `Performance analysis finished. I've identified bottlenecks and implemented optimizations that should improve response time by 40%.`,
      test_engineer: `Test suite has been created with comprehensive coverage. All edge cases are handled with both unit and integration tests.`,
      documentation_writer: `Documentation has been updated with clear examples, API references, and user guides for better developer experience.`,
      architect: `System architecture review complete. I've provided a scalable design with improved modularity and separation of concerns.`,
      debugger: `Bug analysis complete. I've identified the root cause and provided a fix along with prevention strategies.`
    };

    const response = responses[agent.type as keyof typeof responses];
    return response || `Task completed successfully. The solution has been implemented according to your requirements.`;
  };

  const getAgentStatusColor = (status: AgentStatus) => {
    switch (status) {
      case 'working': return 'bg-blue-500';
      case 'idle': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      case 'learning': return 'bg-purple-500';
      case 'collaborating': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getTaskStatusColor = (status: TaskStatus) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <Card className="h-full flex items-center justify-center">
        <div className="flex items-center gap-2">
          <RefreshCw className="w-6 h-6 animate-spin" />
          <span>Initializing AI Agent System...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bot className="w-5 h-5" />
                AI Agent System
              </CardTitle>
              <CardDescription>
                Intelligent agents working together to enhance your development workflow
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="gap-1">
                <Bot className="w-3 h-3" />
                {agents.filter(a => a.isActive).length} Active
              </Badge>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowMetrics(!showMetrics)}
              >
                <BarChart3 className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setIsCreatingAgent(true)}
              >
                <Plus className="w-4 h-4" />
              </Button>
              {onClose && (
                <Button variant="ghost" size="icon" onClick={onClose}>
                  <Eye className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="agents">Agents</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="chat">Chat</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="flex-1 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Agents</p>
                        <p className="text-2xl font-bold">{agents.length}</p>
                      </div>
                      <Bot className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Active Tasks</p>
                        <p className="text-2xl font-bold">{tasks.filter(t => t.status === 'in_progress').length}</p>
                      </div>
                      <Clock className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Completed Today</p>
                        <p className="text-2xl font-bold">{tasks.filter(t => t.status === 'completed').length}</p>
                      </div>
                      <CheckCircle className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                        <p className="text-2xl font-bold">
                          {Math.round(agents.reduce((acc, a) => acc + a.metrics.successRate, 0) / agents.length)}%
                        </p>
                      </div>
                      <Target className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Agent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <div className="space-y-3">
                        {agents.slice(0, 6).map((agent) => {
                          const IconComponent = agentTypeData[agent.type].icon;
                          return (
                            <div key={agent.id} className="flex items-center gap-3 p-2 rounded-lg hover:bg-accent/50">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={agent.avatar} />
                                <AvatarFallback>
                                  <IconComponent className="w-4 h-4" />
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{agent.name}</p>
                                <p className="text-xs text-muted-foreground">{agent.type.replace('_', ' ')}</p>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${getAgentStatusColor(agent.status)}`} />
                                <Badge variant="secondary" className="text-xs">
                                  {agent.metrics.tasksCompleted} tasks
                                </Badge>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recent Tasks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <div className="space-y-3">
                        {tasks.slice(0, 6).map((task) => (
                          <div key={task.id} className="p-3 rounded-lg border bg-card">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="text-sm font-medium truncate pr-2">{task.title}</h4>
                              <Badge className={`text-xs ${getTaskStatusColor(task.status)}`}>
                                {task.status}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between">
                              <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                                {task.priority}
                              </Badge>
                              <div className="text-xs text-muted-foreground">
                                {task.progress}% complete
                              </div>
                            </div>
                            <Progress value={task.progress} className="mt-2 h-1" />
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="agents" className="flex-1 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Available Agents</h3>
                  <p className="text-sm text-muted-foreground">
                    Manage and configure your AI agents
                  </p>
                </div>
                <Button onClick={() => setIsCreatingAgent(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Agent
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {agents.map((agent) => {
                  const IconComponent = agentTypeData[agent.type].icon;
                  return (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer"
                            onClick={() => setSelectedAgent(agent)}>
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3 mb-3">
                            <div className="relative">
                              <Avatar className="h-12 w-12">
                                <AvatarImage src={agent.avatar} />
                                <AvatarFallback style={{ backgroundColor: agentTypeData[agent.type].color }}>
                                  <IconComponent className="w-6 h-6 text-white" />
                                </AvatarFallback>
                              </Avatar>
                              <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-background ${getAgentStatusColor(agent.status)}`} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="font-semibold truncate">{agent.name}</h4>
                              <p className="text-sm text-muted-foreground">{agent.type.replace('_', ' ')}</p>
                              <div className="flex items-center gap-1 mt-1">
                                {agent.isActive ? (
                                  <Badge variant="default" className="text-xs">Active</Badge>
                                ) : (
                                  <Badge variant="secondary" className="text-xs">Inactive</Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                            {agent.description}
                          </p>
                          
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-xs">
                              <span>Success Rate</span>
                              <span className="font-medium">{Math.round(agent.metrics.successRate)}%</span>
                            </div>
                            <Progress value={agent.metrics.successRate} className="h-1" />
                            
                            <div className="flex items-center justify-between text-xs">
                              <span>Tasks Completed</span>
                              <span className="font-medium">{agent.metrics.tasksCompleted}</span>
                            </div>
                            
                            <div className="flex items-center justify-between text-xs">
                              <span>Satisfaction</span>
                              <div className="flex items-center gap-1">
                                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                                <span className="font-medium">{agent.metrics.userSatisfaction.toFixed(1)}</span>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center gap-1 mt-3">
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="flex-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedAgent(agent);
                                setActiveTab('chat');
                              }}
                            >
                              <MessageSquare className="w-3 h-3 mr-1" />
                              Chat
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedAgent(agent);
                                // setIsConfiguring(true); // TODO: Implement configuration dialog
                              }}
                            >
                              <Settings className="w-3 h-3" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="tasks" className="flex-1 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Task Management</h3>
                  <p className="text-sm text-muted-foreground">
                    Monitor and manage agent tasks
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Select defaultValue="all">
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Tasks</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="in_progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Card>
                <CardContent className="p-0">
                  <ScrollArea className="h-96">
                    <div className="divide-y">
                      {tasks.map((task) => {
                        const agent = agents.find(a => a.id === task.agentId);
                        const IconComponent = agent ? agentTypeData[agent.type].icon : Bot;
                        
                        return (
                          <div key={task.id} className="p-4 hover:bg-accent/50 transition-colors">
                            <div className="flex items-start gap-3">
                              <Avatar className="h-8 w-8 mt-1">
                                <AvatarImage src={agent?.avatar} />
                                <AvatarFallback>
                                  <IconComponent className="w-4 h-4" />
                                </AvatarFallback>
                              </Avatar>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between mb-2">
                                  <div>
                                    <h4 className="font-medium truncate pr-2">{task.title}</h4>
                                    <p className="text-sm text-muted-foreground">
                                      Assigned to {agent?.name || 'Unknown Agent'}
                                    </p>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                                      {task.priority}
                                    </Badge>
                                    <Badge className={`text-xs ${getTaskStatusColor(task.status)}`}>
                                      {task.status}
                                    </Badge>
                                  </div>
                                </div>
                                
                                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                                  {task.description}
                                </p>
                                
                                <div className="flex items-center justify-between mb-2">
                                  <div className="text-xs text-muted-foreground">
                                    Progress: {task.progress}%
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    Started: {task.startTime.toLocaleTimeString()}
                                  </div>
                                </div>
                                
                                <Progress value={task.progress} className="h-2" />
                                
                                {task.result && (
                                  <div className="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
                                    <div className="flex items-center gap-2 mb-1">
                                      <CheckCircle className="w-4 h-4 text-green-600" />
                                      <span className="text-sm font-medium text-green-800">Result</span>
                                    </div>
                                    <p className="text-sm text-green-700">{task.result}</p>
                                  </div>
                                )}
                                
                                <div className="flex items-center gap-1 mt-3">
                                  {task.tags.map((tag) => (
                                    <Badge key={tag} variant="outline" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="chat" className="flex-1 flex flex-col">
              <div className="flex items-center gap-4 mb-4">
                <Select 
                  value={selectedAgent?.id || ''} 
                  onValueChange={(value) => setSelectedAgent(agents.find(a => a.id === value) || null)}
                >
                  <SelectTrigger className="w-64">
                    <SelectValue placeholder="Select an agent to chat with" />
                  </SelectTrigger>
                  <SelectContent>
                    {agents.map((agent) => {
                      const IconComponent = agentTypeData[agent.type].icon;
                      return (
                        <SelectItem key={agent.id} value={agent.id}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="w-4 h-4" />
                            {agent.name}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
                
                {selectedAgent && (
                  <Badge variant="outline" className="gap-1">
                    <div className={`w-2 h-2 rounded-full ${getAgentStatusColor(selectedAgent.status)}`} />
                    {selectedAgent.status}
                  </Badge>
                )}
              </div>

              {selectedAgent ? (
                <div className="flex-1 flex flex-col">
                  <Card className="flex-1 flex flex-col">
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={selectedAgent.avatar} />
                          <AvatarFallback>
                            {React.createElement(agentTypeData[selectedAgent.type].icon, { className: "w-4 h-4" })}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg">{selectedAgent.name}</CardTitle>
                          <CardDescription>{selectedAgent.type.replace('_', ' ')}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="flex-1 flex flex-col p-0">
                      <ScrollArea className="flex-1 px-4" ref={chatScrollRef}>
                        <div className="space-y-4 pb-4">
                          <div className="flex justify-center">
                            <Badge variant="secondary" className="text-xs">
                              Conversation started with {selectedAgent.name}
                            </Badge>
                          </div>
                          
                          {conversations
                            .find(c => c.agentId === selectedAgent.id)?.messages
                            .map((message) => (
                              <motion.div
                                key={message.id}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                              >
                                <div className={`max-w-[80%] p-3 rounded-lg ${
                                  message.role === 'user' 
                                    ? 'bg-primary text-primary-foreground' 
                                    : 'bg-muted'
                                }`}>
                                  <p className="text-sm">{message.content}</p>
                                  <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                                    <span>{message.timestamp.toLocaleTimeString()}</span>
                                    {message.metadata && (
                                      <span>
                                        {message.metadata.tokensUsed} tokens • {message.metadata.responseTime}ms
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </motion.div>
                            ))}
                        </div>
                      </ScrollArea>
                      
                      <div className="p-4 border-t">
                        <div className="flex gap-2">
                          <Textarea
                            value={currentTask}
                            onChange={(e) => setCurrentTask(e.target.value)}
                            placeholder={`Send a task to ${selectedAgent.name}...`}
                            className="min-h-[60px] resize-none"
                            onKeyPress={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handleSendTask(selectedAgent.id, currentTask);
                              }
                            }}
                          />
                          <Button 
                            onClick={() => handleSendTask(selectedAgent.id, currentTask)}
                            disabled={!currentTask.trim() || selectedAgent.status === 'working'}
                            size="icon"
                            className="self-end"
                          >
                            {selectedAgent.status === 'working' ? (
                              <RefreshCw className="w-4 h-4 animate-spin" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <Bot className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">Select an Agent</h3>
                    <p className="text-muted-foreground">
                      Choose an agent from the dropdown above to start a conversation
                    </p>
                  </div>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="analytics" className="flex-1 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Total Tokens Used</p>
                        <p className="text-2xl font-bold">
                          {Math.round(agents.reduce((acc, a) => acc + a.metrics.tokensUsed, 0) / 1000)}K
                        </p>
                      </div>
                      <Zap className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                        <p className="text-2xl font-bold">
                          {Math.round(agents.reduce((acc, a) => acc + a.metrics.averageResponseTime, 0) / agents.length / 1000)}s
                        </p>
                      </div>
                      <Clock className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Cost Efficiency</p>
                        <p className="text-2xl font-bold">
                          {Math.round(agents.reduce((acc, a) => acc + a.metrics.costEfficiency, 0) / agents.length)}%
                        </p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">User Satisfaction</p>
                        <p className="text-2xl font-bold">
                          {(agents.reduce((acc, a) => acc + a.metrics.userSatisfaction, 0) / agents.length).toFixed(1)}
                        </p>
                      </div>
                      <Heart className="w-8 h-8 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Agent Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {agents.slice(0, 6).map((agent) => (
                        <div key={agent.id} className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium">{agent.name}</span>
                            <span>{Math.round(agent.metrics.successRate)}%</span>
                          </div>
                          <Progress value={agent.metrics.successRate} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Task Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(
                        tasks.reduce((acc, task) => {
                          acc[task.status] = (acc[task.status] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).map(([status, count]) => (
                        <div key={status} className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="font-medium capitalize">{status.replace('_', ' ')}</span>
                            <span>{count}</span>
                          </div>
                          <Progress value={(count / tasks.length) * 100} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Create Agent Dialog */}
      <Dialog open={isCreatingAgent} onOpenChange={setIsCreatingAgent}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New AI Agent</DialogTitle>
            <DialogDescription>
              Configure a new AI agent with specific capabilities and personality
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="agent-name">Agent Name</Label>
              <Input
                id="agent-name"
                value={agentForm.name}
                onChange={(e) => setAgentForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter agent name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="agent-type">Agent Type</Label>
              <Select 
                value={agentForm.type} 
                onValueChange={(value) => setAgentForm(prev => ({ ...prev, type: value as AgentType }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(agentTypeData).map(([type, data]) => {
                    const IconComponent = data.icon;
                    return (
                      <SelectItem key={type} value={type}>
                        <div className="flex items-center gap-2">
                          <IconComponent className="w-4 h-4" />
                          {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-4">
              <Label>Personality Configuration</Label>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Communication Style</Label>
                  <Select 
                    value={agentForm.personality.communication_style} 
                    onValueChange={(value) => setAgentForm(prev => ({ 
                      ...prev, 
                      personality: { ...prev.personality, communication_style: value as any }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="formal">Formal</SelectItem>
                      <SelectItem value="casual">Casual</SelectItem>
                      <SelectItem value="technical">Technical</SelectItem>
                      <SelectItem value="friendly">Friendly</SelectItem>
                      <SelectItem value="concise">Concise</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Expertise Level</Label>
                  <Select 
                    value={agentForm.personality.expertise_level} 
                    onValueChange={(value) => setAgentForm(prev => ({ 
                      ...prev, 
                      personality: { ...prev.personality, expertise_level: value as any }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="expert">Expert</SelectItem>
                      <SelectItem value="master">Master</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Creativity Level: {agentForm.personality.creativity_level}%</Label>
                <Slider
                  value={[agentForm.personality.creativity_level]}
                  onValueChange={(value) => setAgentForm(prev => ({ 
                    ...prev, 
                    personality: { ...prev.personality, creativity_level: value[0] }
                  }))}
                  max={100}
                  step={1}
                />
              </div>

              <div className="space-y-2">
                <Label>Detail Orientation: {agentForm.personality.detail_orientation}%</Label>
                <Slider
                  value={[agentForm.personality.detail_orientation]}
                  onValueChange={(value) => setAgentForm(prev => ({ 
                    ...prev, 
                    personality: { ...prev.personality, detail_orientation: value[0] }
                  }))}
                  max={100}
                  step={1}
                />
              </div>
            </div>

            <div className="space-y-4">
              <Label>Technical Configuration</Label>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Model</Label>
                  <Select 
                    value={agentForm.configuration.model} 
                    onValueChange={(value) => setAgentForm(prev => ({ 
                      ...prev, 
                      configuration: { ...prev.configuration, model: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
                      <SelectItem value="claude-3-sonnet">Claude 3 Sonnet</SelectItem>
                      <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Max Tokens</Label>
                  <Input
                    type="number"
                    value={agentForm.configuration.maxTokens}
                    onChange={(e) => setAgentForm(prev => ({ 
                      ...prev, 
                      configuration: { ...prev.configuration, maxTokens: parseInt(e.target.value) }
                    }))}
                    min={1000}
                    max={8000}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Temperature: {agentForm.configuration.temperature}</Label>
                <Slider
                  value={[agentForm.configuration.temperature]}
                  onValueChange={(value) => setAgentForm(prev => ({ 
                    ...prev, 
                    configuration: { ...prev.configuration, temperature: value[0] }
                  }))}
                  max={1}
                  step={0.1}
                  min={0}
                />
              </div>

              <div className="space-y-2">
                <Label>Autonomy Level: {agentForm.configuration.autonomyLevel}%</Label>
                <Slider
                  value={[agentForm.configuration.autonomyLevel]}
                  onValueChange={(value) => setAgentForm(prev => ({ 
                    ...prev, 
                    configuration: { ...prev.configuration, autonomyLevel: value[0] }
                  }))}
                  max={100}
                  step={1}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={agentForm.configuration.learningEnabled}
                  onCheckedChange={(checked) => setAgentForm(prev => ({ 
                    ...prev, 
                    configuration: { ...prev.configuration, learningEnabled: checked }
                  }))}
                />
                <Label>Enable Learning</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreatingAgent(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateAgent} disabled={!agentForm.name.trim()}>
              Create Agent
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}