import { useState, useEffect, useRef, use<PERSON><PERSON>back, memo } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
// Tabs components imported but not used in this simplified version
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { api } from '@/lib/api';
// Animation components imported but not used in this simplified version
import * as d3 from 'd3';
import { 
  Network,
  ZoomIn,
  ZoomOut,
  Download,
  RefreshCw,
  Loader2
} from 'lucide-react';

interface CodeFlowVisualizationProps {
  sessionId: string;
  projectPath: string;
  className?: string;
}

interface CodeNode {
  id: string;
  name: string;
  type: 'file' | 'function' | 'class' | 'module' | 'component';
  path: string;
  lines: number;
  complexity: number;
  dependencies: string[];
  dependents: string[];
  metrics: NodeMetrics;
  x?: number;
  y?: number;
  fx?: number;
  fy?: number;
}

interface CodeLink {
  source: string;
  target: string;
  type: 'import' | 'call' | 'extends' | 'implements' | 'uses';
  strength: number;
}

interface NodeMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  maintainabilityIndex: number;
  testCoverage?: number;
  lastModified: Date;
  author?: string;
}

interface VisualizationData {
  nodes: CodeNode[];
  links: CodeLink[];
  clusters: Cluster[];
}

interface Cluster {
  id: string;
  name: string;
  nodes: string[];
  type: 'module' | 'feature' | 'layer';
}

type VisualizationType = 'force' | 'hierarchy' | 'matrix' | 'flow';
type ColorScheme = 'type' | 'complexity' | 'coverage' | 'modified';

// const nodeTypeIcons = {
//   file: FileCode2,
//   function: Circle,
//   class: Square,
//   module: Hexagon,
//   component: Box
// };

const nodeTypeColors = {
  file: '#3B82F6',
  function: '#10B981',
  class: '#F59E0B',
  module: '#8B5CF6',
  component: '#EC4899'
};

export const CodeFlowVisualization = memo(function CodeFlowVisualization({ sessionId, projectPath, className }: CodeFlowVisualizationProps) {
  const [visualizationData, setVisualizationData] = useState<VisualizationData | null>(null);
  const [selectedNode, setSelectedNode] = useState<CodeNode | null>(null);
  const [visualizationType, setVisualizationType] = useState<VisualizationType>('force');
  const [colorScheme, setColorScheme] = useState<ColorScheme>('type');
  const [isLoading, setIsLoading] = useState(true);
  const [showLabels, setShowLabels] = useState(true);
  const [showMetrics, setShowMetrics] = useState(false);
  const [filterComplexity, setFilterComplexity] = useState([0, 100]);
  const [zoomLevel, setZoomLevel] = useState(1);
  
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const simulationRef = useRef<any>(null);

  useEffect(() => {
    loadVisualizationData();
  }, [sessionId, projectPath]);

  useEffect(() => {
    if (visualizationData) {
      renderVisualization();
    }
    
    // Cleanup function
    return () => {
      // Stop any running force simulation
      if (simulationRef.current) {
        simulationRef.current.stop();
        simulationRef.current = null;
      }
      
      // Clear SVG content
      if (svgRef.current) {
        d3.select(svgRef.current).selectAll('*').remove();
      }
    };
  }, [visualizationData, visualizationType, colorScheme, showLabels, filterComplexity]);

  const loadVisualizationData = async () => {
    setIsLoading(true);
    try {
      const data = await api.getCodeFlowData(sessionId, projectPath);
      setVisualizationData(data);
    } catch (error) {
      console.error('Failed to load visualization data:', error);
      // Use mock data for demonstration
      setVisualizationData(generateMockData());
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockData = (): VisualizationData => {
    const nodes: CodeNode[] = [
      {
        id: 'app',
        name: 'App.tsx',
        type: 'component',
        path: 'src/App.tsx',
        lines: 450,
        complexity: 15,
        dependencies: ['router', 'store', 'api'],
        dependents: [],
        metrics: {
          linesOfCode: 450,
          cyclomaticComplexity: 15,
          maintainabilityIndex: 75,
          testCoverage: 82,
          lastModified: new Date('2024-01-15'),
          author: 'John Doe'
        }
      },
      {
        id: 'router',
        name: 'Router.tsx',
        type: 'module',
        path: 'src/Router.tsx',
        lines: 120,
        complexity: 8,
        dependencies: ['components'],
        dependents: ['app'],
        metrics: {
          linesOfCode: 120,
          cyclomaticComplexity: 8,
          maintainabilityIndex: 80,
          testCoverage: 90,
          lastModified: new Date('2024-01-10')
        }
      },
      {
        id: 'store',
        name: 'Store.ts',
        type: 'module',
        path: 'src/store/index.ts',
        lines: 200,
        complexity: 12,
        dependencies: ['api'],
        dependents: ['app', 'components'],
        metrics: {
          linesOfCode: 200,
          cyclomaticComplexity: 12,
          maintainabilityIndex: 70,
          testCoverage: 75,
          lastModified: new Date('2024-01-12')
        }
      },
      {
        id: 'api',
        name: 'API Client',
        type: 'module',
        path: 'src/lib/api.ts',
        lines: 300,
        complexity: 10,
        dependencies: [],
        dependents: ['app', 'store', 'components'],
        metrics: {
          linesOfCode: 300,
          cyclomaticComplexity: 10,
          maintainabilityIndex: 85,
          testCoverage: 95,
          lastModified: new Date('2024-01-08')
        }
      },
      {
        id: 'components',
        name: 'Components',
        type: 'module',
        path: 'src/components',
        lines: 800,
        complexity: 25,
        dependencies: ['store', 'api'],
        dependents: ['router'],
        metrics: {
          linesOfCode: 800,
          cyclomaticComplexity: 25,
          maintainabilityIndex: 78,
          testCoverage: 88,
          lastModified: new Date('2024-01-14')
        }
      }
    ];

    const links: CodeLink[] = [
      { source: 'app', target: 'router', type: 'import', strength: 1 },
      { source: 'app', target: 'store', type: 'import', strength: 1 },
      { source: 'app', target: 'api', type: 'import', strength: 0.8 },
      { source: 'router', target: 'components', type: 'import', strength: 1 },
      { source: 'store', target: 'api', type: 'call', strength: 0.9 },
      { source: 'components', target: 'store', type: 'uses', strength: 0.8 },
      { source: 'components', target: 'api', type: 'call', strength: 0.7 }
    ];

    const clusters: Cluster[] = [
      {
        id: 'presentation',
        name: 'Presentation Layer',
        type: 'layer',
        nodes: ['app', 'router', 'components']
      },
      {
        id: 'data',
        name: 'Data Layer',
        type: 'layer',
        nodes: ['store', 'api']
      }
    ];

    return { nodes, links, clusters };
  };

  const renderVisualization = () => {
    if (!svgRef.current || !containerRef.current || !visualizationData) return;

    // Clear existing visualization
    d3.select(svgRef.current).selectAll('*').remove();

    const { width, height } = containerRef.current.getBoundingClientRect();
    const svg = d3.select(svgRef.current)
      .attr('width', width)
      .attr('height', height);

    switch (visualizationType) {
      case 'force':
        renderForceGraph(svg, width, height);
        break;
      case 'hierarchy':
        renderHierarchy(svg, width, height);
        break;
      case 'matrix':
        renderMatrix(svg, width, height);
        break;
      case 'flow':
        renderFlow(svg, width, height);
        break;
    }
  };

  const renderForceGraph = (svg: any, width: number, height: number) => {
    if (!visualizationData) return;

    const { nodes, links } = visualizationData;
    
    // Filter nodes based on complexity
    const filteredNodes = nodes.filter(n => 
      n.complexity >= filterComplexity[0] && n.complexity <= filterComplexity[1]
    );
    const filteredNodeIds = new Set(filteredNodes.map(n => n.id));
    const filteredLinks = links.filter(l => 
      filteredNodeIds.has(l.source) && filteredNodeIds.has(l.target)
    );

    // Create zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.1, 10])
      .on('zoom', (event) => {
        g.attr('transform', event.transform);
        setZoomLevel(event.transform.k);
      });

    svg.call(zoom);

    const g = svg.append('g');

    // Create force simulation
    const simulation = d3.forceSimulation(filteredNodes)
      .force('link', d3.forceLink(filteredLinks)
        .id((d: any) => d.id)
        .distance((d: any) => 100 / d.strength))
      .force('charge', d3.forceManyBody().strength(-300))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(30));

    simulationRef.current = simulation;

    // Create links
    const link = g.append('g')
      .selectAll('line')
      .data(filteredLinks)
      .enter().append('line')
      .attr('stroke', '#999')
      .attr('stroke-opacity', 0.6)
      .attr('stroke-width', (d: any) => Math.sqrt(d.strength * 5));

    // Create nodes
    const node = g.append('g')
      .selectAll('g')
      .data(filteredNodes)
      .enter().append('g')
      .call(d3.drag()
        .on('start', dragstarted)
        .on('drag', dragged)
        .on('end', dragended));

    // Add circles for nodes
    node.append('circle')
      .attr('r', (d: any) => Math.sqrt(d.lines / 10) + 10)
      .attr('fill', (d: any) => getNodeColor(d))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .on('click', (_event: any, d: any) => setSelectedNode(d));

    // Add labels
    if (showLabels) {
      node.append('text')
        .text((d: any) => d.name)
        .attr('x', 0)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '12px')
        .style('pointer-events', 'none');
    }

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      node.attr('transform', (d: any) => `translate(${d.x},${d.y})`);
    });

    function dragstarted(event: any, d: any) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event: any, d: any) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragended(event: any, d: any) {
      if (!event.active) simulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }
  };

  const renderHierarchy = (svg: any, width: number, height: number) => {
    if (!visualizationData) return;

    // Create hierarchical data structure
    const root = d3.stratify()
      .id((d: any) => d.id)
      .parentId((d: any) => {
        // Simple hierarchy based on dependencies
        if (d.dependencies.length === 0) return null;
        return d.dependencies[0];
      })(visualizationData.nodes);

    const treeLayout = d3.tree()
      .size([width - 100, height - 100]);

    const treeData = treeLayout(root);

    const g = svg.append('g')
      .attr('transform', 'translate(50,50)');

    // Draw links
    g.selectAll('.link')
      .data(treeData.links())
      .enter().append('path')
      .attr('class', 'link')
      .attr('d', d3.linkHorizontal()
        .x((d: any) => d.y)
        .y((d: any) => d.x))
      .attr('fill', 'none')
      .attr('stroke', '#999')
      .attr('stroke-width', 2);

    // Draw nodes
    const node = g.selectAll('.node')
      .data(treeData.descendants())
      .enter().append('g')
      .attr('class', 'node')
      .attr('transform', (d: any) => `translate(${d.y},${d.x})`);

    node.append('circle')
      .attr('r', 10)
      .attr('fill', (d: any) => getNodeColor(d.data))
      .on('click', (_event: any, d: any) => setSelectedNode(d.data));

    if (showLabels) {
      node.append('text')
        .attr('dy', '.35em')
        .attr('x', (d: any) => d.children ? -13 : 13)
        .style('text-anchor', (d: any) => d.children ? 'end' : 'start')
        .text((d: any) => d.data.name);
    }
  };

  const renderMatrix = (svg: any, width: number, height: number) => {
    if (!visualizationData) return;

    const { nodes, links } = visualizationData;
    const matrix: number[][] = [];
    const nodeIndex = new Map<string, number>();

    // Create node index
    nodes.forEach((node, i) => {
      nodeIndex.set(node.id, i);
      matrix[i] = new Array(nodes.length).fill(0);
    });

    // Fill matrix with link strengths
    links.forEach(link => {
      const sourceIdx = nodeIndex.get(link.source);
      const targetIdx = nodeIndex.get(link.target);
      if (sourceIdx !== undefined && targetIdx !== undefined) {
        matrix[sourceIdx][targetIdx] = link.strength;
      }
    });

    const cellSize = Math.min((width - 100) / nodes.length, (height - 100) / nodes.length);
    const g = svg.append('g')
      .attr('transform', 'translate(50,50)');

    // Draw cells
    nodes.forEach((sourceNode, i) => {
      nodes.forEach((_targetNode, j) => {
        if (matrix[i][j] > 0) {
          g.append('rect')
            .attr('x', j * cellSize)
            .attr('y', i * cellSize)
            .attr('width', cellSize - 2)
            .attr('height', cellSize - 2)
            .attr('fill', getNodeColor(sourceNode))
            .attr('opacity', matrix[i][j])
            .on('click', () => {
              setSelectedNode(sourceNode);
            });
        }
      });
    });

    // Add labels
    if (showLabels) {
      nodes.forEach((node, i) => {
        g.append('text')
          .attr('x', -5)
          .attr('y', i * cellSize + cellSize / 2)
          .attr('text-anchor', 'end')
          .attr('dominant-baseline', 'middle')
          .style('font-size', '10px')
          .text(node.name);

        g.append('text')
          .attr('x', i * cellSize + cellSize / 2)
          .attr('y', -5)
          .attr('text-anchor', 'middle')
          .attr('transform', `rotate(-45 ${i * cellSize + cellSize / 2} -5)`)
          .style('font-size', '10px')
          .text(node.name);
      });
    }
  };

  const renderFlow = (svg: any, width: number, height: number) => {
    // Implement Sankey diagram or flow visualization
    renderForceGraph(svg, width, height); // Fallback to force graph for now
  };

  const getNodeColor = useCallback((node: CodeNode): string => {
    switch (colorScheme) {
      case 'type':
        return nodeTypeColors[node.type] || '#666';
      case 'complexity':
        const complexityScale = d3.scaleSequential(d3.interpolateRdYlGn)
          .domain([30, 0]);
        return complexityScale(node.complexity);
      case 'coverage':
        const coverageScale = d3.scaleSequential(d3.interpolateRdYlGn)
          .domain([0, 100]);
        return coverageScale(node.metrics.testCoverage || 0);
      case 'modified':
        const recency = Date.now() - new Date(node.metrics.lastModified).getTime();
        const daysAgo = recency / (1000 * 60 * 60 * 24);
        const recencyScale = d3.scaleSequential(d3.interpolateBlues)
          .domain([30, 0]);
        return recencyScale(daysAgo);
      default:
        return '#666';
    }
  }, [colorScheme]);

  const handleZoom = useCallback((delta: number) => {
    const newZoom = Math.max(0.1, Math.min(10, zoomLevel + delta));
    setZoomLevel(newZoom);
    
    if (svgRef.current) {
      const svg = d3.select(svgRef.current);
      svg.transition().duration(300).call(
        d3.zoom().scaleTo as any,
        newZoom
      );
    }
  }, [zoomLevel]);

  const handleExport = useCallback(() => {
    if (!svgRef.current) return;
    
    const svgData = new XMLSerializer().serializeToString(svgRef.current);
    const blob = new Blob([svgData], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'code-flow-visualization.svg';
    link.click();
    
    URL.revokeObjectURL(url);
  }, []);

  return (
    <div className={`h-full flex flex-col ${className}`}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Network className="w-5 h-5" />
              Code Flow Visualization
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleZoom(0.2)}
              >
                <ZoomIn className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleZoom(-0.2)}
              >
                <ZoomOut className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={handleExport}
              >
                <Download className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={loadVisualizationData}
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex gap-4 overflow-hidden">
          <div className="flex-1 relative" ref={containerRef}>
            {isLoading ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            ) : (
              <svg ref={svgRef} className="w-full h-full" />
            )}
          </div>
          
          <div className="w-80 space-y-4 overflow-y-auto">
            {/* Controls */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Visualization Controls</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="viz-type">Visualization Type</Label>
                  <Select value={visualizationType} onValueChange={(v) => setVisualizationType(v as VisualizationType)}>
                    <SelectTrigger id="viz-type">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="force">Force-Directed Graph</SelectItem>
                      <SelectItem value="hierarchy">Hierarchical Tree</SelectItem>
                      <SelectItem value="matrix">Dependency Matrix</SelectItem>
                      <SelectItem value="flow">Flow Diagram</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="color-scheme">Color Scheme</Label>
                  <Select value={colorScheme} onValueChange={(v) => setColorScheme(v as ColorScheme)}>
                    <SelectTrigger id="color-scheme">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="type">By Type</SelectItem>
                      <SelectItem value="complexity">By Complexity</SelectItem>
                      <SelectItem value="coverage">By Test Coverage</SelectItem>
                      <SelectItem value="modified">By Last Modified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Complexity Filter</Label>
                    <span className="text-xs text-muted-foreground">
                      {filterComplexity[0]} - {filterComplexity[1]}
                    </span>
                  </div>
                  <Slider
                    value={filterComplexity}
                    onValueChange={setFilterComplexity}
                    min={0}
                    max={100}
                    step={1}
                    className="w-full"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-labels">Show Labels</Label>
                  <Switch
                    id="show-labels"
                    checked={showLabels}
                    onCheckedChange={setShowLabels}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-metrics">Show Metrics</Label>
                  <Switch
                    id="show-metrics"
                    checked={showMetrics}
                    onCheckedChange={setShowMetrics}
                  />
                </div>
              </CardContent>
            </Card>
            
            {/* Selected Node Details */}
            {selectedNode && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    {/* Node icon would go here */}
                    {selectedNode.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <p className="text-xs text-muted-foreground">Path</p>
                    <p className="text-sm font-mono">{selectedNode.path}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <p className="text-xs text-muted-foreground">Lines of Code</p>
                      <p className="text-sm font-semibold">{selectedNode.lines}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Complexity</p>
                      <p className="text-sm font-semibold">{selectedNode.complexity}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Maintainability</p>
                      <p className="text-sm font-semibold">{selectedNode.metrics.maintainabilityIndex}%</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Test Coverage</p>
                      <p className="text-sm font-semibold">{selectedNode.metrics.testCoverage || 'N/A'}%</p>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Dependencies ({selectedNode.dependencies.length})</p>
                    <div className="flex flex-wrap gap-1">
                      {selectedNode.dependencies.map(dep => (
                        <Badge key={dep} variant="secondary" className="text-xs">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-xs text-muted-foreground mb-1">Dependents ({selectedNode.dependents.length})</p>
                    <div className="flex flex-wrap gap-1">
                      {selectedNode.dependents.map(dep => (
                        <Badge key={dep} variant="outline" className="text-xs">
                          {dep}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Legend */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Legend</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {colorScheme === 'type' && (
                  <div className="space-y-1">
                    {Object.entries(nodeTypeColors).map(([type, color]) => (
                      <div key={type} className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: color }} />
                        <span className="text-xs capitalize">{type}</span>
                      </div>
                    ))}
                  </div>
                )}
                {colorScheme !== 'type' && (
                  <div className="text-xs text-muted-foreground">
                    Color intensity represents {colorScheme}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});