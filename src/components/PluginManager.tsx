import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Puzzle, 
  Search, 
  Download, 
  Trash2, 
  Code2, 
  AlertCircle,
  CheckCircle,
  GitBranch,
  Package,
  RefreshCw,
  Shield,
  Star,
  Zap
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { api } from '@/lib/api';
import type { Plugin, PluginManifest, PluginSettings } from '@/lib/api';

interface PluginManagerProps {
  onClose: () => void;
}

export function PluginManager({ onClose }: PluginManagerProps) {
  const [installedPlugins, setInstalledPlugins] = useState<Plugin[]>([]);
  const [availablePlugins, setAvailablePlugins] = useState<Plugin[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [activeTab, setActiveTab] = useState('installed');
  const [loading, setLoading] = useState(true);
  const [installing, setInstalling] = useState<string | null>(null);
  const [showDeveloper, setShowDeveloper] = useState(false);
  const [developerForm, setDeveloperForm] = useState<Partial<PluginManifest>>({
    name: '',
    description: '',
    version: '1.0.0',
    author: '',
    entry: 'index.js',
    capabilities: []
  });

  useEffect(() => {
    loadPlugins();
  }, []);

  const loadPlugins = async () => {
    try {
      setLoading(true);
      const [installed, available] = await Promise.all([
        api.getInstalledPlugins(),
        api.getAvailablePlugins()
      ]);
      setInstalledPlugins(installed);
      setAvailablePlugins(available);
    } catch (error) {
      console.error('Failed to load plugins:', error);
    } finally {
      setLoading(false);
    }
  };

  const installPlugin = async (plugin: Plugin) => {
    try {
      setInstalling(plugin.id);
      await api.installPlugin(plugin.id);
      await loadPlugins();
    } catch (error) {
      console.error('Failed to install plugin:', error);
    } finally {
      setInstalling(null);
    }
  };

  const uninstallPlugin = async (plugin: Plugin) => {
    try {
      await api.uninstallPlugin(plugin.id);
      await loadPlugins();
    } catch (error) {
      console.error('Failed to uninstall plugin:', error);
    }
  };

  const togglePlugin = async (plugin: Plugin, enabled: boolean) => {
    try {
      await api.togglePlugin(plugin.id, enabled);
      await loadPlugins();
    } catch (error) {
      console.error('Failed to toggle plugin:', error);
    }
  };

  const updatePluginSettings = async (pluginId: string, settings: PluginSettings) => {
    try {
      await api.updatePluginSettings(pluginId, settings);
      await loadPlugins();
    } catch (error) {
      console.error('Failed to update plugin settings:', error);
    }
  };

  const createPlugin = async () => {
    try {
      const manifest: PluginManifest = {
        id: developerForm.name?.toLowerCase().replace(/\s+/g, '-') || '',
        name: developerForm.name || '',
        description: developerForm.description || '',
        version: developerForm.version || '1.0.0',
        author: developerForm.author || '',
        entry: developerForm.entry || 'index.js',
        capabilities: developerForm.capabilities || [],
        repository: developerForm.repository
      };
      
      await api.createPlugin(manifest);
      setShowDeveloper(false);
      setDeveloperForm({
        name: '',
        description: '',
        version: '1.0.0',
        author: '',
        entry: 'index.js',
        capabilities: []
      });
      await loadPlugins();
    } catch (error) {
      console.error('Failed to create plugin:', error);
    }
  };

  const filteredPlugins = (activeTab === 'installed' ? installedPlugins : availablePlugins)
    .filter(plugin => 
      plugin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      plugin.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

  const getPluginIcon = (plugin: Plugin) => {
    if (plugin.icon) {
      return <img src={plugin.icon} alt={plugin.name} className="w-12 h-12 rounded" />;
    }
    
    const icons: Record<string, any> = {
      'code': Code2,
      'git': GitBranch,
      'security': Shield,
      'performance': Zap,
      'ui': Puzzle
    };
    
    const IconComponent = icons[plugin.category || 'code'] || Package;
    return <IconComponent className="w-12 h-12 text-muted-foreground" />;
  };

  const renderPluginCard = (plugin: Plugin) => {
    const isInstalled = installedPlugins.some(p => p.id === plugin.id);
    const isInstalling = installing === plugin.id;
    
    return (
      <motion.div
        key={plugin.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ scale: 1.02 }}
        className="cursor-pointer"
        onClick={() => setSelectedPlugin(plugin)}
      >
        <Card className="h-full hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              {getPluginIcon(plugin)}
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-lg">{plugin.name}</h3>
                  <div className="flex items-center gap-2">
                    {plugin.verified && (
                      <Badge variant="secondary" className="gap-1">
                        <CheckCircle className="w-3 h-3" />
                        Verified
                      </Badge>
                    )}
                    <Badge variant="outline">{plugin.version}</Badge>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3">{plugin.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Star className="w-3 h-3" />
                      {plugin.rating || 0}
                    </span>
                    <span className="flex items-center gap-1">
                      <Download className="w-3 h-3" />
                      {plugin.downloads || 0}
                    </span>
                    <span>by {plugin.author}</span>
                  </div>
                  {isInstalled ? (
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={plugin.enabled}
                        onCheckedChange={(checked) => togglePlugin(plugin, checked)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          uninstallPlugin(plugin);
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ) : (
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        installPlugin(plugin);
                      }}
                      disabled={isInstalling}
                    >
                      {isInstalling ? (
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Download className="w-4 h-4 mr-2" />
                      )}
                      Install
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  const renderDeveloperDialog = () => (
    <Dialog open={showDeveloper} onOpenChange={setShowDeveloper}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Code2 className="w-4 h-4" />
          Create Plugin
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Plugin</DialogTitle>
          <DialogDescription>
            Create a new plugin for Claudia Enhanced
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 mt-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="plugin-name">Plugin Name</Label>
              <Input
                id="plugin-name"
                value={developerForm.name}
                onChange={(e) => setDeveloperForm({ ...developerForm, name: e.target.value })}
                placeholder="My Awesome Plugin"
              />
            </div>
            <div>
              <Label htmlFor="plugin-version">Version</Label>
              <Input
                id="plugin-version"
                value={developerForm.version}
                onChange={(e) => setDeveloperForm({ ...developerForm, version: e.target.value })}
                placeholder="1.0.0"
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="plugin-description">Description</Label>
            <Textarea
              id="plugin-description"
              value={developerForm.description}
              onChange={(e) => setDeveloperForm({ ...developerForm, description: e.target.value })}
              placeholder="A brief description of what your plugin does"
              rows={3}
            />
          </div>
          
          <div>
            <Label htmlFor="plugin-author">Author</Label>
            <Input
              id="plugin-author"
              value={developerForm.author}
              onChange={(e) => setDeveloperForm({ ...developerForm, author: e.target.value })}
              placeholder="Your Name"
            />
          </div>
          
          <div>
            <Label htmlFor="plugin-entry">Entry Point</Label>
            <Input
              id="plugin-entry"
              value={developerForm.entry}
              onChange={(e) => setDeveloperForm({ ...developerForm, entry: e.target.value })}
              placeholder="index.js"
            />
          </div>
          
          <div>
            <Label>Capabilities</Label>
            <div className="space-y-2 mt-2">
              {['ui', 'api', 'filesystem', 'commands', 'themes'].map(cap => (
                <div key={cap} className="flex items-center space-x-2">
                  <Switch
                    id={`cap-${cap}`}
                    checked={developerForm.capabilities?.includes(cap)}
                    onCheckedChange={(checked) => {
                      const caps = developerForm.capabilities || [];
                      setDeveloperForm({
                        ...developerForm,
                        capabilities: checked 
                          ? [...caps, cap]
                          : caps.filter(c => c !== cap)
                      });
                    }}
                  />
                  <Label htmlFor={`cap-${cap}`} className="text-sm capitalize">
                    {cap}
                  </Label>
                </div>
              ))}
            </div>
          </div>
          
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              A plugin scaffold will be created in the plugins directory. You can then edit the files to implement your plugin functionality.
            </AlertDescription>
          </Alert>
          
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowDeveloper(false)}>
              Cancel
            </Button>
            <Button onClick={createPlugin}>
              Create Plugin
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  const renderPluginDetails = () => {
    if (!selectedPlugin) return null;
    
    return (
      <Dialog open={!!selectedPlugin} onOpenChange={() => setSelectedPlugin(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center gap-4">
              {getPluginIcon(selectedPlugin)}
              <div>
                <DialogTitle className="text-2xl">{selectedPlugin.name}</DialogTitle>
                <DialogDescription className="text-base">
                  {selectedPlugin.description}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>
          
          <div className="space-y-6 mt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>Version {selectedPlugin.version}</span>
                <span>by {selectedPlugin.author}</span>
                {selectedPlugin.repository && (
                  <a
                    href={selectedPlugin.repository}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-primary hover:underline"
                  >
                    <GitBranch className="w-3 h-3" />
                    Repository
                  </a>
                )}
              </div>
              <div className="flex items-center gap-2">
                {selectedPlugin.verified && (
                  <Badge variant="secondary" className="gap-1">
                    <CheckCircle className="w-3 h-3" />
                    Verified
                  </Badge>
                )}
                <Badge variant="outline">{selectedPlugin.category}</Badge>
              </div>
            </div>
            
            {selectedPlugin.readme && (
              <div className="prose dark:prose-invert max-w-none">
                <h3>About</h3>
                <div dangerouslySetInnerHTML={{ __html: selectedPlugin.readme }} />
              </div>
            )}
            
            {selectedPlugin.capabilities && selectedPlugin.capabilities.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Required Permissions</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedPlugin.capabilities.map(cap => (
                    <Badge key={cap} variant="secondary">
                      <Shield className="w-3 h-3 mr-1" />
                      {cap}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {selectedPlugin.settings && (
              <div>
                <h3 className="font-semibold mb-2">Settings</h3>
                <Card>
                  <CardContent className="space-y-4 pt-6">
                    {Object.entries(selectedPlugin.settings).map(([key, setting]) => (
                      <div key={key}>
                        <Label htmlFor={key}>{setting.label}</Label>
                        {setting.type === 'boolean' ? (
                          <Switch
                            id={key}
                            checked={setting.value as boolean}
                            onCheckedChange={(checked) => {
                              updatePluginSettings(selectedPlugin.id, {
                                ...selectedPlugin.settings,
                                [key]: { ...setting, value: checked }
                              });
                            }}
                          />
                        ) : setting.type === 'string' ? (
                          <Input
                            id={key}
                            value={setting.value as string}
                            onChange={(e) => {
                              updatePluginSettings(selectedPlugin.id, {
                                ...selectedPlugin.settings,
                                [key]: { ...setting, value: e.target.value }
                              });
                            }}
                          />
                        ) : setting.type === 'number' ? (
                          <Input
                            id={key}
                            type="number"
                            value={setting.value as number}
                            onChange={(e) => {
                              updatePluginSettings(selectedPlugin.id, {
                                ...selectedPlugin.settings,
                                [key]: { ...setting, value: parseInt(e.target.value) }
                              });
                            }}
                          />
                        ) : null}
                        {setting.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {setting.description}
                          </p>
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            )}
            
            {selectedPlugin.changelog && (
              <div>
                <h3 className="font-semibold mb-2">Changelog</h3>
                <div className="space-y-2">
                  {selectedPlugin.changelog.map((entry, index) => (
                    <div key={index} className="flex gap-2">
                      <Badge variant="outline" className="shrink-0">
                        {entry.version}
                      </Badge>
                      <span className="text-sm">{entry.changes}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center gap-3">
          <Puzzle className="w-6 h-6" />
          <h2 className="text-2xl font-bold">Plugin Manager</h2>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          ×
        </Button>
      </div>
      
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search plugins..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="icon" onClick={loadPlugins}>
                <RefreshCw className="w-4 h-4" />
              </Button>
              {renderDeveloperDialog()}
            </div>
          </div>
          
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="installed">
                Installed ({installedPlugins.length})
              </TabsTrigger>
              <TabsTrigger value="available">
                Available ({availablePlugins.length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="installed" className="mt-6">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
                </div>
              ) : filteredPlugins.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Package className="w-12 h-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      {searchQuery ? 'No plugins found' : 'No plugins installed yet'}
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  <AnimatePresence>
                    {filteredPlugins.map(renderPluginCard)}
                  </AnimatePresence>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="available" className="mt-6">
              {loading ? (
                <div className="flex items-center justify-center py-12">
                  <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
                </div>
              ) : filteredPlugins.length === 0 ? (
                <Card>
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Package className="w-12 h-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No plugins available</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-4">
                  <AnimatePresence>
                    {filteredPlugins.map(renderPluginCard)}
                  </AnimatePresence>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
      
      {renderPluginDetails()}
    </div>
  );
}