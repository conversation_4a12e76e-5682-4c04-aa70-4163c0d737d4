import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Brain,
  Network,
  Zap,
  Users,
  Clock,
  Trophy,
  Eye,
  TrendingUp,
  Store,
  Layers,
  Wifi,
  Building,
  Coins,
  Rocket,
  AlertTriangle,
  CheckCircle,
  Sparkles
} from 'lucide-react';
import { 
  useAdvancedFeatureFlags, 
  applyFeatureFlagPreset,
  featureFlagPresets 
} from '@/lib/advancedFeatureFlags';

const categoryIcons = {
  adaptiveLearning: Brain,
  swarmIntelligence: Network,
  quantumOptimization: Zap,
  agentConsensus: Users,
  metaAgents: Layers,
  temporalControl: Clock,
  agentGaming: Trophy,
  visualUniverse: Eye,
  predictiveAnalytics: TrendingUp,
  agentMarketplace: Store,
  specializedClusters: Layers,
  realTimeFeatures: Wifi,
  enterpriseFeatures: Building,
  blockchainIntegration: Coins,
};

const categoryDescriptions = {
  adaptiveLearning: 'AI system that learns from execution patterns and optimizes performance',
  swarmIntelligence: 'Collective intelligence and emergent behaviors across agent networks',
  quantumOptimization: 'Quantum-inspired algorithms for complex optimization problems',
  agentConsensus: 'Democratic decision making and conflict resolution between agents',
  metaAgents: 'Agents that manage and orchestrate other agents',
  temporalControl: 'Time-travel debugging, rollback, and scheduled execution',
  agentGaming: 'Gamification elements including leaderboards and achievements',
  visualUniverse: '3D visualization, avatars, and immersive experiences',
  predictiveAnalytics: 'ML-powered forecasting and performance prediction',
  agentMarketplace: 'Community-driven agent sharing and discovery platform',
  specializedClusters: 'Domain-specific agent clusters for specialized tasks',
  realTimeFeatures: 'Live collaboration and real-time streaming capabilities',
  enterpriseFeatures: 'Multi-tenant architecture and enterprise governance',
  blockchainIntegration: 'Decentralized agent economy and smart contracts',
};

export function AdvancedFeatureFlagsSettings() {
  const { flags, updateFlags, getConfig } = useAdvancedFeatureFlags();
  const [activePreset, setActivePreset] = useState<string>('custom');

  const handleToggleCategory = (category: keyof typeof flags, enabled: boolean) => {
    updateFlags({
      [category]: {
        ...flags[category],
        enabled,
      },
    });
  };

  const handleToggleFeature = (
    category: keyof typeof flags,
    feature: string,
    enabled: boolean
  ) => {
    updateFlags({
      [category]: {
        ...flags[category],
        [feature]: enabled,
      },
    });
  };

  const handleSliderChange = (
    category: keyof typeof flags,
    feature: string,
    value: number[]
  ) => {
    updateFlags({
      [category]: {
        ...flags[category],
        [feature]: value[0],
      },
    });
  };

  const handlePresetChange = (preset: keyof typeof featureFlagPresets) => {
    applyFeatureFlagPreset(preset);
    setActivePreset(preset);
  };

  const getFeatureStatus = (category: keyof typeof flags) => {
    const config = getConfig(category);
    if (!config.enabled) return 'disabled';
    
    const features = Object.keys(config).filter(key => key !== 'enabled');
    const enabledFeatures = features.filter(key => config[key] === true);
    
    if (enabledFeatures.length === features.length) return 'full';
    if (enabledFeatures.length > 0) return 'partial';
    return 'minimal';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'full': return 'bg-green-500';
      case 'partial': return 'bg-yellow-500';
      case 'minimal': return 'bg-blue-500';
      case 'disabled': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };


  const renderCategoryCard = (category: keyof typeof flags) => {
    const Icon = categoryIcons[category];
    const config = getConfig(category);
    const status = getFeatureStatus(category);
    
    return (
      <Card key={category} className="relative">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg capitalize">
                {category.replace(/([A-Z])/g, ' $1').trim()}
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="flex items-center space-x-1">
                <div className={`w-2 h-2 rounded-full ${getStatusColor(status)}`} />
                <span className="text-xs capitalize">{status}</span>
              </Badge>
              <Switch
                checked={config.enabled}
                onCheckedChange={(enabled) => handleToggleCategory(category, enabled)}
              />
            </div>
          </div>
          <CardDescription className="text-sm">
            {categoryDescriptions[category]}
          </CardDescription>
        </CardHeader>
        
        {config.enabled && (
          <CardContent className="pt-0">
            <div className="space-y-3">
              {Object.entries(config).map(([key, value]) => {
                if (key === 'enabled') return null;
                
                if (typeof value === 'boolean') {
                  return (
                    <div key={key} className="flex items-center justify-between">
                      <Label htmlFor={`${category}-${key}`} className="text-sm capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </Label>
                      <Switch
                        id={`${category}-${key}`}
                        checked={value}
                        onCheckedChange={(enabled) => 
                          handleToggleFeature(category, key, enabled)
                        }
                      />
                    </div>
                  );
                }
                
                if (typeof value === 'number') {
                  const isPercentage = key.includes('Rate') || key.includes('Threshold');
                  const max = isPercentage ? 1 : key.includes('Size') ? 10000 : 300000;
                  const step = isPercentage ? 0.01 : key.includes('Size') ? 100 : 1000;
                  
                  return (
                    <div key={key} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </Label>
                        <span className="text-sm text-muted-foreground">
                          {isPercentage ? `${(value * 100).toFixed(0)}%` : value}
                        </span>
                      </div>
                      <Slider
                        value={[value]}
                        onValueChange={(newValue) => 
                          handleSliderChange(category, key, newValue)
                        }
                        max={max}
                        step={step}
                        className="w-full"
                      />
                    </div>
                  );
                }
                
                return null;
              })}
            </div>
          </CardContent>
        )}
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center space-x-2">
            <Sparkles className="h-6 w-6 text-primary" />
            <span>Advanced Parallel Agents</span>
          </h2>
          <p className="text-muted-foreground mt-1">
            Configure cutting-edge AI agent features and capabilities
          </p>
        </div>
      </div>

      {/* Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Rocket className="h-5 w-5" />
            <span>Feature Presets</span>
          </CardTitle>
          <CardDescription>
            Quick configurations for different use cases
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {Object.keys(featureFlagPresets).map((preset) => (
              <Button
                key={preset}
                variant={activePreset === preset ? "default" : "outline"}
                onClick={() => handlePresetChange(preset as keyof typeof featureFlagPresets)}
                className="capitalize"
              >
                {preset}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Feature Categories */}
      <Tabs defaultValue="core" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="core">Core Intelligence</TabsTrigger>
          <TabsTrigger value="advanced">Advanced Features</TabsTrigger>
          <TabsTrigger value="visualization">Visualization</TabsTrigger>
          <TabsTrigger value="enterprise">Enterprise</TabsTrigger>
        </TabsList>

        <TabsContent value="core" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {renderCategoryCard('adaptiveLearning')}
            {renderCategoryCard('swarmIntelligence')}
            {renderCategoryCard('agentConsensus')}
            {renderCategoryCard('metaAgents')}
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {renderCategoryCard('quantumOptimization')}
            {renderCategoryCard('temporalControl')}
            {renderCategoryCard('predictiveAnalytics')}
            {renderCategoryCard('specializedClusters')}
          </div>
        </TabsContent>

        <TabsContent value="visualization" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {renderCategoryCard('visualUniverse')}
            {renderCategoryCard('agentGaming')}
            {renderCategoryCard('agentMarketplace')}
            {renderCategoryCard('realTimeFeatures')}
          </div>
        </TabsContent>

        <TabsContent value="enterprise" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {renderCategoryCard('enterpriseFeatures')}
            {renderCategoryCard('blockchainIntegration')}
          </div>
          
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Enterprise and Blockchain features are experimental and may require additional setup.
              Some features are only available in premium versions.
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
          <CardDescription>
            Overview of enabled features and system capabilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.keys(flags).map((category) => {
              const status = getFeatureStatus(category as keyof typeof flags);
                        
              return (
                <div key={category} className="flex items-center space-x-2">
                  <CheckCircle className={`h-4 w-4 ${
                    status === 'full' ? 'text-green-500' :
                    status === 'partial' ? 'text-yellow-500' :
                    status === 'minimal' ? 'text-blue-500' :
                    'text-gray-500'
                  }`} />
                  <span className="text-sm capitalize">
                    {category.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}