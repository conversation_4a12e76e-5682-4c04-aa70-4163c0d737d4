import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Download, 
  ExternalLink, 
  Star, 
  CheckCircle,
  Loader2,
  Globe,
  Database,
  Code,
  Cloud,
  Bot,
  Shield,
  Zap,
  Package,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { api, type MCPServer } from '@/lib/api';

// Marketplace server interface
export interface MarketplaceServer {
  name: string;
  description: string;
  category: string;
  author?: string;
  featured?: boolean;
  verified?: boolean;
  popularity?: number;
  capabilities?: string[];
  transport: 'stdio' | 'sse';
  installation: {
    command?: string;
    args?: string[];
    env?: Record<string, string>;
    url?: string;
    npm?: string;
    pip?: string;
    requirements?: string[];
  };
  documentation?: string;
  repository?: string;
  license?: string;
  tags?: string[];
}

// Category configuration
const CATEGORIES = [
  { id: 'all', name: 'All', icon: Package },
  { id: 'web-data', name: 'Web Data', icon: Globe },
  { id: 'ai-tools', name: 'AI Tools', icon: Bot },
  { id: 'cloud-services', name: 'Cloud Services', icon: Cloud },
  { id: 'databases', name: 'Databases', icon: Database },
  { id: 'development', name: 'Development', icon: Code },
  { id: 'security', name: 'Security', icon: Shield },
  { id: 'automation', name: 'Automation', icon: Zap },
];

interface MCPMarketplaceProps {
  installedServers: MCPServer[];
  onInstall: (server: MarketplaceServer) => Promise<void>;
  onError: (message: string) => void;
}

export const MCPMarketplace: React.FC<MCPMarketplaceProps> = ({
  installedServers,
  onInstall,
  onError
}) => {
  const [marketplaceServers, setMarketplaceServers] = useState<MarketplaceServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedServer, setSelectedServer] = useState<MarketplaceServer | null>(null);
  const [installing, setInstalling] = useState<string | null>(null);

  // Load marketplace data
  useEffect(() => {
    loadMarketplaceData();
  }, []);

  const loadMarketplaceData = async (forceLive: boolean = false) => {
    try {
      setLoading(true);
      // Fetch marketplace data using the new API method
      const servers = await api.fetchMCPMarketplace(forceLive);
      setMarketplaceServers(servers);
    } catch (error) {
      console.error('Failed to load marketplace data:', error);
      onError('Failed to load MCP marketplace data');
      // Use sample data as fallback
      setMarketplaceServers(getSampleMarketplaceData());
    } finally {
      setLoading(false);
    }
  };

  // Parse marketplace data from API response
  const parseMarketplaceData = (data: string): MarketplaceServer[] => {
    try {
      // Extract JSON from the response
      const jsonMatch = data.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.error('Failed to parse marketplace data:', error);
    }
    return getSampleMarketplaceData();
  };

  // Enhanced marketplace data with comprehensive MCP servers
  const getSampleMarketplaceData = (): MarketplaceServer[] => [
    {
      name: 'filesystem',
      description: 'Secure file system operations with read/write access to local directories.',
      category: 'development',
      featured: true,
      verified: true,
      popularity: 98,
      capabilities: ['file-operations', 'directory-management', 'text-editing'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-filesystem',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-filesystem', '/path/to/allowed/directory']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['filesystem', 'files', 'local-access']
    },
    {
      name: 'github-mcp',
      description: 'Comprehensive GitHub integration for repository management, issues, PRs, and more.',
      category: 'development',
      featured: true,
      verified: true,
      popularity: 96,
      capabilities: ['repository-management', 'issue-tracking', 'pull-requests', 'search'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-github',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-github'],
        env: {
          GITHUB_PERSONAL_ACCESS_TOKEN: '<your-github-token>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/github',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['github', 'git', 'version-control', 'collaboration']
    },
    {
      name: 'brave-search',
      description: 'Web search capabilities using Brave Search API for real-time information retrieval.',
      category: 'web-data',
      featured: true,
      verified: true,
      popularity: 94,
      capabilities: ['web-search', 'real-time-data', 'information-retrieval'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-brave-search',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-brave-search'],
        env: {
          BRAVE_API_KEY: '<your-brave-api-key>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/brave-search',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['search', 'web', 'brave', 'real-time']
    },
    {
      name: 'puppeteer',
      description: 'Browser automation and web scraping with Puppeteer for dynamic content extraction.',
      category: 'web-data',
      featured: true,
      verified: true,
      popularity: 92,
      capabilities: ['browser-automation', 'web-scraping', 'screenshot', 'pdf-generation'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-puppeteer',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-puppeteer']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/puppeteer',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['puppeteer', 'automation', 'scraping', 'browser']
    },
    {
      name: 'sqlite',
      description: 'SQLite database operations with query execution and schema management.',
      category: 'databases',
      verified: true,
      popularity: 90,
      capabilities: ['sql-queries', 'database-management', 'schema-operations'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-sqlite',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-sqlite', '/path/to/database.db']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/sqlite',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['sqlite', 'database', 'sql', 'local-db']
    },
    {
      name: 'postgres',
      description: 'PostgreSQL database integration with advanced query capabilities.',
      category: 'databases',
      verified: true,
      popularity: 89,
      capabilities: ['sql-queries', 'database-management', 'advanced-features'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-postgres',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-postgres'],
        env: {
          POSTGRES_CONNECTION_STRING: '<your-connection-string>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/postgres',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['postgresql', 'database', 'sql', 'enterprise']
    },
    {
      name: 'google-drive',
      description: 'Google Drive integration for file management and document operations.',
      category: 'cloud-services',
      verified: true,
      popularity: 88,
      capabilities: ['file-management', 'document-operations', 'sharing'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-gdrive',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-gdrive'],
        env: {
          GOOGLE_DRIVE_CREDENTIALS: '<your-service-account-json>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/gdrive',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['google-drive', 'cloud-storage', 'documents']
    },
    {
      name: 'slack',
      description: 'Slack workspace integration for messaging and channel management.',
      category: 'automation',
      verified: true,
      popularity: 87,
      capabilities: ['messaging', 'channel-management', 'user-management'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-slack',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-slack'],
        env: {
          SLACK_BOT_TOKEN: '<your-bot-token>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/slack',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['slack', 'communication', 'team-collaboration']
    },
    {
      name: 'memory',
      description: 'Persistent memory storage for maintaining context across conversations.',
      category: 'ai-tools',
      featured: true,
      verified: true,
      popularity: 95,
      capabilities: ['persistent-memory', 'context-management', 'knowledge-storage'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-memory',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-memory']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/memory',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['memory', 'context', 'persistence', 'ai']
    },
    {
      name: 'everart',
      description: 'AI-powered image generation and manipulation using EverArt API.',
      category: 'ai-tools',
      verified: true,
      popularity: 85,
      capabilities: ['image-generation', 'ai-art', 'style-transfer'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-everart',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-everart'],
        env: {
          EVERART_API_KEY: '<your-everart-api-key>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/everart',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['ai-art', 'image-generation', 'creative']
    },
    {
      name: 'bright-data',
      description: 'Discover, extract, and interact with the web - one interface powering automated access across the public internet.',
      category: 'web-data',
      featured: true,
      verified: true,
      popularity: 95,
      capabilities: ['web-scraping', 'data-extraction', 'automation'],
      transport: 'stdio',
      installation: {
        npm: '@brightdata/mcp-server',
        command: 'npx',
        args: ['@brightdata/mcp-server'],
        env: {
          BRIGHT_DATA_API_KEY: '<your-api-key>'
        }
      },
      documentation: 'https://docs.brightdata.com/mcp',
      repository: 'https://github.com/brightdata/mcp-server',
      tags: ['web-scraping', 'proxy', 'data-collection']
    },
    {
      name: 'agentql',
      description: 'Enable AI agents to get structured data from unstructured web.',
      category: 'web-data',
      verified: true,
      popularity: 88,
      capabilities: ['web-parsing', 'data-structuring', 'ai-integration'],
      transport: 'stdio',
      installation: {
        pip: 'agentql-mcp',
        command: 'python',
        args: ['-m', 'agentql_mcp'],
        env: {
          AGENTQL_API_KEY: '<your-api-key>'
        }
      },
      documentation: 'https://docs.agentql.com',
      tags: ['web-data', 'ai', 'parsing']
    },
    {
      name: 'aws-bedrock-kb',
      description: 'Query Amazon Bedrock Knowledge Bases using natural language to retrieve relevant information.',
      category: 'cloud-services',
      verified: true,
      popularity: 92,
      capabilities: ['knowledge-base', 'rag', 'aws-integration'],
      transport: 'stdio',
      installation: {
        npm: '@aws/bedrock-kb-mcp',
        command: 'node',
        args: ['node_modules/@aws/bedrock-kb-mcp/dist/index.js'],
        env: {
          AWS_REGION: 'us-east-1',
          AWS_ACCESS_KEY_ID: '<your-access-key>',
          AWS_SECRET_ACCESS_KEY: '<your-secret-key>'
        }
      },
      documentation: 'https://aws.amazon.com/bedrock',
      tags: ['aws', 'knowledge-base', 'rag']
    },
    {
      name: 'apify',
      description: 'Use 3,000+ pre-built cloud tools to extract data from websites, e-commerce, social media, search engines.',
      category: 'web-data',
      featured: true,
      verified: true,
      popularity: 90,
      capabilities: ['web-scraping', 'automation', 'data-extraction'],
      transport: 'sse',
      installation: {
        url: 'https://api.apify.com/v2/mcp',
        env: {
          APIFY_API_TOKEN: '<your-api-token>'
        }
      },
      documentation: 'https://docs.apify.com/mcp',
      tags: ['web-scraping', 'automation', 'cloud']
    },
    {
      name: 'github',
      description: 'Interact with GitHub repositories, issues, pull requests, and more.',
      category: 'development',
      verified: true,
      popularity: 94,
      capabilities: ['repository-management', 'issue-tracking', 'ci-cd'],
      transport: 'stdio',
      installation: {
        npm: '@github/mcp-server',
        command: 'node',
        args: ['node_modules/@github/mcp-server/dist/index.js'],
        env: {
          GITHUB_TOKEN: '<your-github-token>'
        }
      },
      documentation: 'https://docs.github.com/mcp',
      repository: 'https://github.com/github/mcp-server',
      tags: ['github', 'development', 'version-control']
    },
    {
      name: 'openai',
      description: 'Connect to OpenAI GPT models and assistants for AI-powered workflows.',
      category: 'ai-tools',
      featured: true,
      verified: true,
      popularity: 96,
      capabilities: ['ai-generation', 'chat', 'embeddings'],
      transport: 'stdio',
      installation: {
        npm: '@openai/mcp-server',
        command: 'node',
        args: ['node_modules/@openai/mcp-server/dist/index.js'],
        env: {
          OPENAI_API_KEY: '<your-openai-api-key>'
        }
      },
      documentation: 'https://platform.openai.com/docs',
      tags: ['ai', 'gpt', 'llm']
    },
    {
      name: 'postgres',
      description: 'Execute queries and manage PostgreSQL databases.',
      category: 'databases',
      verified: true,
      popularity: 89,
      capabilities: ['sql-queries', 'database-management', 'data-export'],
      transport: 'stdio',
      installation: {
        npm: '@postgres/mcp-server',
        command: 'node',
        args: ['node_modules/@postgres/mcp-server/dist/index.js'],
        env: {
          POSTGRES_CONNECTION_STRING: '<your-connection-string>'
        }
      },
      documentation: 'https://www.postgresql.org/docs/',
      tags: ['database', 'sql', 'postgres']
    },
    {
      name: 'docker',
      description: 'Manage Docker containers, images, and compose stacks.',
      category: 'development',
      verified: true,
      popularity: 87,
      capabilities: ['container-management', 'image-building', 'compose'],
      transport: 'stdio',
      installation: {
        npm: '@docker/mcp-server',
        command: 'node',
        args: ['node_modules/@docker/mcp-server/dist/index.js']
      },
      documentation: 'https://docs.docker.com',
      tags: ['docker', 'containers', 'devops']
    },
    {
      name: 'slack',
      description: 'Send messages and interact with Slack workspaces.',
      category: 'automation',
      verified: true,
      popularity: 85,
      capabilities: ['messaging', 'channel-management', 'file-sharing'],
      transport: 'sse',
      installation: {
        url: 'https://slack.com/api/mcp',
        env: {
          SLACK_BOT_TOKEN: '<your-bot-token>',
          SLACK_APP_TOKEN: '<your-app-token>'
        }
      },
      documentation: 'https://api.slack.com',
      tags: ['communication', 'messaging', 'automation']
    },
    {
      name: 'stripe',
      description: 'Process payments and manage Stripe accounts.',
      category: 'automation',
      verified: true,
      popularity: 82,
      capabilities: ['payment-processing', 'subscription-management', 'invoicing'],
      transport: 'stdio',
      installation: {
        npm: '@stripe/mcp-server',
        command: 'node',
        args: ['node_modules/@stripe/mcp-server/dist/index.js'],
        env: {
          STRIPE_SECRET_KEY: '<your-secret-key>'
        }
      },
      documentation: 'https://stripe.com/docs',
      tags: ['payments', 'fintech', 'automation']
    },
    {
      name: 'sequential-thinking',
      description: 'Advanced reasoning and step-by-step problem solving for complex tasks.',
      category: 'ai-tools',
      featured: true,
      verified: true,
      popularity: 93,
      capabilities: ['reasoning', 'problem-solving', 'step-by-step-analysis'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-sequential-thinking',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-sequential-thinking']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/sequential-thinking',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['reasoning', 'thinking', 'analysis', 'ai']
    },
    {
      name: 'fetch',
      description: 'HTTP client for making web requests and API calls with comprehensive options.',
      category: 'web-data',
      verified: true,
      popularity: 91,
      capabilities: ['http-requests', 'api-calls', 'web-content'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-fetch',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-fetch']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/fetch',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['http', 'fetch', 'api', 'web-requests']
    },
    {
      name: 'kubernetes',
      description: 'Kubernetes cluster management and resource operations.',
      category: 'cloud-services',
      verified: true,
      popularity: 86,
      capabilities: ['cluster-management', 'pod-operations', 'deployment'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-kubernetes',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-kubernetes'],
        env: {
          KUBECONFIG: '<path-to-kubeconfig>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/kubernetes',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['kubernetes', 'k8s', 'containers', 'orchestration']
    },
    {
      name: 'docker',
      description: 'Docker container and image management with comprehensive operations.',
      category: 'development',
      verified: true,
      popularity: 87,
      capabilities: ['container-management', 'image-operations', 'compose'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-docker',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-docker']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/docker',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['docker', 'containers', 'devops', 'virtualization']
    },
    {
      name: 'git',
      description: 'Git version control operations for repository management.',
      category: 'development',
      verified: true,
      popularity: 89,
      capabilities: ['version-control', 'repository-operations', 'branch-management'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-git',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-git']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/git',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['git', 'version-control', 'repository', 'source-control']
    },
    {
      name: 'aws-kb',
      description: 'Amazon Bedrock Knowledge Base integration for RAG applications.',
      category: 'cloud-services',
      verified: true,
      popularity: 84,
      capabilities: ['knowledge-base', 'rag', 'aws-integration', 'semantic-search'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-aws-kb',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-aws-kb'],
        env: {
          AWS_REGION: '<your-aws-region>',
          AWS_ACCESS_KEY_ID: '<your-access-key>',
          AWS_SECRET_ACCESS_KEY: '<your-secret-key>'
        }
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/aws-kb',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['aws', 'bedrock', 'knowledge-base', 'rag']
    },
    {
      name: 'time',
      description: 'Time and date operations with timezone support and scheduling.',
      category: 'automation',
      verified: true,
      popularity: 82,
      capabilities: ['time-operations', 'timezone-conversion', 'scheduling'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-time',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-time']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/time',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['time', 'date', 'timezone', 'scheduling']
    },
    {
      name: 'everything',
      description: 'Windows Everything search integration for fast file system searches.',
      category: 'development',
      verified: true,
      popularity: 75,
      capabilities: ['file-search', 'fast-indexing', 'windows-integration'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-everything',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-everything']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/everything',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['search', 'files', 'windows', 'indexing']
    },
    {
      name: 'obsidian-vault',
      description: 'Obsidian vault integration for note management and knowledge graphs.',
      category: 'development',
      verified: true,
      popularity: 83,
      capabilities: ['note-management', 'knowledge-graph', 'markdown'],
      transport: 'stdio',
      installation: {
        npm: '@modelcontextprotocol/server-obsidian-vault',
        command: 'npx',
        args: ['-y', '@modelcontextprotocol/server-obsidian-vault', '/path/to/vault']
      },
      documentation: 'https://github.com/modelcontextprotocol/servers/tree/main/src/obsidian-vault',
      repository: 'https://github.com/modelcontextprotocol/servers',
      tags: ['obsidian', 'notes', 'knowledge-management', 'markdown']
    },
    {
      name: 'vault',
      description: 'HashiCorp Vault integration for secrets management and encryption.',
      category: 'security',
      verified: true,
      popularity: 78,
      capabilities: ['secret-management', 'encryption', 'authentication'],
      transport: 'stdio',
      installation: {
        command: 'vault',
        args: ['mcp-server'],
        env: {
          VAULT_ADDR: '<your-vault-address>',
          VAULT_TOKEN: '<your-vault-token>'
        }
      },
      documentation: 'https://www.vaultproject.io/docs',
      tags: ['security', 'secrets', 'encryption', 'vault']
    }
  ];

  // Filter servers based on search and category
  const filteredServers = useMemo(() => {
    return marketplaceServers.filter(server => {
      const matchesSearch = searchQuery === '' || 
        server.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        server.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        server.tags?.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || server.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }, [marketplaceServers, searchQuery, selectedCategory]);

  // Check if server is installed
  const isInstalled = (serverName: string) => {
    return installedServers.some(s => s.name === serverName);
  };

  // Handle server installation
  const handleInstall = async (server: MarketplaceServer) => {
    try {
      setInstalling(server.name);
      await onInstall(server);
    } catch (error) {
      console.error('Failed to install server:', error);
      onError(`Failed to install ${server.name}`);
    } finally {
      setInstalling(null);
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    const cat = CATEGORIES.find(c => c.id === category);
    return cat ? cat.icon : Package;
  };

  // Render server card
  const renderServerCard = (server: MarketplaceServer) => {
    const installed = isInstalled(server.name);
    const isInstalling = installing === server.name;
    const IconComponent = getCategoryIcon(server.category);

    return (
      <motion.div
        key={server.name}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        whileHover={{ scale: 1.02 }}
        className="cursor-pointer"
        onClick={() => setSelectedServer(server)}
      >
        <Card className="h-full hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <div className="p-3 rounded-lg bg-primary/10 flex-shrink-0">
                <IconComponent className="w-6 h-6 text-primary" />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-lg">{server.name}</h3>
                  <div className="flex items-center gap-2">
                    {server.featured && (
                      <Badge variant="default" className="gap-1">
                        <Star className="w-3 h-3" />
                        Featured
                      </Badge>
                    )}
                    {server.verified && (
                      <Badge variant="secondary" className="gap-1">
                        <CheckCircle className="w-3 h-3" />
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2 overflow-hidden">
                  {server.description}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {server.tags?.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  {installed ? (
                    <Badge variant="secondary" className="gap-1">
                      <CheckCircle className="w-3 h-3" />
                      Installed
                    </Badge>
                  ) : (
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleInstall(server);
                      }}
                      disabled={isInstalling}
                    >
                      {isInstalling ? (
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      ) : (
                        <Download className="w-4 h-4 mr-2" />
                      )}
                      Install
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  // Render server details dialog
  const renderServerDetails = () => {
    if (!selectedServer) return null;

    const installed = isInstalled(selectedServer.name);
    const IconComponent = getCategoryIcon(selectedServer.category);

    return (
      <Dialog open={!!selectedServer} onOpenChange={() => setSelectedServer(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0 pb-4 border-b">
            <div className="flex items-start gap-4">
              <div className="p-3 rounded-lg bg-primary/10 flex-shrink-0">
                <IconComponent className="w-8 h-8 text-primary" />
              </div>
              <div className="flex-1">
                <DialogTitle className="text-2xl">{selectedServer.name}</DialogTitle>
                <DialogDescription className="text-base mt-1">
                  {selectedServer.description}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto px-6 py-4">
            <div className="space-y-6">
            {/* Badges */}
            <div className="flex items-center gap-2">
              {selectedServer.featured && (
                <Badge variant="default" className="gap-1">
                  <Star className="w-3 h-3" />
                  Featured
                </Badge>
              )}
              {selectedServer.verified && (
                <Badge variant="secondary" className="gap-1">
                  <CheckCircle className="w-3 h-3" />
                  Verified
                </Badge>
              )}
              <Badge variant="outline">{selectedServer.transport.toUpperCase()}</Badge>
              {selectedServer.popularity && (
                <Badge variant="outline" className="gap-1">
                  <Star className="w-3 h-3" />
                  {selectedServer.popularity}% Popular
                </Badge>
              )}
            </div>

            {/* Author and Links */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              {selectedServer.author && <span>by {selectedServer.author}</span>}
              {selectedServer.repository && (
                <a
                  href={selectedServer.repository}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-primary hover:underline"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ExternalLink className="w-3 h-3" />
                  Repository
                </a>
              )}
              {selectedServer.documentation && (
                <a
                  href={selectedServer.documentation}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 text-primary hover:underline"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ExternalLink className="w-3 h-3" />
                  Documentation
                </a>
              )}
            </div>

            {/* Capabilities */}
            {selectedServer.capabilities && selectedServer.capabilities.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Capabilities</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedServer.capabilities.map(cap => (
                    <Badge key={cap} variant="secondary">
                      {cap}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Installation */}
            <div>
              <h3 className="font-semibold mb-2">Installation</h3>
              <Card className="p-4 bg-muted/50">
                <code className="text-sm">
                  {selectedServer.installation.npm && (
                    <div>npm install {selectedServer.installation.npm}</div>
                  )}
                  {selectedServer.installation.pip && (
                    <div>pip install {selectedServer.installation.pip}</div>
                  )}
                  {selectedServer.installation.command && (
                    <div className="mt-2">
                      Command: {selectedServer.installation.command} {selectedServer.installation.args?.join(' ')}
                    </div>
                  )}
                  {selectedServer.installation.url && (
                    <div className="mt-2">URL: {selectedServer.installation.url}</div>
                  )}
                </code>
              </Card>
            </div>

            {/* Environment Variables */}
            {selectedServer.installation.env && Object.keys(selectedServer.installation.env).length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Required Environment Variables</h3>
                <Card className="p-4 bg-muted/50">
                  <code className="text-sm">
                    {Object.entries(selectedServer.installation.env).map(([key, value]) => (
                      <div key={key}>{key}={value}</div>
                    ))}
                  </code>
                </Card>
              </div>
            )}

            {/* Tags */}
            {selectedServer.tags && selectedServer.tags.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedServer.tags.map(tag => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

              {/* Actions */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button variant="outline" onClick={() => setSelectedServer(null)}>
                  Close
                </Button>
                {!installed && (
                  <Button
                    onClick={() => {
                      handleInstall(selectedServer);
                      setSelectedServer(null);
                    }}
                    disabled={installing === selectedServer.name}
                  >
                    {installing === selectedServer.name ? (
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                      <Download className="w-4 h-4 mr-2" />
                    )}
                    Install Server
                  </Button>
                )}
            </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-xl font-semibold">MCP Server Marketplace</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Browse and install MCP servers from mcpservers.org
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadMarketplaceData(false)}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => loadMarketplaceData(true)}
            disabled={loading}
            title="Fetch live data from mcpservers.org"
          >
            <Globe className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Live Data
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search servers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="flex flex-wrap h-auto p-1 gap-1">
          {CATEGORIES.map(category => {
            const Icon = category.icon;
            return (
              <TabsTrigger 
                key={category.id} 
                value={category.id} 
                className="flex items-center gap-2 px-3 py-2 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{category.name}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>
      </Tabs>

      {/* Content */}
      {loading ? (
        <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="h-full">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 rounded-lg bg-muted animate-pulse" />
                  <div className="flex-1 space-y-3">
                    <div className="h-5 bg-muted rounded animate-pulse" />
                    <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
                    <div className="flex gap-2">
                      <div className="h-6 w-16 bg-muted rounded animate-pulse" />
                      <div className="h-6 w-20 bg-muted rounded animate-pulse" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredServers.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Package className="w-12 h-12 text-muted-foreground mb-4" />
            <p className="text-muted-foreground text-center">
              {searchQuery ? 'No servers found matching your search' : 'No servers available in this category'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          <AnimatePresence>
            {filteredServers.map(renderServerCard)}
          </AnimatePresence>
        </div>
      )}

      {/* Server Details Dialog */}
      {renderServerDetails()}
    </div>
  );
};