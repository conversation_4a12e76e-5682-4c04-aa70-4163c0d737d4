import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Settings2,
  Palette,
  Moon,
  Sun,
  Monitor,
  Zap,
  Eye,
  EyeOff,
  Layout,
  Code2,
  Sparkles,
  X,
  ChevronRight,
  ChevronLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuickCustomizationProps {
  defaultPosition?: 'left' | 'right';
  defaultOpen?: boolean;
}

export function QuickCustomization({ 
  defaultPosition = 'right',
  defaultOpen = false 
}: QuickCustomizationProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [position, setPosition] = useState(defaultPosition);
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system');
  const [accentColor, setAccentColor] = useState('blue');
  const [fontSize, setFontSize] = useState(16);
  const [compactMode, setCompactMode] = useState(false);
  const [animations, setAnimations] = useState(true);
  const [focusMode, setFocusMode] = useState(false);
  const [codeTheme, setCodeTheme] = useState('github-dark');
  const [aiAssistLevel, setAiAssistLevel] = useState(50);

  // Apply customizations to the document
  const applyCustomizations = (prefs: any) => {
    const root = document.documentElement;
    
    // Theme - Remove both classes first, then add the appropriate one
    root.classList.remove('light', 'dark');
    
    if (prefs.theme === 'dark') {
      root.classList.add('dark');
    } else if (prefs.theme === 'light') {
      root.classList.add('light');
    } else {
      // System preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.classList.add(prefersDark ? 'dark' : 'light');
    }
    
    // Font size
    root.style.setProperty('--base-font-size', `${prefs.fontSize}px`);
    
    // Compact mode
    if (prefs.compactMode) {
      root.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
    }
    
    // Animations
    if (!prefs.animations) {
      root.classList.add('no-animations');
    } else {
      root.classList.remove('no-animations');
    }
    
    // Focus mode
    if (prefs.focusMode) {
      root.classList.add('focus-mode');
    } else {
      root.classList.remove('focus-mode');
    }
    
    // Accent color
    const accentColors: Record<string, string> = {
      blue: '217 91% 60%',
      purple: '263 70% 50%',
      green: '142 71% 45%',
      red: '0 84% 60%',
      orange: '25 95% 53%',
      pink: '340 82% 52%'
    };
    root.style.setProperty('--accent', accentColors[prefs.accentColor] || accentColors.blue);
  };

  // Load preferences from localStorage
  useEffect(() => {
    const savedPrefs = localStorage.getItem('quick-customization');
    if (savedPrefs) {
      try {
        const prefs = JSON.parse(savedPrefs);
        setTheme(prefs.theme || 'system');
        setAccentColor(prefs.accentColor || 'blue');
        setFontSize(prefs.fontSize || 16);
        setCompactMode(prefs.compactMode || false);
        setAnimations(prefs.animations !== false);
        setFocusMode(prefs.focusMode || false);
        setCodeTheme(prefs.codeTheme || 'github-dark');
        setAiAssistLevel(prefs.aiAssistLevel || 50);
        
        // Apply the loaded preferences immediately
        applyCustomizations(prefs);
      } catch (e) {
        console.error('Failed to load customization preferences');
      }
    } else {
      // Apply default preferences if none saved
      const defaultPrefs = {
        theme: 'system',
        accentColor: 'blue',
        fontSize: 16,
        compactMode: false,
        animations: true,
        focusMode: false,
        codeTheme: 'github-dark',
        aiAssistLevel: 50
      };
      applyCustomizations(defaultPrefs);
    }
  }, []);

  // Save preferences
  const savePreferences = () => {
    const prefs = {
      theme,
      accentColor,
      fontSize,
      compactMode,
      animations,
      focusMode,
      codeTheme,
      aiAssistLevel
    };
    localStorage.setItem('quick-customization', JSON.stringify(prefs));
    
    // Apply changes
    applyCustomizations(prefs);
  };

  const accentColors = [
    { value: 'blue', label: 'Blue', color: 'bg-blue-500' },
    { value: 'purple', label: 'Purple', color: 'bg-purple-500' },
    { value: 'green', label: 'Green', color: 'bg-green-500' },
    { value: 'red', label: 'Red', color: 'bg-red-500' },
    { value: 'orange', label: 'Orange', color: 'bg-orange-500' },
    { value: 'pink', label: 'Pink', color: 'bg-pink-500' }
  ];

  const codeThemes = [
    { value: 'github-dark', label: 'GitHub Dark' },
    { value: 'monokai', label: 'Monokai' },
    { value: 'dracula', label: 'Dracula' },
    { value: 'solarized-dark', label: 'Solarized Dark' },
    { value: 'vs-code', label: 'VS Code' },
    { value: 'atom-one-dark', label: 'Atom One Dark' }
  ];

  return (
    <>
      {/* Floating Toggle Button */}
      <motion.button
        className={cn(
          "fixed top-20 z-50 p-3 rounded-full bg-primary text-primary-foreground shadow-lg hover:shadow-xl transition-all",
          position === 'right' ? 'right-0 rounded-r-none' : 'left-0 rounded-l-none'
        )}
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          {isOpen ? (
            position === 'right' ? <ChevronRight className="w-5 h-5" /> : <ChevronLeft className="w-5 h-5" />
          ) : (
            <Settings2 className="w-5 h-5" />
          )}
        </motion.div>
      </motion.button>

      {/* Customization Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: position === 'right' ? 400 : -400, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: position === 'right' ? 400 : -400, opacity: 0 }}
            transition={{ type: 'spring', damping: 20 }}
            className={cn(
              "fixed top-16 bottom-16 w-96 z-40 shadow-2xl",
              position === 'right' ? 'right-0' : 'left-0'
            )}
          >
            <Card className="h-full flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Sparkles className="w-5 h-5" />
                      Quick Customization
                    </CardTitle>
                    <CardDescription className="text-xs mt-1">
                      Personalize your experience
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setPosition(position === 'right' ? 'left' : 'right')}
                    >
                      {position === 'right' ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setIsOpen(false)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="flex-1 overflow-y-auto">
                <Tabs defaultValue="appearance" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="appearance" className="text-xs">Appearance</TabsTrigger>
                    <TabsTrigger value="interface" className="text-xs">Interface</TabsTrigger>
                    <TabsTrigger value="ai" className="text-xs">AI Settings</TabsTrigger>
                  </TabsList>

                  <TabsContent value="appearance" className="space-y-6 mt-4">
                    {/* Theme Selection */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Theme</Label>
                      <div className="grid grid-cols-3 gap-2">
                        <Button
                          variant={theme === 'light' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => {
                            setTheme('light');
                            // Apply immediately
                            applyCustomizations({ ...{ theme, accentColor, fontSize, compactMode, animations, focusMode, codeTheme, aiAssistLevel }, theme: 'light' });
                          }}
                          className="flex items-center gap-2"
                        >
                          <Sun className="w-4 h-4" />
                          Light
                        </Button>
                        <Button
                          variant={theme === 'dark' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => {
                            setTheme('dark');
                            // Apply immediately
                            applyCustomizations({ ...{ theme, accentColor, fontSize, compactMode, animations, focusMode, codeTheme, aiAssistLevel }, theme: 'dark' });
                          }}
                          className="flex items-center gap-2"
                        >
                          <Moon className="w-4 h-4" />
                          Dark
                        </Button>
                        <Button
                          variant={theme === 'system' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => {
                            setTheme('system');
                            // Apply immediately
                            applyCustomizations({ ...{ theme, accentColor, fontSize, compactMode, animations, focusMode, codeTheme, aiAssistLevel }, theme: 'system' });
                          }}
                          className="flex items-center gap-2"
                        >
                          <Monitor className="w-4 h-4" />
                          System
                        </Button>
                      </div>
                    </div>

                    {/* Accent Color */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Accent Color</Label>
                      <div className="grid grid-cols-3 gap-2">
                        {accentColors.map((color) => (
                          <button
                            key={color.value}
                            onClick={() => setAccentColor(color.value)}
                            className={cn(
                              "h-10 rounded-md border-2 transition-all flex items-center justify-center gap-2",
                              accentColor === color.value ? 'border-primary' : 'border-transparent'
                            )}
                          >
                            <div className={cn("w-4 h-4 rounded-full", color.color)} />
                            <span className="text-xs">{color.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Font Size */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Font Size</Label>
                        <span className="text-xs text-muted-foreground">{fontSize}px</span>
                      </div>
                      <Slider
                        value={[fontSize]}
                        onValueChange={([value]) => setFontSize(value)}
                        min={12}
                        max={20}
                        step={1}
                        className="w-full"
                      />
                    </div>

                    {/* Code Theme */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Code Editor Theme</Label>
                      <Select value={codeTheme} onValueChange={setCodeTheme}>
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {codeThemes.map((theme) => (
                            <SelectItem key={theme.value} value={theme.value}>
                              {theme.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </TabsContent>

                  <TabsContent value="interface" className="space-y-6 mt-4">
                    {/* Compact Mode */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Compact Mode</Label>
                        <p className="text-xs text-muted-foreground">Reduce spacing and padding</p>
                      </div>
                      <Switch
                        checked={compactMode}
                        onCheckedChange={setCompactMode}
                      />
                    </div>

                    {/* Animations */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Animations</Label>
                        <p className="text-xs text-muted-foreground">Enable motion and transitions</p>
                      </div>
                      <Switch
                        checked={animations}
                        onCheckedChange={setAnimations}
                      />
                    </div>

                    {/* Focus Mode */}
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <Label className="text-sm font-medium">Focus Mode</Label>
                        <p className="text-xs text-muted-foreground">Hide non-essential UI elements</p>
                      </div>
                      <Switch
                        checked={focusMode}
                        onCheckedChange={setFocusMode}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="ai" className="space-y-6 mt-4">
                    {/* AI Assist Level */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">AI Assistance Level</Label>
                        <span className="text-xs text-muted-foreground">{aiAssistLevel}%</span>
                      </div>
                      <Slider
                        value={[aiAssistLevel]}
                        onValueChange={([value]) => setAiAssistLevel(value)}
                        min={0}
                        max={100}
                        step={10}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Minimal</span>
                        <span>Balanced</span>
                        <span>Maximum</span>
                      </div>
                    </div>

                    {/* AI Features */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">AI Features</Label>
                      <div className="space-y-2">
                        <label className="flex items-center gap-2 text-sm">
                          <input type="checkbox" className="rounded" defaultChecked />
                          Smart code suggestions
                        </label>
                        <label className="flex items-center gap-2 text-sm">
                          <input type="checkbox" className="rounded" defaultChecked />
                          Auto-documentation
                        </label>
                        <label className="flex items-center gap-2 text-sm">
                          <input type="checkbox" className="rounded" />
                          Predictive task creation
                        </label>
                        <label className="flex items-center gap-2 text-sm">
                          <input type="checkbox" className="rounded" defaultChecked />
                          Context-aware responses
                        </label>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                {/* Apply Button */}
                <div className="mt-6 flex gap-2">
                  <Button 
                    className="flex-1" 
                    onClick={savePreferences}
                  >
                    Apply Changes
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => {
                      // Reset to defaults
                      setTheme('system');
                      setAccentColor('blue');
                      setFontSize(16);
                      setCompactMode(false);
                      setAnimations(true);
                      setFocusMode(false);
                      setCodeTheme('github-dark');
                      setAiAssistLevel(50);
                    }}
                  >
                    Reset
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}