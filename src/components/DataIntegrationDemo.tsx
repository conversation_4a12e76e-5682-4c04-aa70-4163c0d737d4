import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  dataEventBus, 
  DataEventType,
  createPostgreSQLConnector,
  dataValidator,
  CircuitBreakerFactory,
  dataOperationHooks,
  z
} from '@/lib/data-integration';
import { useToast } from '@/hooks/use-toast';

export const DataIntegrationDemo: React.FC = () => {
  const [events, setEvents] = useState<Array<{ type: string; data: any; timestamp: Date }>>([]);
  const [connectorStatus, setConnectorStatus] = useState<'disconnected' | 'connected' | 'error'>('disconnected');
  const [circuitBreakerStatus, setCircuitBreakerStatus] = useState<string>('CLOSED');
  const { toast } = useToast();
  
  useEffect(() => {
    // Set up event listeners
    const subscriptions = [
      dataEventBus.on(DataEventType.CONNECTION_ESTABLISHED, (event) => {
        setConnectorStatus('connected');
        addEvent('Connection Established', event.data);
        toast({ title: 'Connected', description: 'Database connection established' });
      }),
      
      dataEventBus.on(DataEventType.CONNECTION_LOST, (event) => {
        setConnectorStatus('disconnected');
        addEvent('Connection Lost', event.data);
        toast({ title: 'Disconnected', description: 'Database connection lost', variant: 'destructive' });
      }),
      
      dataEventBus.on(DataEventType.DATA_VALIDATION_FAILED, (event) => {
        addEvent('Validation Failed', event.data);
        toast({ title: 'Validation Error', description: 'Data validation failed', variant: 'destructive' });
      }),
      
      dataEventBus.on(DataEventType.CIRCUIT_BREAKER_STATE_CHANGED, (event) => {
        setCircuitBreakerStatus(event.data.newState);
        addEvent('Circuit Breaker State Changed', event.data);
      })
    ];
    
    // Clean up subscriptions
    return () => {
      subscriptions.forEach(sub => sub.unsubscribe());
    };
  }, [toast]);
  
  const addEvent = (type: string, data: any) => {
    setEvents(prev => [...prev.slice(-9), { type, data, timestamp: new Date() }]);
  };
  
  const demoConnection = async () => {
    try {
      const connector = createPostgreSQLConnector({
        name: 'demo-db',
        host: 'localhost',
        port: 5432,
        database: 'demo',
        username: 'demo_user',
        password: 'demo_pass'
      });
      
      await connector.connect();
      
      // Demo query
      const result = await connector.query('SELECT * FROM users LIMIT 5');
      addEvent('Query Executed', { rowCount: result.rowCount });
      
      await connector.disconnect();
    } catch (error) {
      setConnectorStatus('error');
      console.error('Connection demo failed:', error);
    }
  };
  
  const demoValidation = async () => {
    // Register a schema
    const userSchema = z.object({
      id: z.string().uuid(),
      name: z.string().min(1),
      email: z.string().email(),
      age: z.number().min(0).max(150),
      active: z.boolean()
    });
    
    dataValidator.registerSchema('user', userSchema);
    
    // Test valid data
    const validData = {
      id: '123e4567-e89b-12d3-a456-************',
      name: 'John Doe',
      email: '<EMAIL>',
      age: 30,
      active: true
    };
    
    const validResult = await dataValidator.validateSchema(validData, 'user');
    addEvent('Validation Success', { valid: validResult.valid });
    
    // Test invalid data
    const invalidData = {
      id: 'not-a-uuid',
      name: '',
      email: 'invalid-email',
      age: 200,
      active: 'yes'
    };
    
    const invalidResult = await dataValidator.validateSchema(invalidData, 'user');
    addEvent('Validation Errors', { errors: invalidResult.errors });
  };
  
  const demoHooks = () => {
    // Register a custom hook
    const registration = dataOperationHooks.addValidationHook(
      async (data) => {
        if (data.age && data.age < 18) {
          return { valid: false, errors: ['User must be 18 or older'] };
        }
        return { valid: true };
      },
      ['insert', 'update']
    );
    
    addEvent('Hook Registered', { name: 'Age Validation Hook' });
    
    // Clean up after demo
    setTimeout(() => {
      registration.unregister();
      addEvent('Hook Unregistered', { name: 'Age Validation Hook' });
    }, 5000);
  };
  
  const demoCircuitBreaker = async () => {
    const breaker = CircuitBreakerFactory.create({
      name: 'demo-breaker',
      failureThreshold: 3,
      successThreshold: 2,
      timeout: 1000,
      resetTimeout: 5000,
      monitoringPeriod: 10000,
      volumeThreshold: 5
    });
    
    // Simulate failures
    for (let i = 0; i < 5; i++) {
      try {
        await breaker.execute(async () => {
          if (i < 3) {
            throw new Error('Simulated failure');
          }
          return 'Success';
        });
      } catch (error) {
        addEvent('Circuit Breaker Error', { attempt: i + 1, error: error.message });
      }
    }
    
    const metrics = breaker.getMetrics();
    addEvent('Circuit Breaker Metrics', metrics);
  };
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Data Integration System Demo</CardTitle>
          <CardDescription>
            Interactive demonstration of the new data integration components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="connectors" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="connectors">Connectors</TabsTrigger>
              <TabsTrigger value="validation">Validation</TabsTrigger>
              <TabsTrigger value="hooks">Hooks</TabsTrigger>
              <TabsTrigger value="resilience">Resilience</TabsTrigger>
            </TabsList>
            
            <TabsContent value="connectors" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Database Connector</h3>
                  <p className="text-sm text-muted-foreground">
                    PostgreSQL connector with connection pooling and retry logic
                  </p>
                </div>
                <Badge variant={
                  connectorStatus === 'connected' ? 'default' : 
                  connectorStatus === 'error' ? 'destructive' : 'secondary'
                }>
                  {connectorStatus}
                </Badge>
              </div>
              <Button onClick={demoConnection}>Test Connection</Button>
            </TabsContent>
            
            <TabsContent value="validation" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">Data Validation</h3>
                <p className="text-sm text-muted-foreground">
                  Schema validation with Zod, business rules, and quality metrics
                </p>
              </div>
              <Button onClick={demoValidation}>Run Validation Demo</Button>
            </TabsContent>
            
            <TabsContent value="hooks" className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold">Operation Hooks</h3>
                <p className="text-sm text-muted-foreground">
                  Pre/post operation hooks for validation, transformation, and auditing
                </p>
              </div>
              <Button onClick={demoHooks}>Register Custom Hook</Button>
            </TabsContent>
            
            <TabsContent value="resilience" className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Circuit Breaker</h3>
                  <p className="text-sm text-muted-foreground">
                    Fault tolerance with circuit breaker pattern
                  </p>
                </div>
                <Badge variant={
                  circuitBreakerStatus === 'CLOSED' ? 'default' :
                  circuitBreakerStatus === 'OPEN' ? 'destructive' : 'secondary'
                }>
                  {circuitBreakerStatus}
                </Badge>
              </div>
              <Button onClick={demoCircuitBreaker}>Test Circuit Breaker</Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Event Log</CardTitle>
          <CardDescription>Real-time events from the data integration system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {events.length === 0 ? (
              <p className="text-sm text-muted-foreground">No events yet...</p>
            ) : (
              events.map((event, index) => (
                <div key={index} className="flex items-start space-x-2 text-sm">
                  <span className="text-muted-foreground">
                    {event.timestamp.toLocaleTimeString()}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {event.type}
                  </Badge>
                  <pre className="flex-1 overflow-x-auto">
                    {JSON.stringify(event.data, null, 2)}
                  </pre>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};