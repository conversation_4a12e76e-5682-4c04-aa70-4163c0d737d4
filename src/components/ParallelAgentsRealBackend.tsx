import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import {
  Zap,
  Users,
  Activity,
  BarChart3,
  Play,
  Pause,
  Settings,
  Code,
  FileText,
  TestTube,
  Shield,
  Layers,
  Plus,
  X,
  RefreshCw,
  AlertCircle
} from 'lucide-react';

interface AgentTask {
  id: string;
  agentId: string;
  content: string;
  type: 'code_review' | 'refactor' | 'test_generation' | 'documentation' | 'security_scan';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  output?: string;
  error?: string;
  startTime?: Date;
  endTime?: Date;
}

interface RunningAgent {
  id: string;
  name: string;
  status: 'idle' | 'busy';
  currentTask?: string;
  pid?: number;
}

export function ParallelAgentsRealBackend() {
  const [activeTab, setActiveTab] = useState('agents');
  const [agents, setAgents] = useState<any[]>([]);
  const [runningAgents, setRunningAgents] = useState<RunningAgent[]>([]);
  const [tasks, setTasks] = useState<AgentTask[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [taskInput, setTaskInput] = useState('');
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [loadingAgents, setLoadingAgents] = useState(true);
  const [metrics, setMetrics] = useState({
    activeAgents: 0,
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    averageTime: 0
  });

  // Load available agents
  useEffect(() => {
    loadAgents();
  }, []);

  // Update metrics
  useEffect(() => {
    const interval = setInterval(() => {
      if (isProcessing) {
        updateMetrics();
        checkAgentStatuses();
      }
    }, 2000);

    return () => clearInterval(interval);
  }, [isProcessing]);

  const loadAgents = async () => {
    try {
      setLoadingAgents(true);
      const agentList = await api.listAgents();
      setAgents(agentList);
      // Auto-select first 5 agents for parallel processing
      setSelectedAgents(agentList.slice(0, 5).map((a: any) => a.id));
    } catch (error) {
      console.error('Failed to load agents:', error);
      toast.error('Failed to load agents');
    } finally {
      setLoadingAgents(false);
    }
  };

  const updateMetrics = () => {
    const completed = tasks.filter(t => t.status === 'completed').length;
    const failed = tasks.filter(t => t.status === 'failed').length;
    const totalTime = tasks
      .filter(t => t.endTime && t.startTime)
      .reduce((acc, t) => acc + (t.endTime!.getTime() - t.startTime!.getTime()), 0);
    
    setMetrics({
      activeAgents: runningAgents.filter(a => a.status === 'busy').length,
      totalTasks: tasks.length,
      completedTasks: completed,
      failedTasks: failed,
      averageTime: completed > 0 ? totalTime / completed / 1000 : 0
    });
  };

  const checkAgentStatuses = async () => {
    try {
      const sessions = await api.listRunningSessions();
      const updatedAgents = runningAgents.map(agent => {
        const session = sessions.find((s: any) => 
          s.process_type.AgentRun?.agent_id === agent.id
        );
        return {
          ...agent,
          status: session ? 'busy' as const : 'idle' as const,
          pid: session?.pid
        };
      });
      setRunningAgents(updatedAgents);
    } catch (error) {
      console.error('Failed to check agent statuses:', error);
    }
  };

  const startParallelProcessing = async () => {
    if (!taskInput.trim()) {
      toast.error('Please enter tasks to process');
      return;
    }

    if (selectedAgents.length === 0) {
      toast.error('Please select at least one agent');
      return;
    }

    setIsProcessing(true);
    const taskLines = taskInput.split('\n').filter(line => line.trim());
    
    // Create tasks
    const newTasks: AgentTask[] = taskLines.map((content, index) => ({
      id: `task-${Date.now()}-${index}`,
      agentId: selectedAgents[index % selectedAgents.length],
      content: content.trim(),
      type: inferTaskType(content),
      status: 'pending',
      progress: 0,
      startTime: new Date()
    }));

    setTasks(newTasks);

    // Initialize running agents
    const agentsToRun = selectedAgents.map(agentId => {
      const agent = agents.find(a => a.id === agentId);
      return {
        id: agentId,
        name: agent?.name || agentId,
        status: 'idle' as const
      };
    });
    setRunningAgents(agentsToRun);

    // Process tasks in parallel
    await Promise.all(
      newTasks.map(async (task, index) => {
        // Add delay to prevent overwhelming the system
        await new Promise(resolve => setTimeout(resolve, index * 500));
        await processTask(task);
      })
    );

    setIsProcessing(false);
  };

  const processTask = async (task: AgentTask) => {
    try {
      // Update task status to running
      setTasks(prev => prev.map(t => 
        t.id === task.id ? { ...t, status: 'running', progress: 10 } : t
      ));

      // Update agent status
      setRunningAgents(prev => prev.map(a => 
        a.id === task.agentId ? { ...a, status: 'busy', currentTask: task.id } : a
      ));

      // Execute the agent
      const agent = agents.find(a => a.id === task.agentId);
      if (!agent) throw new Error('Agent not found');

      // Create a proper task based on type
      const agentTask = createAgentTask(task.content, task.type);
      
      // Execute agent with real backend
      await api.executeAgent(agent.id, agentTask, selectedAgents[0], (chunk) => {
        // Update progress based on output
        setTasks(prev => prev.map(t => 
          t.id === task.id 
            ? { 
                ...t, 
                progress: Math.min(90, t.progress + 10),
                output: (t.output || '') + chunk
              } 
            : t
        ));
      });

      // Mark as completed
      setTasks(prev => prev.map(t => 
        t.id === task.id 
          ? { 
              ...t, 
              status: 'completed', 
              progress: 100,
              endTime: new Date()
            } 
          : t
      ));

    } catch (error) {
      console.error(`Failed to process task ${task.id}:`, error);
      setTasks(prev => prev.map(t => 
        t.id === task.id 
          ? { 
              ...t, 
              status: 'failed', 
              error: error instanceof Error ? error.message : 'Unknown error',
              endTime: new Date()
            } 
          : t
      ));
    } finally {
      // Update agent status back to idle
      setRunningAgents(prev => prev.map(a => 
        a.id === task.agentId ? { ...a, status: 'idle', currentTask: undefined } : a
      ));
    }
  };

  const inferTaskType = (content: string): AgentTask['type'] => {
    const lower = content.toLowerCase();
    if (lower.includes('review') || lower.includes('check')) return 'code_review';
    if (lower.includes('refactor') || lower.includes('optimize')) return 'refactor';
    if (lower.includes('test') || lower.includes('spec')) return 'test_generation';
    if (lower.includes('document') || lower.includes('docs')) return 'documentation';
    if (lower.includes('security') || lower.includes('scan')) return 'security_scan';
    return 'code_review';
  };

  const createAgentTask = (content: string, type: AgentTask['type']) => {
    switch (type) {
      case 'code_review':
        return `Review the following code or module: ${content}`;
      case 'refactor':
        return `Refactor and optimize: ${content}`;
      case 'test_generation':
        return `Generate comprehensive tests for: ${content}`;
      case 'documentation':
        return `Create detailed documentation for: ${content}`;
      case 'security_scan':
        return `Perform security analysis on: ${content}`;
      default:
        return content;
    }
  };

  const stopProcessing = async () => {
    setIsProcessing(false);
    
    // Kill all running agent sessions
    for (const agent of runningAgents) {
      if (agent.pid) {
        try {
          await api.killAgentSession(agent.pid.toString());
        } catch (error) {
          console.error(`Failed to kill agent ${agent.id}:`, error);
        }
      }
    }

    // Update all running tasks to failed
    setTasks(prev => prev.map(t => 
      t.status === 'running' 
        ? { ...t, status: 'failed', error: 'Processing stopped by user' }
        : t
    ));
  };

  const getTaskIcon = (type: AgentTask['type']) => {
    switch (type) {
      case 'code_review': return <Code className="w-4 h-4" />;
      case 'refactor': return <RefreshCw className="w-4 h-4" />;
      case 'test_generation': return <TestTube className="w-4 h-4" />;
      case 'documentation': return <FileText className="w-4 h-4" />;
      case 'security_scan': return <Shield className="w-4 h-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b">
        <h2 className="text-2xl font-bold mb-2">Parallel Agents Processing</h2>
        <p className="text-muted-foreground">
          Execute multiple AI agents in parallel for massive task processing
        </p>
      </div>

      <div className="flex-1 p-6 overflow-auto">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="agents">Agent Selection</TabsTrigger>
            <TabsTrigger value="tasks">Task Queue</TabsTrigger>
            <TabsTrigger value="metrics">Real-time Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="agents" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Available Agents</CardTitle>
              </CardHeader>
              <CardContent>
                {loadingAgents ? (
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : agents.length === 0 ? (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      No agents found. Please create agents first.
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {agents.map(agent => (
                      <motion.div
                        key={agent.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card 
                          className={`cursor-pointer transition-all ${
                            selectedAgents.includes(agent.id) 
                              ? 'border-primary ring-2 ring-primary/20' 
                              : ''
                          }`}
                          onClick={() => {
                            setSelectedAgents(prev => 
                              prev.includes(agent.id)
                                ? prev.filter(id => id !== agent.id)
                                : [...prev, agent.id]
                            );
                          }}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">{agent.name}</h4>
                              {selectedAgents.includes(agent.id) && (
                                <Badge variant="secondary">Selected</Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {agent.model || 'claude-3-5-sonnet-20241022'}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Task Input</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Textarea
                  placeholder="Enter tasks (one per line)...
Example:
Review authentication module for security vulnerabilities
Refactor user service to improve performance
Generate unit tests for payment processing"
                  value={taskInput}
                  onChange={(e) => setTaskInput(e.target.value)}
                  rows={8}
                  className="font-mono text-sm"
                />
                <div className="flex gap-2">
                  <Button 
                    onClick={startParallelProcessing}
                    disabled={isProcessing || selectedAgents.length === 0}
                  >
                    {isProcessing ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Start Parallel Processing
                      </>
                    )}
                  </Button>
                  {isProcessing && (
                    <Button variant="destructive" onClick={stopProcessing}>
                      <X className="mr-2 h-4 w-4" />
                      Stop
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Tasks</p>
                      <p className="text-2xl font-bold">{metrics.totalTasks}</p>
                    </div>
                    <Layers className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Completed</p>
                      <p className="text-2xl font-bold text-green-600">
                        {metrics.completedTasks}
                      </p>
                    </div>
                    <Activity className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Failed</p>
                      <p className="text-2xl font-bold text-red-600">
                        {metrics.failedTasks}
                      </p>
                    </div>
                    <AlertCircle className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Avg Time</p>
                      <p className="text-2xl font-bold">
                        {metrics.averageTime.toFixed(1)}s
                      </p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Task Queue</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-2">
                    {tasks.map(task => (
                      <motion.div
                        key={task.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className="p-3 border rounded-lg"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getTaskIcon(task.type)}
                            <span className="font-medium">{task.content}</span>
                          </div>
                          <Badge variant={
                            task.status === 'completed' ? 'success' :
                            task.status === 'failed' ? 'destructive' :
                            task.status === 'running' ? 'default' : 'secondary'
                          }>
                            {task.status}
                          </Badge>
                        </div>
                        {task.status === 'running' && (
                          <Progress value={task.progress} className="h-2" />
                        )}
                        {task.error && (
                          <p className="text-sm text-red-600 mt-2">{task.error}</p>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Active Agents</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {runningAgents.map(agent => (
                      <div key={agent.id} className="flex items-center justify-between p-3 border rounded">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${
                            agent.status === 'busy' ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
                          }`} />
                          <span className="font-medium">{agent.name}</span>
                        </div>
                        <Badge variant={agent.status === 'busy' ? 'default' : 'secondary'}>
                          {agent.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Processing Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Success Rate</span>
                        <span className="text-sm font-medium">
                          {metrics.totalTasks > 0 
                            ? Math.round((metrics.completedTasks / metrics.totalTasks) * 100)
                            : 0}%
                        </span>
                      </div>
                      <Progress 
                        value={metrics.totalTasks > 0 
                          ? (metrics.completedTasks / metrics.totalTasks) * 100 
                          : 0} 
                      />
                    </div>
                    <div className="grid grid-cols-2 gap-4 pt-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold">{metrics.activeAgents}</p>
                        <p className="text-sm text-muted-foreground">Active Agents</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">
                          {metrics.totalTasks > 0 
                            ? Math.round(metrics.completedTasks / (metrics.averageTime || 1))
                            : 0}/s
                        </p>
                        <p className="text-sm text-muted-foreground">Throughput</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}