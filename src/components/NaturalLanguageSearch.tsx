import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  FileCode, 
  Function, 
  Variable, 
  Zap,
  Brain,
  Clock,
  ArrowRight,
  Code,
  GitBranch,
  Bug,
  Lightbulb
} from 'lucide-react';

interface SearchResult {
  id: string;
  type: 'function' | 'variable' | 'class' | 'file' | 'component' | 'hook';
  name: string;
  description: string;
  file: string;
  line: number;
  code: string;
  confidence: number;
  context: string[];
}

interface SearchSuggestion {
  query: string;
  description: string;
  category: 'function' | 'pattern' | 'bug' | 'feature';
}

const SEARCH_SUGGESTIONS: SearchSuggestion[] = [
  { query: "Find the auth function", description: "Locate authentication logic", category: "function" },
  { query: "Show me error handling", description: "Find error handling patterns", category: "pattern" },
  { query: "Where is the API called", description: "Find API call locations", category: "function" },
  { query: "Find state management", description: "Locate state management code", category: "pattern" },
  { query: "Show me the theme logic", description: "Find theming implementation", category: "function" },
  { query: "Find components that use hooks", description: "Locate hook usage", category: "pattern" },
  { query: "Where are types defined", description: "Find TypeScript type definitions", category: "function" },
  { query: "Show me the routing", description: "Find routing configuration", category: "pattern" },
  { query: "Find performance issues", description: "Identify potential bottlenecks", category: "bug" },
  { query: "Show me unused code", description: "Find dead code", category: "bug" }
];

// Mock search results for demo
const MOCK_RESULTS: SearchResult[] = [
  {
    id: '1',
    type: 'function',
    name: 'authenticateUser',
    description: 'Main authentication function that validates user credentials',
    file: 'src/lib/auth.ts',
    line: 42,
    code: 'export async function authenticateUser(credentials: LoginCredentials): Promise<AuthResult>',
    confidence: 0.95,
    context: ['authentication', 'login', 'security']
  },
  {
    id: '2',
    type: 'hook',
    name: 'useAuth',
    description: 'React hook for managing authentication state',
    file: 'src/hooks/useAuth.ts',
    line: 15,
    code: 'export function useAuth(): AuthContextType',
    confidence: 0.88,
    context: ['authentication', 'react', 'state']
  },
  {
    id: '3',
    type: 'component',
    name: 'LoginForm',
    description: 'Login form component with validation',
    file: 'src/components/LoginForm.tsx',
    line: 23,
    code: 'export function LoginForm({ onSubmit }: LoginFormProps)',
    confidence: 0.82,
    context: ['authentication', 'form', 'ui']
  }
];

export function NaturalLanguageSearch() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Simulate natural language processing
  const processQuery = (query: string): { intent: string; entities: string[] } => {
    const lowerQuery = query.toLowerCase();
    
    // Simple intent detection
    let intent = 'search';
    if (lowerQuery.includes('find') || lowerQuery.includes('show') || lowerQuery.includes('where')) {
      intent = 'find';
    } else if (lowerQuery.includes('how') || lowerQuery.includes('what')) {
      intent = 'explain';
    } else if (lowerQuery.includes('fix') || lowerQuery.includes('bug')) {
      intent = 'debug';
    }

    // Simple entity extraction
    const entities: string[] = [];
    const keywords = ['auth', 'theme', 'api', 'component', 'hook', 'function', 'error', 'state'];
    keywords.forEach(keyword => {
      if (lowerQuery.includes(keyword)) {
        entities.push(keyword);
      }
    });

    return { intent, entities };
  };

  const handleSearch = async () => {
    if (!query.trim()) return;

    setIsSearching(true);
    setShowSuggestions(false);

    // Add to recent searches
    setRecentSearches(prev => {
      const updated = [query, ...prev.filter(q => q !== query)].slice(0, 5);
      return updated;
    });

    // Process the natural language query
    const { intent, entities } = processQuery(query);
    
    // Simulate search delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Filter mock results based on entities
    let filteredResults = MOCK_RESULTS;
    if (entities.length > 0) {
      filteredResults = MOCK_RESULTS.filter(result => 
        entities.some(entity => 
          result.context.some(ctx => ctx.includes(entity)) ||
          result.name.toLowerCase().includes(entity) ||
          result.description.toLowerCase().includes(entity)
        )
      );
    }

    setResults(filteredResults);
    setIsSearching(false);
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.query);
    setShowSuggestions(false);
    // Auto-search when suggestion is clicked
    setTimeout(() => handleSearch(), 100);
  };

  const getTypeIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'function': return <Function className="w-4 h-4" />;
      case 'component': return <Code className="w-4 h-4" />;
      case 'hook': return <Zap className="w-4 h-4" />;
      case 'variable': return <Variable className="w-4 h-4" />;
      case 'class': return <GitBranch className="w-4 h-4" />;
      case 'file': return <FileCode className="w-4 h-4" />;
      default: return <Code className="w-4 h-4" />;
    }
  };

  const getCategoryIcon = (category: SearchSuggestion['category']) => {
    switch (category) {
      case 'function': return <Function className="w-4 h-4" />;
      case 'pattern': return <GitBranch className="w-4 h-4" />;
      case 'bug': return <Bug className="w-4 h-4" />;
      case 'feature': return <Lightbulb className="w-4 h-4" />;
      default: return <Search className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Natural Language Search
            <Badge variant="secondary">AI-Powered</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  ref={inputRef}
                  placeholder="Ask me anything... e.g., 'Find the auth function' or 'Show me error handling'"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  onFocus={() => setShowSuggestions(true)}
                  className="pl-10"
                />
              </div>
              <Button onClick={handleSearch} disabled={isSearching || !query.trim()}>
                {isSearching ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Brain className="w-4 h-4" />
                  </motion.div>
                ) : (
                  <Search className="w-4 h-4" />
                )}
              </Button>
            </div>

            {/* Search Suggestions Dropdown */}
            <AnimatePresence>
              {showSuggestions && query.length === 0 && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 right-0 z-50 mt-1"
                >
                  <Card className="shadow-lg">
                    <CardContent className="p-3">
                      <div className="text-sm font-medium mb-2">Try these searches:</div>
                      <div className="space-y-1">
                        {SEARCH_SUGGESTIONS.slice(0, 6).map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="w-full text-left p-2 rounded hover:bg-muted transition-colors flex items-center gap-2"
                          >
                            {getCategoryIcon(suggestion.category)}
                            <div>
                              <div className="text-sm font-medium">{suggestion.query}</div>
                              <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                            </div>
                          </button>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Recent:</span>
              <div className="flex gap-1 flex-wrap">
                {recentSearches.map((search, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => setQuery(search)}
                  >
                    {search}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Search Results */}
          <AnimatePresence>
            {results.length > 0 && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Search Results ({results.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-96">
                      <div className="space-y-3">
                        {results.map((result) => (
                          <motion.div
                            key={result.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className="border rounded-lg p-4 hover:bg-muted/50 transition-colors cursor-pointer"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-2">
                                {getTypeIcon(result.type)}
                                <span className="font-medium">{result.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {result.type}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="text-xs text-muted-foreground">
                                  {Math.round(result.confidence * 100)}% match
                                </div>
                                <div 
                                  className="w-2 h-2 rounded-full"
                                  style={{ 
                                    backgroundColor: result.confidence > 0.9 ? '#10b981' : 
                                                   result.confidence > 0.8 ? '#f59e0b' : '#ef4444'
                                  }}
                                />
                              </div>
                            </div>
                            
                            <p className="text-sm text-muted-foreground mb-2">
                              {result.description}
                            </p>
                            
                            <div className="bg-muted rounded p-2 mb-2">
                              <code className="text-xs">{result.code}</code>
                            </div>
                            
                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <span>{result.file}:{result.line}</span>
                              <div className="flex gap-1">
                                {result.context.map((ctx, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    {ctx}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Empty State */}
          {!isSearching && results.length === 0 && query && (
            <Card className="text-center py-8">
              <CardContent>
                <Search className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-medium mb-2">No results found</h3>
                <p className="text-sm text-muted-foreground">
                  Try rephrasing your search or use different keywords
                </p>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}