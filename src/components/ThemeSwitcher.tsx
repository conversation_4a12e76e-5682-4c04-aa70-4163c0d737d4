import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { Popover } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Slider } from '@/components/ui/slider';
import { 
  Palette, 
  Sun, 
  Moon, 
  Monitor,
  Eye,
  Check
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { api } from '@/lib/api';
import type { ThemeConfig as ApiThemeConfig } from '@/lib/api';

interface ThemeSwitcherProps {
  className?: string;
}

type Theme = 'light' | 'dark' | 'system';
type ColorScheme = 'default' | 'ocean' | 'forest' | 'sunset' | 'midnight' | 'aurora' | 'monochrome';

interface ThemeConfig {
  theme: Theme;
  colorScheme: ColorScheme;
  fontSize: number;
  reduceMotion: boolean;
  highContrast: boolean;
  colorBlindMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
}

const colorSchemes: Record<ColorScheme, { primary: string; accent: string; background: string }> = {
  default: { primary: '#3B82F6', accent: '#10B981', background: '#FFFFFF' },
  ocean: { primary: '#0EA5E9', accent: '#06B6D4', background: '#F0F9FF' },
  forest: { primary: '#10B981', accent: '#84CC16', background: '#F0FDF4' },
  sunset: { primary: '#F59E0B', accent: '#EF4444', background: '#FFF7ED' },
  midnight: { primary: '#6366F1', accent: '#8B5CF6', background: '#1E1B4B' },
  aurora: { primary: '#EC4899', accent: '#A855F7', background: '#FDF2F8' },
  monochrome: { primary: '#6B7280', accent: '#374151', background: '#F9FAFB' }
};

export function ThemeSwitcher({ className }: ThemeSwitcherProps) {
  const [config, setConfig] = useState<ThemeConfig>({
    theme: 'system',
    colorScheme: 'default',
    fontSize: 16,
    reduceMotion: false,
    highContrast: false,
    colorBlindMode: 'none'
  });
  const [previewOpen, setPreviewOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load saved theme configuration from backend
    loadThemeConfig();
  }, []);

  const loadThemeConfig = async () => {
    try {
      const savedConfig = await api.getThemeConfig();
      setConfig({
        theme: savedConfig.theme as Theme,
        colorScheme: savedConfig.color_scheme as ColorScheme,
        fontSize: savedConfig.font_size,
        reduceMotion: savedConfig.reduce_motion,
        highContrast: savedConfig.high_contrast,
        colorBlindMode: savedConfig.color_blind_mode as ThemeConfig['colorBlindMode']
      });
    } catch (error) {
      console.error('Failed to load theme config:', error);
      // Fall back to system preferences
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      
      setConfig(prev => ({
        ...prev,
        theme: prefersDark ? 'dark' : 'light',
        reduceMotion: prefersReducedMotion
      }));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!loading) {
      // Apply theme changes
      applyTheme(config);
      // Save configuration to backend
      saveThemeConfig();
    }
  }, [config, loading]);

  const saveThemeConfig = async () => {
    try {
      const apiConfig: ApiThemeConfig = {
        theme: config.theme,
        color_scheme: config.colorScheme,
        font_size: config.fontSize,
        reduce_motion: config.reduceMotion,
        high_contrast: config.highContrast,
        color_blind_mode: config.colorBlindMode
      };
      await api.setThemeConfig(apiConfig);
    } catch (error) {
      console.error('Failed to save theme config:', error);
    }
  };

  const applyTheme = (config: ThemeConfig) => {
    const root = document.documentElement;
    
    // Apply base theme
    const effectiveTheme = config.theme === 'system' 
      ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
      : config.theme;
    
    root.classList.remove('light', 'dark');
    root.classList.add(effectiveTheme);
    
    // Apply color scheme
    const scheme = colorSchemes[config.colorScheme];
    const isDark = effectiveTheme === 'dark';
    
    // Set CSS variables
    root.style.setProperty('--primary', scheme.primary);
    root.style.setProperty('--primary-foreground', isDark ? '#FFFFFF' : '#000000');
    root.style.setProperty('--accent', scheme.accent);
    root.style.setProperty('--background', isDark ? '#0A0A0A' : scheme.background);
    root.style.setProperty('--foreground', isDark ? '#FFFFFF' : '#000000');
    
    // Apply font size
    root.style.fontSize = `${config.fontSize}px`;
    
    // Apply accessibility settings
    if (config.reduceMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }
    
    if (config.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }
    
    // Apply color blind filters
    if (config.colorBlindMode !== 'none') {
      root.classList.add(`colorblind-${config.colorBlindMode}`);
    } else {
      root.classList.remove('colorblind-protanopia', 'colorblind-deuteranopia', 'colorblind-tritanopia');
    }
  };


  return (
    <>
      <Popover
        trigger={
          <Button variant="ghost" size="icon" className={className}>
            {loading ? (
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : (
              <Palette className="h-5 w-5" />
            )}
          </Button>
        }
        content={
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Theme Settings</h4>
            </div>
            
            {/* Theme Mode */}
            <div>
              <Label className="text-sm mb-2">Theme Mode</Label>
              <RadioGroup value={config.theme} onValueChange={(value) => setConfig({ ...config, theme: value as Theme })}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="light" id="light" />
                  <Label htmlFor="light" className="flex items-center gap-2 cursor-pointer">
                    <Sun className="w-4 h-4" />
                    Light
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="dark" id="dark" />
                  <Label htmlFor="dark" className="flex items-center gap-2 cursor-pointer">
                    <Moon className="w-4 h-4" />
                    Dark
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="system" id="system" />
                  <Label htmlFor="system" className="flex items-center gap-2 cursor-pointer">
                    <Monitor className="w-4 h-4" />
                    System
                  </Label>
                </div>
              </RadioGroup>
            </div>
            
            {/* Color Scheme */}
            <div>
              <Label className="text-sm mb-2">Color Scheme</Label>
              <div className="grid grid-cols-4 gap-2">
                {(Object.keys(colorSchemes) as ColorScheme[]).map((scheme) => (
                  <button
                    key={scheme}
                    onClick={() => setConfig({ ...config, colorScheme: scheme })}
                    className={`relative h-8 rounded-md border-2 transition-all ${
                      config.colorScheme === scheme ? 'border-primary' : 'border-transparent'
                    }`}
                    style={{ backgroundColor: colorSchemes[scheme].primary }}
                    title={scheme}
                  >
                    {config.colorScheme === scheme && (
                      <Check className="absolute inset-0 m-auto w-4 h-4 text-white" />
                    )}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Font Size */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm">Font Size</Label>
                <span className="text-xs text-muted-foreground">{config.fontSize}px</span>
              </div>
              <Slider
                value={[config.fontSize]}
                onValueChange={(value) => setConfig({ ...config, fontSize: value[0] })}
                min={12}
                max={20}
                step={1}
                className="w-full"
              />
            </div>
            
            {/* Accessibility */}
            <div className="space-y-3">
              <h5 className="text-sm font-medium">Accessibility</h5>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="reduce-motion" className="text-sm">Reduce Motion</Label>
                <Switch
                  id="reduce-motion"
                  checked={config.reduceMotion}
                  onCheckedChange={(checked) => setConfig({ ...config, reduceMotion: checked })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="high-contrast" className="text-sm">High Contrast</Label>
                <Switch
                  id="high-contrast"
                  checked={config.highContrast}
                  onCheckedChange={(checked) => setConfig({ ...config, highContrast: checked })}
                />
              </div>
            </div>
            
            {/* Preview Button */}
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => setPreviewOpen(true)}
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview Theme
            </Button>
          </div>
        }
      />
      
      {/* Theme Preview Modal */}
      <AnimatePresence>
        {previewOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
            onClick={() => setPreviewOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="w-full max-w-3xl p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <Card>
                <CardHeader>
                  <CardTitle>Theme Preview</CardTitle>
                  <CardDescription>
                    See how your theme looks across different components
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Primary Card</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm">This demonstrates primary colors and typography.</p>
                        <div className="flex gap-2 mt-3">
                          <Button size="sm">Primary</Button>
                          <Button size="sm" variant="secondary">Secondary</Button>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Secondary Card</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm">Different states and variations.</p>
                        <div className="flex gap-2 mt-3">
                          <Button size="sm" variant="outline">Outline</Button>
                          <Button size="sm" variant="ghost">Ghost</Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button onClick={() => setPreviewOpen(false)}>
                      Close Preview
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}