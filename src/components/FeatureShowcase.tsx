import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  Zap, 
  Brain, 
  Palette, 
  Mic, 
  Eye, 
  Gamepad2, 
  Workflow,
  MessageCircle,
  Code,
  Search,
  Layers,
  ChevronRight,
  Star
} from 'lucide-react';
import { AmazingFeatures } from './AmazingFeatures';
import { PerformanceOptimizer } from './PerformanceOptimizer';
import { SmartCodeAssistant } from './SmartCodeAssistant';

interface FeatureShowcaseProps {
  onClose: () => void;
}

const showcaseFeatures = [
  {
    id: 'amazing-features',
    title: 'Amazing Features',
    description: 'Explore revolutionary features coming to <PERSON>',
    icon: <Sparkles className="w-6 h-6" />,
    color: 'from-purple-500 to-pink-500',
    component: AmazingFeatures
  },
  {
    id: 'performance-optimizer',
    title: 'Performance Optimizer',
    description: 'Auto-detect and fix performance issues',
    icon: <Zap className="w-6 h-6" />,
    color: 'from-blue-500 to-cyan-500',
    component: PerformanceOptimizer
  },
  {
    id: 'smart-assistant',
    title: 'Smart Code Assistant',
    description: 'AI-powered code analysis and suggestions',
    icon: <Brain className="w-6 h-6" />,
    color: 'from-green-500 to-emerald-500',
    component: SmartCodeAssistant
  }
];

export const FeatureShowcase: React.FC<FeatureShowcaseProps> = ({ onClose }) => {
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);

  const SelectedComponent = selectedFeature 
    ? showcaseFeatures.find(f => f.id === selectedFeature)?.component 
    : null;

  return (
    <>
      <AnimatePresence>
        {!selectedFeature && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
            >
              <div className="p-6 border-b">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold flex items-center gap-2">
                      <Star className="w-6 h-6 text-yellow-500" />
                      Feature Showcase
                    </h2>
                    <p className="text-muted-foreground mt-1">
                      Discover the latest improvements and upcoming features
                    </p>
                  </div>
                  <Button variant="ghost" onClick={onClose}>×</Button>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {showcaseFeatures.map((feature, index) => (
                    <motion.div
                      key={feature.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card 
                        className="h-full hover-lift cursor-pointer no-motion-conflict relative overflow-hidden group"
                        onClick={() => setSelectedFeature(feature.id)}
                      >
                        {/* Gradient Background */}
                        <div className={`absolute inset-0 bg-gradient-to-br ${feature.color} opacity-5 group-hover:opacity-10 transition-opacity`} />
                        
                        <CardHeader className="relative">
                          <div className="flex items-center justify-between">
                            <div className={`p-2 rounded-lg bg-gradient-to-br ${feature.color} text-white`}>
                              {feature.icon}
                            </div>
                            <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-foreground transition-colors" />
                          </div>
                          <CardTitle className="text-lg">{feature.title}</CardTitle>
                        </CardHeader>
                        
                        <CardContent className="relative">
                          <p className="text-sm text-muted-foreground">
                            {feature.description}
                          </p>
                          
                          <div className="mt-4">
                            <Badge variant="outline" className="text-xs">
                              Click to explore
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                {/* Performance Improvements Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="mt-8 p-6 bg-muted/30 rounded-lg"
                >
                  <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                    <Zap className="w-5 h-5 text-blue-500" />
                    Performance Improvements
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">60fps</div>
                      <div className="text-muted-foreground">Animation Frame Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">80%</div>
                      <div className="text-muted-foreground">Reduced Layout Thrashing</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">25%</div>
                      <div className="text-muted-foreground">Faster Time to Interactive</div>
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Selected Feature Component */}
      <AnimatePresence>
        {SelectedComponent && (
          <SelectedComponent onClose={() => setSelectedFeature(null)} />
        )}
      </AnimatePresence>
    </>
  );
};