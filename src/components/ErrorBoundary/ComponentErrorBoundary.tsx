import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ErrorBoundary } from './ErrorBoundary';

interface ComponentErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  componentName?: string;
}

const ComponentErrorFallback: React.FC<ComponentErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  componentName = 'Component',
}) => (
  <Card className="border-destructive/20 bg-destructive/5">
    <CardContent className="p-4">
      <div className="flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-destructive mt-0.5 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-sm text-destructive">
            {componentName} Error
          </h4>
          <p className="text-sm text-muted-foreground mt-1">
            {error.message || 'Something went wrong while loading this component.'}
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={resetErrorBoundary}
            className="mt-3 gap-2"
          >
            <RefreshCw className="h-3 w-3" />
            Retry
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
);

interface ComponentErrorBoundaryProps {
  children: React.ReactNode;
  componentName?: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  resetKeys?: Array<string | number>;
}

export const ComponentErrorBoundary: React.FC<ComponentErrorBoundaryProps> = ({
  children,
  componentName,
  onError,
  resetKeys,
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Log component-specific error
    console.error(`Error in ${componentName || 'component'}:`, error);
    
    // Call the provided onError callback
    onError?.(error, errorInfo);
  };

  return (
    <ErrorBoundary
      fallback={
        <ComponentErrorFallback
          error={new Error('Component failed to render')}
          resetErrorBoundary={() => window.location.reload()}
          componentName={componentName}
        />
      }
      onError={handleError}
      resetKeys={resetKeys}
      resetOnPropsChange={false}
      showErrorDetails={false}
    >
      {children}
    </ErrorBoundary>
  );
};