import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useMCPServers } from '@/hooks/useMCPServers';
import { MCPServerConfig } from '@/lib/mcp/MCPServerManager';
import { MCPAIAssistant } from './MCPAIAssistant';
import { MCPIntelligentAutomation } from './MCPIntelligentAutomation';
import { MCPAdvancedAnalytics } from './MCPAdvancedAnalytics';
import { MCPPredictiveInsights } from './MCPPredictiveInsights';
import {
  Server,
  Plus,
  Settings,
  Activity,
  Shield,
  Zap,
  Network,
  Database,
  Code,
  FileText,
  Monitor,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  Upload,
  Trash2,
  Edit,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

interface MCPTemplate {
  name: string;
  description: string;
  type: string;
  command: string;
  args: string[];
  capabilities: string[];
}

export function EnhancedMCPManager({ onBack }: { onBack?: () => void }) {
  const {
    servers,
    metrics,
    loading,
    error,
    addServer,
    removeServer,
    startServer,
    stopServer,
    restartServer,
    testConnection,
    exportConfiguration,
    importConfiguration,
    getServerMetrics,
    getServerTemplates,
    clearError
  } = useMCPServers();

  const [selectedServer, setSelectedServer] = useState<MCPServerConfig | null>(null);
  const [showAddServer, setShowAddServer] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const getTemplateDescription = (type: string): string => {
    const descriptions = {
      filesystem: 'Access and manage files and directories',
      git: 'Git repository operations and version control',
      database: 'Database operations and queries',
      api: 'Connect to external REST APIs',
      custom: 'Custom MCP server implementation'
    };
    return descriptions[type as keyof typeof descriptions] || 'Custom server';
  };

  const templates = getServerTemplates().map(template => ({
    ...template,
    description: getTemplateDescription(template.type)
  }));

  const getStatusColor = (status: MCPServerConfig['status']) => {
    switch (status) {
      case 'running': return 'text-green-500';
      case 'stopped': return 'text-gray-500';
      case 'error': return 'text-red-500';
      case 'starting': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: MCPServerConfig['status']) => {
    switch (status) {
      case 'running': return CheckCircle;
      case 'stopped': return Pause;
      case 'error': return AlertTriangle;
      case 'starting': return Clock;
      default: return Pause;
    }
  };

  const getTypeIcon = (type: MCPServerConfig['type']) => {
    switch (type) {
      case 'filesystem': return FileText;
      case 'git': return Code;
      case 'database': return Database;
      case 'api': return Network;
      default: return Server;
    }
  };

  const handleToggleServer = async (serverId: string) => {
    const server = servers.find(s => s.id === serverId);
    if (!server) return;

    try {
      if (server.status === 'running') {
        await stopServer(serverId);
      } else {
        await startServer(serverId);
      }
    } catch (error) {
      console.error('Failed to toggle server:', error);
    }
  };

  const handleAddServerFromTemplate = async (template: MCPTemplate) => {
    try {
      await addServer({
        name: template.name,
        type: template.type as MCPServerConfig['type'],
        command: template.command,
        args: template.args,
        capabilities: template.capabilities,
        config: {}
      });
      setShowAddServer(false);
    } catch (error) {
      console.error('Failed to add server:', error);
    }
  };

  const handleDeleteServer = async (serverId: string) => {
    try {
      await removeServer(serverId);
      if (selectedServer?.id === serverId) {
        setSelectedServer(null);
      }
    } catch (error) {
      console.error('Failed to delete server:', error);
    }
  };

  const handleRestartServer = async (serverId: string) => {
    try {
      await restartServer(serverId);
    } catch (error) {
      console.error('Failed to restart server:', error);
    }
  };

  const handleExportConfig = async () => {
    try {
      setIsExporting(true);
      const config = await exportConfiguration();
      const blob = new Blob([config], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mcp-config-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export configuration:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportConfig = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      setIsImporting(true);
      const content = await file.text();
      await importConfiguration(content);
    } catch (error) {
      console.error('Failed to import configuration:', error);
    } finally {
      setIsImporting(false);
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Clock className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p>Loading MCP servers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 m-4 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
            <Button size="sm" variant="outline" onClick={clearError}>
              Dismiss
            </Button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="border-b p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Server className="w-8 h-8 text-blue-500" />
              Enhanced MCP Manager
            </h1>
            <p className="text-muted-foreground mt-2">
              Advanced Model Context Protocol server management and monitoring
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="px-3 py-1">
              <Activity className="w-4 h-4 mr-2" />
              {servers.filter(s => s.status === 'running').length} Active
            </Badge>
            {onBack && (
              <Button variant="outline" onClick={onBack}>
                ← Back
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs defaultValue="servers" className="h-full flex flex-col">
          <TabsList className="mx-6 mt-4">
            <TabsTrigger value="servers">Server Management</TabsTrigger>
            <TabsTrigger value="monitoring">Analytics</TabsTrigger>
            <TabsTrigger value="ai-assistant">AI Assistant</TabsTrigger>
            <TabsTrigger value="automation">Automation</TabsTrigger>
            <TabsTrigger value="insights">Predictive Insights</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden p-6">
            <TabsContent value="servers" className="h-full">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
                {/* Server List */}
                <div className="lg:col-span-2">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">MCP Servers</h3>
                    <Button onClick={() => setShowAddServer(true)}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Server
                    </Button>
                  </div>

                  <ScrollArea className="h-[600px]">
                    <div className="space-y-4">
                      {servers.map((server) => {
                        const StatusIcon = getStatusIcon(server.status);
                        const TypeIcon = getTypeIcon(server.type);

                        return (
                          <Card 
                            key={server.id}
                            className={`cursor-pointer transition-colors ${
                              selectedServer?.id === server.id ? 'border-primary' : ''
                            }`}
                            onClick={() => setSelectedServer(server)}
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <TypeIcon className="w-5 h-5" />
                                  <div>
                                    <h4 className="font-medium">{server.name}</h4>
                                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                      <StatusIcon className={`w-4 h-4 ${getStatusColor(server.status)}`} />
                                      <span>{server.status}</span>
                                      {server.status === 'running' && (
                                        <span>• {formatUptime(server.performance.uptime)}</span>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                <div className="flex items-center gap-2">
                                  <Badge variant="outline">{server.type}</Badge>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleToggleServer(server.id);
                                    }}
                                  >
                                    {server.status === 'running' ? (
                                      <Pause className="w-4 h-4" />
                                    ) : (
                                      <Play className="w-4 h-4" />
                                    )}
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteServer(server.id);
                                    }}
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>

                              {server.status === 'running' && (() => {
                                const serverMetrics = getServerMetrics(server.id);
                                return serverMetrics && (
                                  <div className="mt-3 grid grid-cols-3 gap-4 text-sm">
                                    <div>
                                      <span className="text-muted-foreground">Requests:</span>
                                      <div className="font-medium">{serverMetrics.requests.total}</div>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Avg Response:</span>
                                      <div className="font-medium">{Math.round(serverMetrics.requests.avgResponseTime)}ms</div>
                                    </div>
                                    <div>
                                      <span className="text-muted-foreground">Errors:</span>
                                      <div className="font-medium text-red-500">{serverMetrics.requests.failed}</div>
                                    </div>
                                  </div>
                                );
                              })()}
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </div>

                {/* Server Details */}
                <div>
                  {selectedServer ? (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Settings className="w-5 h-5" />
                          Server Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label>Name</Label>
                          <Input value={selectedServer.name} readOnly />
                        </div>

                        <div>
                          <Label>Type</Label>
                          <Input value={selectedServer.type} readOnly />
                        </div>

                        <div>
                          <Label>Status</Label>
                          <div className="flex items-center gap-2">
                            <Badge variant={selectedServer.status === 'running' ? 'default' : 'secondary'}>
                              {selectedServer.status}
                            </Badge>
                          </div>
                        </div>

                        <div>
                          <Label>Capabilities</Label>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedServer.capabilities.map((cap) => (
                              <Badge key={cap} variant="outline" className="text-xs">
                                {cap}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {selectedServer.status === 'running' && (() => {
                          const serverMetrics = getServerMetrics(selectedServer.id);
                          return serverMetrics && (
                            <>
                              <div>
                                <Label>Resource Usage</Label>
                                <div className="space-y-2 mt-2">
                                  <div>
                                    <div className="flex justify-between text-sm">
                                      <span>Memory</span>
                                      <span>{serverMetrics.resources.memory}MB</span>
                                    </div>
                                    <Progress value={(serverMetrics.resources.memory / 512) * 100} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between text-sm">
                                      <span>CPU</span>
                                      <span>{serverMetrics.resources.cpu}%</span>
                                    </div>
                                    <Progress value={serverMetrics.resources.cpu} className="h-2" />
                                  </div>
                                </div>
                              </div>

                              <div>
                                <Label>Performance Metrics</Label>
                                <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                                  <div className="text-center p-2 bg-muted rounded">
                                    <div className="font-bold text-green-600">{serverMetrics.requests.total}</div>
                                    <div className="text-xs">Requests</div>
                                  </div>
                                  <div className="text-center p-2 bg-muted rounded">
                                    <div className="font-bold text-blue-600">{Math.round(serverMetrics.requests.avgResponseTime)}ms</div>
                                    <div className="text-xs">Avg Response</div>
                                  </div>
                                </div>
                              </div>
                            </>
                          );
                        })()}

                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" className="flex-1">
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="flex-1"
                            onClick={() => handleRestartServer(selectedServer.id)}
                          >
                            <RotateCcw className="w-4 h-4 mr-2" />
                            Restart
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent className="p-8 text-center">
                        <Server className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          Select a server to view details and configuration
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="monitoring" className="h-full">
              <MCPAdvancedAnalytics />
            </TabsContent>

            <TabsContent value="ai-assistant" className="h-full">
              <MCPAIAssistant />
            </TabsContent>

            <TabsContent value="automation" className="h-full">
              <MCPIntelligentAutomation />
            </TabsContent>

            <TabsContent value="insights" className="h-full">
              <MCPPredictiveInsights />
            </TabsContent>

            <TabsContent value="templates" className="h-full">
              <div>
                <h3 className="text-lg font-semibold mb-4">Server Templates</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {templates.map((template) => {
                    const TypeIcon = getTypeIcon(template.type as MCPServer['type']);
                    
                    return (
                      <Card key={template.id}>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <TypeIcon className="w-5 h-5" />
                            {template.name}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {template.description}
                          </p>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div>
                              <Label className="text-xs">Capabilities</Label>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {template.capabilities.slice(0, 3).map((cap) => (
                                  <Badge key={cap} variant="outline" className="text-xs">
                                    {cap}
                                  </Badge>
                                ))}
                                {template.capabilities.length > 3 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{template.capabilities.length - 3} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <Button 
                              size="sm" 
                              className="w-full"
                              onClick={() => handleAddServerFromTemplate(template)}
                            >
                              <Plus className="w-4 h-4 mr-2" />
                              Create Server
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="h-full">
              <div className="max-w-2xl space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Global Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Auto-start servers on boot</Label>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Enable health monitoring</Label>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label>Collect performance metrics</Label>
                      <Switch defaultChecked />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Import/Export</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex gap-2">
                      <Button variant="outline" className="flex-1" asChild>
                        <label>
                          <Upload className="w-4 h-4 mr-2" />
                          Import Config
                          <input
                            type="file"
                            className="hidden"
                            accept=".json"
                            onChange={handleImportConfig}
                            disabled={isImporting}
                          />
                        </label>
                      </Button>
                      <Button 
                        variant="outline" 
                        className="flex-1"
                        onClick={handleExportConfig}
                        disabled={isExporting}
                      >
                        <Download className="w-4 h-4 mr-2" />
                        {isExporting ? 'Exporting...' : 'Export Config'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}