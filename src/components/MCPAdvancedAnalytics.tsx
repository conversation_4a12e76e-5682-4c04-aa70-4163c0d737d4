import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  BarChart3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Target,
  Brain
} from 'lucide-react';

export function MCPAdvancedAnalytics() {
  const { servers, getServerMetrics } = useMCPServers();
  const [analytics, setAnalytics] = useState({
    performanceScore: 0,
    efficiencyRating: 0,
    riskLevel: 0,
    optimizationPotential: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    calculateRealAnalytics();
  }, [servers]);

  const calculateRealAnalytics = async () => {
    setLoading(true);
    try {
      let totalPerformance = 0;
      let totalEfficiency = 0;
      let totalRisk = 0;
      let totalOptimization = 0;
      let activeServers = 0;

      for (const server of servers.filter(s => s.status === 'running')) {
        const metrics = getServerMetrics(server.id);
        if (metrics) {
          activeServers++;
          
          // Calculate real performance score
          const cpuScore = Math.max(0, 100 - metrics.resources.cpu);
          const responseScore = Math.max(0, 100 - (metrics.requests.avgResponseTime / 50));
          const errorScore = Math.max(0, 100 - (metrics.requests.failed / metrics.requests.total * 100));
          const performanceScore = (cpuScore + responseScore + errorScore) / 3;
          
          totalPerformance += performanceScore;
          totalEfficiency += Math.max(0, 100 - metrics.resources.memory / 10);
          totalRisk += Math.min(100, metrics.resources.cpu / 2 + (metrics.requests.failed / metrics.requests.total * 50));
          totalOptimization += Math.min(100, (100 - performanceScore) * 0.8);
        }
      }

      if (activeServers > 0) {
        setAnalytics({
          performanceScore: Math.round(totalPerformance / activeServers),
          efficiencyRating: Math.round(totalEfficiency / activeServers),
          riskLevel: Math.round(totalRisk / activeServers),
          optimizationPotential: Math.round(totalOptimization / activeServers)
        });
      }
    } catch (error) {
      console.error('Failed to calculate analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <BarChart3 className="w-8 h-8 mx-auto mb-2 text-blue-500" />
            <div className="text-2xl font-bold">{analytics.performanceScore}</div>
            <div className="text-sm text-muted-foreground">Performance Score</div>
            <Progress value={analytics.performanceScore} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 mx-auto mb-2 text-green-500" />
            <div className="text-2xl font-bold">{analytics.efficiencyRating}%</div>
            <div className="text-sm text-muted-foreground">Efficiency Rating</div>
            <Progress value={analytics.efficiencyRating} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <AlertTriangle className="w-8 h-8 mx-auto mb-2 text-yellow-500" />
            <div className="text-2xl font-bold">{analytics.riskLevel}%</div>
            <div className="text-sm text-muted-foreground">Risk Level</div>
            <Progress value={analytics.riskLevel} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Brain className="w-8 h-8 mx-auto mb-2 text-purple-500" />
            <div className="text-2xl font-bold">{analytics.optimizationPotential}%</div>
            <div className="text-sm text-muted-foreground">Optimization Potential</div>
            <Progress value={analytics.optimizationPotential} className="mt-2 h-1" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}