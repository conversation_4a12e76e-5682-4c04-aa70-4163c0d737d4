import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { 
  Bot, 
  FolderCode, 
  FileCode2, 
  Activity, 
  Puzzle,
  Sparkles,
  Plus,
  X,
  Save,
  Brain,
  Target,
  Zap
} from 'lucide-react';
import { useFocusMode } from '@/hooks/useFocusMode';
import { useFeatureContext } from '@/hooks/useFeatureContext';
import { cn } from '@/lib/utils';

interface SmartFeatureSelectionProps {
  currentView: string;
  activeProject?: string | null;
}

interface FeatureInfo {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'essential' | 'productivity' | 'advanced' | 'experimental';
  usage: number;
}

const FEATURES: FeatureInfo[] = [
  {
    id: 'agents',
    name: 'CC Agents',
    description: 'Create and manage AI agents',
    icon: <Bot className="w-5 h-5" />,
    category: 'essential',
    usage: 0
  },
  {
    id: 'projects',
    name: 'CC Projects',
    description: 'Browse and manage projects',
    icon: <FolderCode className="w-5 h-5" />,
    category: 'essential',
    usage: 0
  },
  {
    id: 'templates',
    name: 'Smart Templates',
    description: 'AI-powered code templates',
    icon: <FileCode2 className="w-5 h-5" />,
    category: 'productivity',
    usage: 0
  },
  {
    id: 'performance',
    name: 'Performance',
    description: 'Monitor API usage and costs',
    icon: <Activity className="w-5 h-5" />,
    category: 'advanced',
    usage: 0
  },
  {
    id: 'plugins',
    name: 'Plugins',
    description: 'Extend with custom plugins',
    icon: <Puzzle className="w-5 h-5" />,
    category: 'experimental',
    usage: 0
  },
  {
    id: 'agent-system',
    name: 'Agent System',
    description: 'Advanced agent workflows',
    icon: <Brain className="w-5 h-5" />,
    category: 'experimental',
    usage: 0
  }
];

export function SmartFeatureSelection({ 
  currentView, 
  activeProject
}: SmartFeatureSelectionProps) {
  const { 
    config,
    updateEssentialFeatures,
    saveCustomPreset
  } = useFocusMode();
  
  const { 
    suggestedFeatures,
    projectType
  } = useFeatureContext(currentView, activeProject);

  const [selectedFeatures, setSelectedFeatures] = useState<string[]>(config.essentialFeatures);
  const [presetName, setPresetName] = useState('');
  const [showSavePreset, setShowSavePreset] = useState(false);

  useEffect(() => {
    setSelectedFeatures(config.essentialFeatures);
  }, [config.essentialFeatures]);

  const handleFeatureToggle = (featureId: string) => {
    setSelectedFeatures(prev => {
      if (prev.includes(featureId)) {
        return prev.filter(f => f !== featureId);
      }
      return [...prev, featureId];
    });
  };

  const handleSaveSelection = () => {
    updateEssentialFeatures(selectedFeatures);
  };

  const handleSaveAsPreset = () => {
    if (presetName.trim()) {
      saveCustomPreset({
        id: `custom-${Date.now()}`,
        name: presetName.trim(),
        description: `Custom preset with ${selectedFeatures.length} features`,
        features: selectedFeatures
      });
      setPresetName('');
      setShowSavePreset(false);
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'essential': return 'text-blue-500';
      case 'productivity': return 'text-green-500';
      case 'advanced': return 'text-orange-500';
      case 'experimental': return 'text-purple-500';
      default: return 'text-gray-500';
    }
  };

  const getSuggestionReason = (featureId: string) => {
    if (!suggestedFeatures.includes(featureId)) return null;
    
    if (featureId === 'performance' && projectType === 'web-backend') {
      return 'Recommended for API projects';
    }
    if (featureId === 'templates' && projectType === 'web-frontend') {
      return 'Popular with frontend projects';
    }
    if (featureId === currentView) {
      return 'Currently active';
    }
    
    return 'Frequently used together';
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5" />
          Smart Feature Selection
        </CardTitle>
        <CardDescription>
          Choose which features to show in focus mode. AI recommendations are based on your current context.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* AI Recommendations */}
        {suggestedFeatures.length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-primary" />
              <Label className="text-sm font-medium">AI Recommendations</Label>
            </div>
            <div className="flex flex-wrap gap-2">
              {suggestedFeatures.map(featureId => {
                const feature = FEATURES.find(f => f.id === featureId);
                if (!feature) return null;
                
                return (
                  <motion.div
                    key={featureId}
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleFeatureToggle(featureId)}
                      className={cn(
                        "gap-2",
                        selectedFeatures.includes(featureId) && "bg-primary/10 border-primary"
                      )}
                    >
                      {feature.icon}
                      <span>{feature.name}</span>
                      {selectedFeatures.includes(featureId) && (
                        <Zap className="w-3 h-3 text-primary" />
                      )}
                    </Button>
                  </motion.div>
                );
              })}
            </div>
          </div>
        )}

        <Separator />

        {/* Feature Categories */}
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-6">
            {['essential', 'productivity', 'advanced', 'experimental'].map(category => {
              const categoryFeatures = FEATURES.filter(f => f.category === category);
              
              return (
                <div key={category} className="space-y-3">
                  <Label className={cn("text-sm font-medium capitalize", getCategoryColor(category))}>
                    {category} Features
                  </Label>
                  <div className="space-y-2">
                    {categoryFeatures.map(feature => {
                      const isSelected = selectedFeatures.includes(feature.id);
                      const suggestion = getSuggestionReason(feature.id);
                      
                      return (
                        <motion.div
                          key={feature.id}
                          initial={false}
                          animate={{
                            backgroundColor: isSelected ? 'hsl(var(--primary) / 0.05)' : 'transparent'
                          }}
                          className="flex items-center justify-between p-3 rounded-lg border transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "p-2 rounded-md",
                              isSelected ? "bg-primary/10" : "bg-muted"
                            )}>
                              {feature.icon}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{feature.name}</span>
                                {suggestion && (
                                  <Badge variant="secondary" className="text-xs">
                                    <Sparkles className="w-3 h-3 mr-1" />
                                    {suggestion}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {feature.description}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={isSelected}
                            onCheckedChange={() => handleFeatureToggle(feature.id)}
                          />
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>

        {/* Actions */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {selectedFeatures.length} features selected
          </div>
          <div className="flex gap-2">
            {showSavePreset ? (
              <div className="flex items-center gap-2">
                <Input
                  value={presetName}
                  onChange={(e) => setPresetName(e.target.value)}
                  placeholder="Preset name"
                  className="w-32 h-8"
                  onKeyDown={(e) => e.key === 'Enter' && handleSaveAsPreset()}
                />
                <Button size="sm" onClick={handleSaveAsPreset} disabled={!presetName.trim()}>
                  <Save className="w-3 h-3" />
                </Button>
                <Button size="sm" variant="ghost" onClick={() => setShowSavePreset(false)}>
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSavePreset(true)}
                >
                  <Plus className="w-3 h-3 mr-1" />
                  Save as Preset
                </Button>
                <Button
                  size="sm"
                  onClick={handleSaveSelection}
                >
                  Apply Selection
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}