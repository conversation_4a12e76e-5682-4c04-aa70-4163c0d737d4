import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ap,
  Eye,
  Palette,
  <PERSON>tings,
  Monitor,
  Moon,
  Sun,
  Download,
  Upload,
  RefreshCw,
  Save,
  X,
  Check,
  Copy,
  Trash2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area';
import { api } from '@/lib/api';
import { toast } from 'sonner';

interface ThemeConfig {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    muted: string;
    border: string;
  };
  animations: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
  borderRadius: string;
  fontFamily: string;
  fontSize: string;
}

interface ColorScheme {
  id: string;
  name: string;
  colors: Record<string, string>;
}

export function VisualEffectsManagerReal() {
  const [currentTheme, setCurrentTheme] = useState<ThemeConfig | null>(null);
  const [availableThemes, setAvailableThemes] = useState<string[]>([]);
  const [colorSchemes, setColorSchemes] = useState<ColorScheme[]>([]);
  const [customThemes, setCustomThemes] = useState<ThemeConfig[]>([]);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editingTheme, setEditingTheme] = useState<ThemeConfig | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadThemeData();
  }, []);

  useEffect(() => {
    // Apply theme changes to document
    if (currentTheme) {
      applyThemeToDocument(currentTheme);
    }
  }, [currentTheme]);

  const loadThemeData = async () => {
    try {
      setLoading(true);
      
      // Load current theme configuration
      const themeConfig = await api.getThemeConfig();
      setCurrentTheme(themeConfig.current);
      setIsDarkMode(themeConfig.darkMode || false);
      
      // Load available color schemes
      const schemes = await api.getAvailableColorSchemes();
      setColorSchemes(schemes);
      
      // Load custom themes
      const customList = await api.listCustomThemes();
      setCustomThemes(customList);
      
      // Extract available theme names
      const themeNames = [
        'default',
        ...schemes.map((s: ColorScheme) => s.name),
        ...customList.map((t: ThemeConfig) => t.name)
      ];
      setAvailableThemes(themeNames);
      
    } catch (error) {
      console.error('Failed to load theme data:', error);
      toast.error('Failed to load theme configuration');
    } finally {
      setLoading(false);
    }
  };

  const applyThemeToDocument = (theme: ThemeConfig) => {
    const root = document.documentElement;
    
    // Apply colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });
    
    // Apply other properties
    root.style.setProperty('--border-radius', theme.borderRadius);
    root.style.setProperty('--font-family', theme.fontFamily);
    root.style.setProperty('--font-size', theme.fontSize);
    
    // Apply animation settings
    if (theme.animations.enabled) {
      root.style.setProperty('--animation-duration', `${theme.animations.duration}ms`);
      root.style.setProperty('--animation-easing', theme.animations.easing);
    } else {
      root.style.setProperty('--animation-duration', '0ms');
    }
  };

  const saveTheme = async () => {
    if (!currentTheme) return;
    
    try {
      await api.setThemeConfig({
        current: currentTheme,
        darkMode: isDarkMode,
        customThemes
      });
      
      toast.success('Theme saved successfully');
    } catch (error) {
      console.error('Failed to save theme:', error);
      toast.error('Failed to save theme');
    }
  };

  const applyColorScheme = async (schemeId: string) => {
    const scheme = colorSchemes.find(s => s.id === schemeId);
    if (!scheme || !currentTheme) return;
    
    const updatedTheme = {
      ...currentTheme,
      colors: {
        ...currentTheme.colors,
        ...scheme.colors
      }
    };
    
    setCurrentTheme(updatedTheme);
    await saveTheme();
  };

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle('dark');
  };

  const importTheme = async () => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      
      input.onchange = async (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (!file) return;
        
        const text = await file.text();
        const theme = JSON.parse(text) as ThemeConfig;
        
        await api.importTheme(theme);
        setCustomThemes([...customThemes, theme]);
        toast.success('Theme imported successfully');
      };
      
      input.click();
    } catch (error) {
      console.error('Failed to import theme:', error);
      toast.error('Failed to import theme');
    }
  };

  const exportTheme = async () => {
    if (!currentTheme) return;
    
    try {
      const exported = await api.exportTheme(currentTheme.name);
      
      const blob = new Blob([JSON.stringify(exported, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentTheme.name}-theme.json`;
      a.click();
      URL.revokeObjectURL(url);
      
      toast.success('Theme exported successfully');
    } catch (error) {
      console.error('Failed to export theme:', error);
      toast.error('Failed to export theme');
    }
  };

  const startEditingTheme = () => {
    setIsEditing(true);
    setEditingTheme(currentTheme ? { ...currentTheme } : createDefaultTheme());
  };

  const saveEditedTheme = async () => {
    if (!editingTheme) return;
    
    setCurrentTheme(editingTheme);
    setIsEditing(false);
    await saveTheme();
  };

  const createDefaultTheme = (): ThemeConfig => ({
    name: 'Custom Theme',
    colors: {
      primary: '#3b82f6',
      secondary: '#8b5cf6',
      accent: '#10b981',
      background: '#ffffff',
      foreground: '#000000',
      muted: '#f3f4f6',
      border: '#e5e7eb'
    },
    animations: {
      enabled: true,
      duration: 200,
      easing: 'ease-in-out'
    },
    borderRadius: '0.5rem',
    fontFamily: 'system-ui, -apple-system, sans-serif',
    fontSize: '16px'
  });

  const updateEditingColor = (key: string, value: string) => {
    if (!editingTheme) return;
    
    setEditingTheme({
      ...editingTheme,
      colors: {
        ...editingTheme.colors,
        [key]: value
      }
    });
  };

  const copyColorToClipboard = (color: string) => {
    navigator.clipboard.writeText(color);
    toast.success('Color copied to clipboard');
  };

  const resetToDefault = async () => {
    try {
      const defaultTheme = createDefaultTheme();
      defaultTheme.name = 'default';
      setCurrentTheme(defaultTheme);
      await saveTheme();
      toast.success('Reset to default theme');
    } catch (error) {
      console.error('Failed to reset theme:', error);
      toast.error('Failed to reset theme');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-6 border-b">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Visual Effects Manager</h2>
            <p className="text-muted-foreground">
              Customize themes, colors, and visual effects
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={toggleDarkMode}
            >
              {isDarkMode ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={saveTheme}
            >
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 p-6 overflow-auto">
        <Tabs defaultValue="theme" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="theme">Theme</TabsTrigger>
            <TabsTrigger value="colors">Colors</TabsTrigger>
            <TabsTrigger value="effects">Effects</TabsTrigger>
            <TabsTrigger value="presets">Presets</TabsTrigger>
          </TabsList>

          <TabsContent value="theme" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Theme Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label>Current Theme</Label>
                  <Select value={currentTheme?.name || 'default'} onValueChange={(value) => {
                    const theme = customThemes.find(t => t.name === value) || createDefaultTheme();
                    theme.name = value;
                    setCurrentTheme(theme);
                  }}>
                    <SelectTrigger className="mt-2">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableThemes.map(theme => (
                        <SelectItem key={theme} value={theme}>
                          {theme}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div>
                    <Label>Border Radius</Label>
                    <Select value={currentTheme?.borderRadius || '0.5rem'} onValueChange={(value) => {
                      if (currentTheme) {
                        setCurrentTheme({ ...currentTheme, borderRadius: value });
                      }
                    }}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">None</SelectItem>
                        <SelectItem value="0.25rem">Small</SelectItem>
                        <SelectItem value="0.5rem">Medium</SelectItem>
                        <SelectItem value="0.75rem">Large</SelectItem>
                        <SelectItem value="1rem">Extra Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Font Size</Label>
                    <Select value={currentTheme?.fontSize || '16px'} onValueChange={(value) => {
                      if (currentTheme) {
                        setCurrentTheme({ ...currentTheme, fontSize: value });
                      }
                    }}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="14px">Small</SelectItem>
                        <SelectItem value="16px">Medium</SelectItem>
                        <SelectItem value="18px">Large</SelectItem>
                        <SelectItem value="20px">Extra Large</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Separator />

                <div className="flex gap-2">
                  <Button variant="outline" onClick={importTheme}>
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </Button>
                  <Button variant="outline" onClick={exportTheme}>
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                  <Button variant="outline" onClick={resetToDefault}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="colors" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Color Palette</CardTitle>
                <Button
                  variant={isEditing ? 'default' : 'outline'}
                  size="sm"
                  onClick={isEditing ? saveEditedTheme : startEditingTheme}
                >
                  {isEditing ? (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  ) : (
                    <>
                      <Palette className="h-4 w-4 mr-2" />
                      Edit Colors
                    </>
                  )}
                </Button>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  {currentTheme && Object.entries(isEditing ? editingTheme?.colors || {} : currentTheme.colors).map(([key, value]) => (
                    <div key={key} className="space-y-2">
                      <Label className="capitalize">{key}</Label>
                      <div className="flex gap-2">
                        {isEditing ? (
                          <Input
                            type="color"
                            value={value}
                            onChange={(e) => updateEditingColor(key, e.target.value)}
                            className="h-10 w-20"
                          />
                        ) : (
                          <div
                            className="h-10 w-20 rounded border"
                            style={{ backgroundColor: value }}
                          />
                        )}
                        <Input
                          value={value}
                          onChange={(e) => isEditing && updateEditingColor(key, e.target.value)}
                          readOnly={!isEditing}
                          className="flex-1 font-mono text-sm"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => copyColorToClipboard(value)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="effects" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Animation Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Enable Animations</Label>
                    <p className="text-sm text-muted-foreground">
                      Smooth transitions and motion effects
                    </p>
                  </div>
                  <Switch
                    checked={currentTheme?.animations.enabled || false}
                    onCheckedChange={(checked) => {
                      if (currentTheme) {
                        setCurrentTheme({
                          ...currentTheme,
                          animations: { ...currentTheme.animations, enabled: checked }
                        });
                      }
                    }}
                  />
                </div>

                {currentTheme?.animations.enabled && (
                  <>
                    <div className="space-y-2">
                      <Label>Animation Duration</Label>
                      <div className="flex items-center gap-4">
                        <Slider
                          value={[currentTheme.animations.duration]}
                          onValueChange={([value]) => {
                            setCurrentTheme({
                              ...currentTheme,
                              animations: { ...currentTheme.animations, duration: value }
                            });
                          }}
                          min={100}
                          max={1000}
                          step={50}
                          className="flex-1"
                        />
                        <span className="text-sm w-16 text-right">
                          {currentTheme.animations.duration}ms
                        </span>
                      </div>
                    </div>

                    <div>
                      <Label>Easing Function</Label>
                      <Select
                        value={currentTheme.animations.easing}
                        onValueChange={(value) => {
                          setCurrentTheme({
                            ...currentTheme,
                            animations: { ...currentTheme.animations, easing: value }
                          });
                        }}
                      >
                        <SelectTrigger className="mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="linear">Linear</SelectItem>
                          <SelectItem value="ease">Ease</SelectItem>
                          <SelectItem value="ease-in">Ease In</SelectItem>
                          <SelectItem value="ease-out">Ease Out</SelectItem>
                          <SelectItem value="ease-in-out">Ease In Out</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Visual Effects</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert>
                  <Sparkles className="h-4 w-4" />
                  <AlertDescription>
                    Advanced visual effects like particles and 3D animations can be enabled through plugins
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="presets" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Color Scheme Presets</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="grid grid-cols-2 gap-4">
                    {colorSchemes.map(scheme => (
                      <motion.div
                        key={scheme.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card
                          className="cursor-pointer transition-all hover:shadow-lg"
                          onClick={() => applyColorScheme(scheme.id)}
                        >
                          <CardContent className="p-4">
                            <h4 className="font-medium mb-3">{scheme.name}</h4>
                            <div className="grid grid-cols-4 gap-1">
                              {Object.entries(scheme.colors).slice(0, 4).map(([key, color]) => (
                                <div
                                  key={key}
                                  className="h-8 rounded"
                                  style={{ backgroundColor: color }}
                                  title={key}
                                />
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}