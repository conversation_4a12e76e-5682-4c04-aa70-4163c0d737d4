import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Upload,
  // Play,
  // Pause,
  RotateCcw,
  Download,
  Brain,
  Code,
  FileText,
  Search,
  TestTube,
  Shield,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  // ChevronRight,
  // Split,
  // Users
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Task types and interfaces
export interface Task {
  id: string;
  content: string;
  type: TaskType;
  priority: Priority;
  status: TaskStatus;
  assignedAgent: AgentType | null;
  progress: number;
  startedAt?: Date;
  completedAt?: Date;
  result?: string;
  error?: string;
  metadata?: Record<string, any>;
  dependencies?: string[];
  estimatedTime?: number;
}

export type TaskType = 
  | 'code-generation'
  | 'code-review'
  | 'bug-fix'
  | 'documentation'
  | 'testing'
  | 'optimization'
  | 'security-audit'
  | 'refactoring'
  | 'analysis'
  | 'research';

export type Priority = 'critical' | 'high' | 'medium' | 'low';

export type TaskStatus = 
  | 'pending'
  | 'analyzing'
  | 'assigned'
  | 'in-progress'
  | 'completed'
  | 'failed'
  | 'cancelled';

export type AgentType = 
  | 'architect'
  | 'developer'
  | 'reviewer'
  | 'tester'
  | 'security'
  | 'documentation'
  | 'optimizer'
  | 'researcher';

interface TaskGroup {
  id: string;
  name: string;
  tasks: Task[];
  type: TaskType;
  agent: AgentType;
}

interface BulkTaskManagerProps {
  onClose?: () => void;
}

// Agent capabilities mapping
const AGENT_CAPABILITIES: Record<AgentType, TaskType[]> = {
  architect: ['analysis', 'code-generation', 'refactoring'],
  developer: ['code-generation', 'bug-fix', 'refactoring'],
  reviewer: ['code-review', 'analysis'],
  tester: ['testing'],
  security: ['security-audit', 'code-review'],
  documentation: ['documentation'],
  optimizer: ['optimization', 'refactoring'],
  researcher: ['research', 'analysis']
};

// Task type icons
const TASK_TYPE_ICONS: Record<TaskType, React.ElementType> = {
  'code-generation': Code,
  'code-review': Search,
  'bug-fix': AlertCircle,
  'documentation': FileText,
  'testing': TestTube,
  'optimization': Zap,
  'security-audit': Shield,
  'refactoring': RotateCcw,
  'analysis': Brain,
  'research': Search
};

// Agent icons
const AGENT_ICONS: Record<AgentType, React.ElementType> = {
  architect: Brain,
  developer: Code,
  reviewer: Search,
  tester: TestTube,
  security: Shield,
  documentation: FileText,
  optimizer: Zap,
  researcher: Search
};

export function BulkTaskManager({ onClose }: BulkTaskManagerProps) {
  const [input, setInput] = useState('');
  const [tasks, setTasks] = useState<Task[]>([]);
  const [taskGroups, setTaskGroups] = useState<TaskGroup[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState('input');
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [overallProgress, setOverallProgress] = useState(0);

  // Parse and analyze input to create tasks
  const analyzeInput = useCallback(async (text: string) => {
    setIsProcessing(true);
    setActiveTab('tasks');

    // Split input into lines and analyze
    const lines = text.split('\n').filter(line => line.trim());
    const analyzedTasks: Task[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Analyze line to determine task type and priority
      const task = await analyzeLine(line, i);
      analyzedTasks.push(task);
    }

    // Group tasks by type and assign agents
    const groups = groupTasksByType(analyzedTasks);
    
    setTasks(analyzedTasks);
    setTaskGroups(groups);
    setIsProcessing(false);

    // Start processing tasks
    processTasksInParallel(groups);
  }, []);

  // Analyze a single line to create a task
  const analyzeLine = async (line: string, index: number): Promise<Task> => {
    const lowerLine = line.toLowerCase();
    
    // Determine task type based on keywords
    let type: TaskType = 'analysis';
    let priority: Priority = 'medium';
    let estimatedTime = 30; // seconds

    if (lowerLine.includes('fix') || lowerLine.includes('bug') || lowerLine.includes('error')) {
      type = 'bug-fix';
      priority = 'high';
      estimatedTime = 120;
    } else if (lowerLine.includes('create') || lowerLine.includes('generate') || lowerLine.includes('implement')) {
      type = 'code-generation';
      priority = 'high';
      estimatedTime = 180;
    } else if (lowerLine.includes('test') || lowerLine.includes('verify')) {
      type = 'testing';
      estimatedTime = 90;
    } else if (lowerLine.includes('document') || lowerLine.includes('comment')) {
      type = 'documentation';
      priority = 'low';
      estimatedTime = 60;
    } else if (lowerLine.includes('review') || lowerLine.includes('check')) {
      type = 'code-review';
      estimatedTime = 90;
    } else if (lowerLine.includes('optimize') || lowerLine.includes('performance')) {
      type = 'optimization';
      estimatedTime = 150;
    } else if (lowerLine.includes('security') || lowerLine.includes('vulnerability')) {
      type = 'security-audit';
      priority = 'critical';
      estimatedTime = 180;
    } else if (lowerLine.includes('refactor') || lowerLine.includes('restructure')) {
      type = 'refactoring';
      estimatedTime = 120;
    } else if (lowerLine.includes('research') || lowerLine.includes('investigate')) {
      type = 'research';
      estimatedTime = 240;
    }

    // Check for priority indicators
    if (lowerLine.includes('urgent') || lowerLine.includes('critical') || lowerLine.includes('asap')) {
      priority = 'critical';
    } else if (lowerLine.includes('important') || lowerLine.includes('high priority')) {
      priority = 'high';
    } else if (lowerLine.includes('low priority') || lowerLine.includes('when possible')) {
      priority = 'low';
    }

    return {
      id: `task-${Date.now()}-${index}`,
      content: line,
      type,
      priority,
      status: 'pending',
      assignedAgent: null,
      progress: 0,
      estimatedTime,
      metadata: {
        lineNumber: index + 1,
        originalText: line
      }
    };
  };

  // Group tasks by type and assign to appropriate agents
  const groupTasksByType = (tasks: Task[]): TaskGroup[] => {
    const groups: Record<TaskType, Task[]> = {} as any;
    
    // Group tasks by type
    tasks.forEach(task => {
      if (!groups[task.type]) {
        groups[task.type] = [];
      }
      groups[task.type].push(task);
    });

    // Create task groups with assigned agents
    return Object.entries(groups).map(([type, tasks]) => {
      const taskType = type as TaskType;
      const agent = findBestAgentForTaskType(taskType);
      
      // Update tasks with assigned agent
      tasks.forEach(task => {
        task.assignedAgent = agent;
        task.status = 'assigned';
      });

      return {
        id: `group-${type}-${Date.now()}`,
        name: `${type.replace('-', ' ').toUpperCase()} Tasks`,
        tasks,
        type: taskType,
        agent
      };
    });
  };

  // Find the best agent for a task type
  const findBestAgentForTaskType = (type: TaskType): AgentType => {
    for (const [agent, capabilities] of Object.entries(AGENT_CAPABILITIES)) {
      if (capabilities.includes(type)) {
        return agent as AgentType;
      }
    }
    return 'developer'; // Default fallback
  };

  // Process tasks in parallel by agent
  const processTasksInParallel = async (groups: TaskGroup[]) => {
    const processingPromises = groups.map(group => processTaskGroup(group));
    await Promise.all(processingPromises);
  };

  // Process a group of tasks
  const processTaskGroup = async (group: TaskGroup) => {
    for (const task of group.tasks) {
      await processTask(task);
    }
  };

  // Process individual task
  const processTask = async (task: Task) => {
    // Update task status
    setTasks(prev => prev.map(t => 
      t.id === task.id 
        ? { ...t, status: 'in-progress', startedAt: new Date() }
        : t
    ));

    // Simulate processing with progress updates
    const steps = 10;
    for (let i = 1; i <= steps; i++) {
      await new Promise(resolve => setTimeout(resolve, (task.estimatedTime || 30) * 100 / steps));
      
      setTasks(prev => prev.map(t => 
        t.id === task.id 
          ? { ...t, progress: (i / steps) * 100 }
          : t
      ));
    }

    // Complete task
    setTasks(prev => prev.map(t => 
      t.id === task.id 
        ? { 
            ...t, 
            status: 'completed', 
            progress: 100,
            completedAt: new Date(),
            result: `Task completed successfully by ${t.assignedAgent}`
          }
        : t
    ));

    // Update overall progress
    updateOverallProgress();
  };

  // Update overall progress
  const updateOverallProgress = () => {
    setTasks(prev => {
      const completed = prev.filter(t => t.status === 'completed').length;
      const total = prev.length;
      const progress = total > 0 ? (completed / total) * 100 : 0;
      setOverallProgress(progress);
      return prev;
    });
  };

  // Export results
  const exportResults = () => {
    const results = tasks.map(task => ({
      id: task.id,
      content: task.content,
      type: task.type,
      status: task.status,
      agent: task.assignedAgent,
      result: task.result,
      startedAt: task.startedAt,
      completedAt: task.completedAt
    }));

    const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `task-results-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Load file
  const handleFileLoad = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setInput(content);
    };
    reader.readAsText(file);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">AI Task Manager</h2>
            <p className="text-sm text-muted-foreground">
              Process thousands of tasks with intelligent agent delegation
            </p>
          </div>
          <div className="flex items-center gap-2">
            {tasks.length > 0 && (
              <div className="flex items-center gap-4">
                <div className="text-sm">
                  <span className="text-muted-foreground">Overall Progress:</span>
                  <span className="ml-2 font-medium">{Math.round(overallProgress)}%</span>
                </div>
                <Progress value={overallProgress} className="w-32" />
              </div>
            )}
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="mx-4 mt-4">
            <TabsTrigger value="input">Input</TabsTrigger>
            <TabsTrigger value="tasks" disabled={tasks.length === 0}>
              Tasks ({tasks.length})
            </TabsTrigger>
            <TabsTrigger value="agents" disabled={taskGroups.length === 0}>
              Agents ({taskGroups.length})
            </TabsTrigger>
            <TabsTrigger value="results" disabled={tasks.length === 0}>
              Results
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden">
            <TabsContent value="input" className="h-full p-4">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle>Task Input</CardTitle>
                  <CardDescription>
                    Paste up to 5000 lines of tasks or load from a file
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col gap-4">
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" asChild>
                      <label>
                        <Upload className="w-4 h-4 mr-2" />
                        Load File
                        <input
                          type="file"
                          className="hidden"
                          accept=".txt,.md,.csv"
                          onChange={handleFileLoad}
                        />
                      </label>
                    </Button>
                    <Button 
                      size="sm"
                      onClick={() => analyzeInput(input)}
                      disabled={!input.trim() || isProcessing}
                    >
                      {isProcessing ? (
                        <>
                          <Clock className="w-4 h-4 mr-2 animate-spin" />
                          Analyzing...
                        </>
                      ) : (
                        <>
                          <Brain className="w-4 h-4 mr-2" />
                          Analyze & Process
                        </>
                      )}
                    </Button>
                  </div>
                  
                  <Textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Paste your tasks here, one per line..."
                    className="flex-1 font-mono text-sm"
                  />
                  
                  <div className="text-sm text-muted-foreground">
                    {input.split('\n').filter(line => line.trim()).length} lines
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tasks" className="h-full p-4">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle>Task Overview</CardTitle>
                  <CardDescription>
                    All parsed and categorized tasks
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-1 overflow-hidden">
                  <ScrollArea className="h-full">
                    <div className="space-y-2">
                      {tasks.map((task) => {
                        const Icon = TASK_TYPE_ICONS[task.type];
                        const statusColor = {
                          pending: 'text-gray-500',
                          analyzing: 'text-blue-500',
                          assigned: 'text-purple-500',
                          'in-progress': 'text-yellow-500',
                          completed: 'text-green-500',
                          failed: 'text-red-500',
                          cancelled: 'text-gray-400'
                        }[task.status];

                        return (
                          <motion.div
                            key={task.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            className={cn(
                              "p-3 rounded-lg border cursor-pointer transition-colors",
                              selectedTask?.id === task.id && "border-primary bg-accent"
                            )}
                            onClick={() => setSelectedTask(task)}
                          >
                            <div className="flex items-start gap-3">
                              <Icon className={cn("w-5 h-5 mt-0.5", statusColor)} />
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">
                                    {task.type}
                                  </Badge>
                                  <Badge 
                                    variant={task.priority === 'critical' ? 'destructive' : 'secondary'}
                                    className="text-xs"
                                  >
                                    {task.priority}
                                  </Badge>
                                  {task.assignedAgent && (
                                    <Badge variant="outline" className="text-xs">
                                      {task.assignedAgent}
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm mt-1 line-clamp-2">{task.content}</p>
                                {task.status === 'in-progress' && (
                                  <Progress value={task.progress} className="mt-2 h-1" />
                                )}
                              </div>
                              <div className="text-right">
                                <div className={cn("text-xs font-medium", statusColor)}>
                                  {task.status.replace('-', ' ')}
                                </div>
                                {task.estimatedTime && task.status === 'pending' && (
                                  <div className="text-xs text-muted-foreground mt-1">
                                    ~{task.estimatedTime}s
                                  </div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="agents" className="h-full p-4">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Agent Workload</CardTitle>
                  <CardDescription>
                    Task distribution across AI agents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {taskGroups.map((group) => {
                      const Icon = AGENT_ICONS[group.agent];
                      const completedTasks = group.tasks.filter(t => t.status === 'completed').length;
                      const progress = group.tasks.length > 0 
                        ? (completedTasks / group.tasks.length) * 100 
                        : 0;

                      return (
                        <Card key={group.id}>
                          <CardHeader className="pb-3">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Icon className="w-5 h-5" />
                                <CardTitle className="text-base">{group.agent}</CardTitle>
                              </div>
                              <Badge variant="secondary">
                                {group.tasks.length} tasks
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <Progress value={progress} />
                              <div className="text-sm text-muted-foreground">
                                {completedTasks} of {group.tasks.length} completed
                              </div>
                              <div className="pt-2 space-y-1">
                                {group.tasks.slice(0, 3).map(task => (
                                  <div key={task.id} className="text-xs truncate">
                                    • {task.content}
                                  </div>
                                ))}
                                {group.tasks.length > 3 && (
                                  <div className="text-xs text-muted-foreground">
                                    +{group.tasks.length - 3} more...
                                  </div>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results" className="h-full p-4">
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Results</CardTitle>
                      <CardDescription>
                        Task completion results and outputs
                      </CardDescription>
                    </div>
                    <Button size="sm" variant="outline" onClick={exportResults}>
                      <Download className="w-4 h-4 mr-2" />
                      Export Results
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="flex-1 overflow-hidden">
                  <ScrollArea className="h-full">
                    <div className="space-y-4">
                      {tasks
                        .filter(task => task.status === 'completed' || task.status === 'failed')
                        .map((task) => (
                          <Card key={task.id}>
                            <CardHeader className="pb-3">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  {task.status === 'completed' ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                  ) : (
                                    <XCircle className="w-4 h-4 text-red-500" />
                                  )}
                                  <span className="text-sm font-medium">
                                    Task #{task.metadata?.lineNumber}
                                  </span>
                                </div>
                                <Badge variant="outline" className="text-xs">
                                  {task.assignedAgent}
                                </Badge>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-2">
                                <div>
                                  <div className="text-xs text-muted-foreground">Input:</div>
                                  <div className="text-sm">{task.content}</div>
                                </div>
                                {task.result && (
                                  <div>
                                    <div className="text-xs text-muted-foreground">Result:</div>
                                    <div className="text-sm text-green-600 dark:text-green-400">
                                      {task.result}
                                    </div>
                                  </div>
                                )}
                                {task.error && (
                                  <div>
                                    <div className="text-xs text-muted-foreground">Error:</div>
                                    <div className="text-sm text-red-600 dark:text-red-400">
                                      {task.error}
                                    </div>
                                  </div>
                                )}
                                <div className="text-xs text-muted-foreground">
                                  Duration: {task.startedAt && task.completedAt 
                                    ? `${Math.round((task.completedAt.getTime() - task.startedAt.getTime()) / 1000)}s`
                                    : 'N/A'
                                  }
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}