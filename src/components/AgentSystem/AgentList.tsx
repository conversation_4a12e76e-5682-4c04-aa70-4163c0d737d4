import { memo } from 'react';
import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Agent } from './types';
import { Settings2, Pause, Play, Trash2 } from 'lucide-react';

interface AgentListProps {
  agents: Agent[];
  selectedAgent: Agent | null;
  onSelectAgent: (agent: Agent) => void;
  onConfigureAgent: (agent: Agent) => void;
  onPauseAgent: (agentId: string) => void;
  onResumeAgent: (agentId: string) => void;
  onRemoveAgent: (agentId: string) => void;
}

export const AgentList = memo(function AgentList({
  agents,
  selectedAgent,
  onSelectAgent,
  onConfigureAgent,
  onPauseAgent,
  onResumeAgent,
  onRemoveAgent
}: AgentListProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'idle':
        return 'bg-gray-500';
      case 'working':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'paused':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getAgentInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (agents.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No agents deployed yet</p>
        <p className="text-sm text-muted-foreground mt-1">
          Select an agent type from the catalog to deploy
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {agents.map((agent) => (
        <div
          key={agent.id}
          onClick={() => onSelectAgent(agent)}
          className={`p-4 rounded-lg border cursor-pointer transition-all ${
            selectedAgent?.id === agent.id
              ? 'border-primary bg-primary/5'
              : 'border-border hover:border-primary/50 hover:bg-accent/50'
          }`}
        >
          <div className="flex items-start gap-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="text-xs">
                {getAgentInitials(agent.name)}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-sm">{agent.name}</h4>
                  <p className="text-xs text-muted-foreground">{agent.type}</p>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(agent.status)}`} />
                  <Badge variant={agent.status === 'working' ? 'default' : 'secondary'}>
                    {agent.status}
                  </Badge>
                </div>
              </div>
              
              {agent.currentTask && (
                <div className="mt-2">
                  <p className="text-xs text-muted-foreground mb-1">
                    Current: {agent.currentTask}
                  </p>
                  <Progress value={75} className="h-1" />
                </div>
              )}
              
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>Tasks: {agent.tasksCompleted}</span>
                  <span>Accuracy: {agent.accuracy}%</span>
                </div>
                
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => {
                      e.stopPropagation();
                      onConfigureAgent(agent);
                    }}
                  >
                    <Settings2 className="w-4 h-4" />
                  </Button>
                  {agent.status === 'working' ? (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        onPauseAgent(agent.id);
                      }}
                    >
                      <Pause className="w-4 h-4" />
                    </Button>
                  ) : agent.status === 'paused' ? (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        onResumeAgent(agent.id);
                      }}
                    >
                      <Play className="w-4 h-4" />
                    </Button>
                  ) : null}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-destructive"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemoveAgent(agent.id);
                    }}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
});