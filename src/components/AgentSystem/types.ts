export interface AgentType {
  id: string;
  name: string;
  description: string;
  category: string;
  capabilities: string[];
  limitations: string[];
  cost: number;
  speed: 'fast' | 'medium' | 'slow';
  accuracy: 'high' | 'medium' | 'low';
}

export interface Agent {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'working' | 'error' | 'paused';
  currentTask?: string;
  tasksCompleted: number;
  accuracy: number;
  lastActive: Date;
  specialization?: string;
  memory: AgentMemory;
  settings: AgentSettings;
}

export interface AgentMemory {
  shortTerm: string[];
  longTerm: Record<string, any>;
  workingMemory: Record<string, any>;
}

export interface AgentSettings {
  autonomy: 'full' | 'guided' | 'manual';
  creativity: number;
  verbosity: number;
  maxConcurrentTasks: number;
  preferredTools: string[];
}

export interface AgentTask {
  id: string;
  agentId: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  progress: number;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  result?: string;
  error?: string;
  dependencies: string[];
  subtasks: AgentTask[];
}

export interface AgentConversation {
  id: string;
  agentId: string;
  messages: ConversationMessage[];
  context: Record<string, any>;
  startedAt: Date;
  lastMessageAt: Date;
}

export interface ConversationMessage {
  id: string;
  role: 'user' | 'agent' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AgentMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageCompletionTime: number;
  accuracy: number;
  efficiency: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    tokens: number;
  };
  performance: {
    hourly: number[];
    daily: number[];
    weekly: number[];
  };
}