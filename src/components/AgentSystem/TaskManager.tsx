import { memo, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { AgentTask, Agent } from './types';
import { Plus, Clock, CheckCircle, XCircle, AlertCircle, ChevronRight, ChevronDown } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface TaskManagerProps {
  tasks: AgentTask[];
  agents: Agent[];
  onCreateTask: (task: Partial<AgentTask>) => void;
  onUpdateTaskStatus?: (taskId: string, status: AgentTask['status']) => void;
  onAssignTask?: (taskId: string, agentId: string) => void;
}

export const TaskManager = memo(function TaskManager({
  tasks,
  agents,
  onCreateTask,
  onUpdateTaskStatus: _onUpdateTaskStatus,
  onAssignTask: _onAssignTask
}: TaskManagerProps) {
  const [showNewTask, setShowNewTask] = useState(false);
  const [newTask, setNewTask] = useState<Partial<AgentTask>>({
    title: '',
    description: '',
    priority: 'medium',
    agentId: ''
  });
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  const handleCreateTask = () => {
    if (newTask.title && newTask.description && newTask.agentId) {
      onCreateTask(newTask);
      setNewTask({
        title: '',
        description: '',
        priority: 'medium',
        agentId: ''
      });
      setShowNewTask(false);
    }
  };

  const toggleTaskExpansion = (taskId: string) => {
    const newExpanded = new Set(expandedTasks);
    if (newExpanded.has(taskId)) {
      newExpanded.delete(taskId);
    } else {
      newExpanded.add(taskId);
    }
    setExpandedTasks(newExpanded);
  };

  const getStatusIcon = (status: AgentTask['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'in-progress':
        return <AlertCircle className="w-4 h-4 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'failed':
        return <XCircle className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority: AgentTask['priority']) => {
    switch (priority) {
      case 'critical':
        return 'destructive';
      case 'high':
        return 'default';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
    }
  };

  const TaskItem = ({ task, level = 0 }: { task: AgentTask; level?: number }) => {
    const agent = agents.find(a => a.id === task.agentId);
    const isExpanded = expandedTasks.has(task.id);
    const hasSubtasks = task.subtasks && task.subtasks.length > 0;

    return (
      <div className={`${level > 0 ? 'ml-6' : ''}`}>
        <div className="p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="flex items-start gap-3">
            {hasSubtasks && (
              <button
                onClick={() => toggleTaskExpansion(task.id)}
                className="mt-0.5"
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </button>
            )}
            
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getStatusIcon(task.status)}
                  <h4 className="font-medium text-sm">{task.title}</h4>
                  <Badge variant={getPriorityColor(task.priority)} className="text-xs">
                    {task.priority}
                  </Badge>
                </div>
                
                {agent && (
                  <Badge variant="outline" className="text-xs">
                    {agent.name}
                  </Badge>
                )}
              </div>
              
              <p className="text-xs text-muted-foreground mt-1">{task.description}</p>
              
              {task.status === 'in-progress' && (
                <div className="mt-2">
                  <Progress value={task.progress} className="h-1" />
                  <p className="text-xs text-muted-foreground mt-1">
                    {task.progress}% complete
                  </p>
                </div>
              )}
              
              <div className="flex items-center gap-4 mt-2">
                <p className="text-xs text-muted-foreground">
                  Created: {new Date(task.createdAt).toLocaleDateString()}
                </p>
                {task.completedAt && (
                  <p className="text-xs text-muted-foreground">
                    Completed: {new Date(task.completedAt).toLocaleDateString()}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {hasSubtasks && isExpanded && (
          <div className="mt-2 space-y-2">
            {task.subtasks.map(subtask => (
              <TaskItem key={subtask.id} task={subtask} level={level + 1} />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="flex-1 flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Task Manager</CardTitle>
          <Button
            size="sm"
            onClick={() => setShowNewTask(!showNewTask)}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Task
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col">
        <AnimatePresence>
          {showNewTask && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-4 p-4 border rounded-lg bg-muted/50"
            >
              <div className="space-y-3">
                <div>
                  <Label htmlFor="task-title">Title</Label>
                  <Input
                    id="task-title"
                    value={newTask.title}
                    onChange={(e) => setNewTask({ ...newTask, title: e.target.value })}
                    placeholder="Enter task title"
                  />
                </div>
                
                <div>
                  <Label htmlFor="task-description">Description</Label>
                  <Textarea
                    id="task-description"
                    value={newTask.description}
                    onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                    placeholder="Describe the task"
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="task-agent">Assign to Agent</Label>
                    <Select
                      value={newTask.agentId}
                      onValueChange={(value) => setNewTask({ ...newTask, agentId: value })}
                    >
                      <SelectTrigger id="task-agent">
                        <SelectValue placeholder="Select agent" />
                      </SelectTrigger>
                      <SelectContent>
                        {agents.map(agent => (
                          <SelectItem key={agent.id} value={agent.id}>
                            {agent.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="task-priority">Priority</Label>
                    <Select
                      value={newTask.priority}
                      onValueChange={(value) => setNewTask({ ...newTask, priority: value as AgentTask['priority'] })}
                    >
                      <SelectTrigger id="task-priority">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowNewTask(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleCreateTask}
                  >
                    Create Task
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <ScrollArea className="flex-1">
          <div className="space-y-2">
            {tasks.filter(task => !task.dependencies || task.dependencies.length === 0).map(task => (
              <TaskItem key={task.id} task={task} />
            ))}
          </div>
        </ScrollArea>
        
        <div className="mt-4 pt-4 border-t">
          <div className="grid grid-cols-4 gap-2 text-center">
            <div>
              <p className="text-2xl font-bold">{tasks.filter(t => t.status === 'pending').length}</p>
              <p className="text-xs text-muted-foreground">Pending</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{tasks.filter(t => t.status === 'in-progress').length}</p>
              <p className="text-xs text-muted-foreground">In Progress</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{tasks.filter(t => t.status === 'completed').length}</p>
              <p className="text-xs text-muted-foreground">Completed</p>
            </div>
            <div>
              <p className="text-2xl font-bold">{tasks.filter(t => t.status === 'failed').length}</p>
              <p className="text-xs text-muted-foreground">Failed</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});