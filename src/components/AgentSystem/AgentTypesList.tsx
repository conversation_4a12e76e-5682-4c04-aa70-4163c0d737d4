import { memo } from 'react';
import { Badge } from '@/components/ui/badge';
import { AgentType } from './types';
import {
  Code,
  Shield,
  Cloud,
  Database,
  Palette,
  Brain,
  MessageSquare,
  Zap,
  TestTube,
  FileCode,
  Briefcase,
  Globe,
  Lock,
  Users,
  BookOpen,
  GitBranch,
  Eye,
  Layers,
  Package,
  DollarSign,
  Cpu,
  Search,
  Bot,
  Cog,
  Terminal
} from 'lucide-react';

interface AgentTypesListProps {
  agentTypes: AgentType[];
  selectedType: string | null;
  onSelectType: (type: AgentType) => void;
}

export const AgentTypesList = memo(function AgentTypesList({
  agentTypes,
  selectedType,
  onSelectType
}: AgentTypesListProps) {
  const getAgentIcon = (type: string) => {
    const icons: Record<string, any> = {
      code: Code,
      security: Shield,
      cloud: Cloud,
      database: Database,
      ui: Palette,
      ai: Brain,
      communication: MessageSquare,
      performance: Zap,
      testing: TestTube,
      documentation: FileCode,
      project: Briefcase,
      api: Globe,
      authentication: Lock,
      collaboration: Users,
      learning: BookOpen,
      versioning: GitBranch,
      monitoring: Eye,
      architecture: Layers,
      deployment: Package,
      optimization: DollarSign,
      system: Cpu,
      search: Search,
      assistant: Bo<PERSON>,
      configuration: Cog,
      terminal: Terminal
    };
    
    const Icon = icons[type] || Code;
    return <Icon className="w-4 h-4" />;
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
      {agentTypes.map((type) => (
        <button
          key={type.id}
          onClick={() => onSelectType(type)}
          className={`text-left p-4 rounded-lg border transition-all ${
            selectedType === type.id
              ? 'border-primary bg-primary/5'
              : 'border-border hover:border-primary/50 hover:bg-accent/50'
          }`}
        >
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-md ${
              selectedType === type.id ? 'bg-primary/10' : 'bg-muted'
            }`}>
              {getAgentIcon(type.category)}
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-sm">{type.name}</h4>
              <p className="text-xs text-muted-foreground mt-1">{type.description}</p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary" className="text-xs">
                  {type.category}
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {type.cost} tokens/task
                </Badge>
              </div>
            </div>
          </div>
        </button>
      ))}
    </div>
  );
});