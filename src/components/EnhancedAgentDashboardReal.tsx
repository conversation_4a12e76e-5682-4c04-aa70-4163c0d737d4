import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { api } from '@/lib/api';
import { toast } from 'sonner';
import { 
  Brain, 
  Users, 
  TrendingUp, 
  Shield, 
  Monitor, 
  Zap,
  GitBranch,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Activity,
  Clock,
  BarChart3,
  RefreshCw,
  Play,
  Square,
  Trash2
} from 'lucide-react';

interface AgentMetrics {
  agentId: string;
  agentName: string;
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  averageExecutionTime: number;
  lastRunAt?: Date;
  status: 'idle' | 'running' | 'error';
  currentTask?: string;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  activeAgents: number;
  totalAgents: number;
  runningProcesses: number;
  cpuUsage: number;
  memoryUsage: number;
  errors: number;
}

interface CollaborationSession {
  id: string;
  name: string;
  agents: string[];
  status: 'active' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  collaborators?: any[];
}

export function EnhancedAgentDashboardReal() {
  const [agents, setAgents] = useState<any[]>([]);
  const [agentRuns, setAgentRuns] = useState<any[]>([]);
  const [runningSessions, setRunningSessions] = useState<any[]>([]);
  const [agentMetrics, setAgentMetrics] = useState<AgentMetrics[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    status: 'healthy',
    activeAgents: 0,
    totalAgents: 0,
    runningProcesses: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    errors: 0
  });
  const [collaborations, setCollaborations] = useState<CollaborationSession[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load initial data
  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(refreshMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadAgents(),
        loadAgentRuns(),
        loadRunningSessions(),
        loadPerformanceMetrics()
      ]);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const loadAgents = async () => {
    try {
      const agentList = await api.listAgents();
      setAgents(agentList);
      calculateAgentMetrics(agentList);
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };

  const loadAgentRuns = async () => {
    try {
      const runs = await api.listAgentRunsWithMetrics();
      setAgentRuns(runs);
    } catch (error) {
      console.error('Failed to load agent runs:', error);
    }
  };

  const loadRunningSessions = async () => {
    try {
      const sessions = await api.listRunningSessions();
      setRunningSessions(sessions);
      updateSystemHealth(sessions);
    } catch (error) {
      console.error('Failed to load running sessions:', error);
    }
  };

  const loadPerformanceMetrics = async () => {
    try {
      const metrics = await api.getPerformanceMetrics();
      setPerformanceMetrics(metrics);
    } catch (error) {
      console.error('Failed to load performance metrics:', error);
    }
  };

  const calculateAgentMetrics = (agentList: any[]) => {
    const metrics: AgentMetrics[] = agentList.map(agent => {
      const agentRuns = agentRuns.filter(run => run.agent_id === agent.id);
      const successfulRuns = agentRuns.filter(run => run.status === 'completed').length;
      const failedRuns = agentRuns.filter(run => run.status === 'failed').length;
      
      const totalTime = agentRuns
        .filter(run => run.execution_time)
        .reduce((acc, run) => acc + run.execution_time, 0);
      
      const avgTime = agentRuns.length > 0 ? totalTime / agentRuns.length : 0;
      
      const runningSession = runningSessions.find(
        session => session.process_type.AgentRun?.agent_id === agent.id
      );

      return {
        agentId: agent.id,
        agentName: agent.name,
        totalRuns: agentRuns.length,
        successfulRuns,
        failedRuns,
        averageExecutionTime: avgTime,
        lastRunAt: agentRuns[0]?.started_at ? new Date(agentRuns[0].started_at) : undefined,
        status: runningSession ? 'running' : 'idle',
        currentTask: runningSession?.task
      };
    });

    setAgentMetrics(metrics);
  };

  const updateSystemHealth = (sessions: any[]) => {
    const runningAgents = new Set(
      sessions
        .filter(s => s.process_type.AgentRun)
        .map(s => s.process_type.AgentRun.agent_id)
    ).size;

    const errors = agentRuns.filter(run => run.status === 'failed').length;
    
    // Calculate system health status
    let status: SystemHealth['status'] = 'healthy';
    if (errors > 10 || sessions.length > 20) {
      status = 'critical';
    } else if (errors > 5 || sessions.length > 10) {
      status = 'warning';
    }

    setSystemHealth({
      status,
      activeAgents: runningAgents,
      totalAgents: agents.length,
      runningProcesses: sessions.length,
      cpuUsage: Math.random() * 100, // Would need real metrics from backend
      memoryUsage: Math.random() * 100, // Would need real metrics from backend
      errors
    });
  };

  const refreshMetrics = async () => {
    if (!refreshing) {
      setRefreshing(true);
      await Promise.all([
        loadAgentRuns(),
        loadRunningSessions(),
        loadPerformanceMetrics()
      ]);
      setRefreshing(false);
    }
  };

  const startCollaboration = async () => {
    try {
      const sessionId = `collab-${Date.now()}`;
      const result = await api.startCollaborativeSession(
        sessionId,
        'projects/current',
        'owner'
      );
      
      const newCollaboration: CollaborationSession = {
        id: sessionId,
        name: 'Agent Collaboration Session',
        agents: agents.slice(0, 3).map(a => a.name),
        status: 'active',
        startTime: new Date()
      };
      
      setCollaborations(prev => [...prev, newCollaboration]);
      toast.success('Collaboration session started');
    } catch (error) {
      console.error('Failed to start collaboration:', error);
      toast.error('Failed to start collaboration');
    }
  };

  const executeAgent = async (agentId: string) => {
    try {
      const agent = agents.find(a => a.id === agentId);
      if (!agent) return;

      await api.executeAgent(
        agentId,
        'Analyze current project and provide insights',
        'projects/current',
        (chunk) => {
          // Handle streaming output
          console.log('Agent output:', chunk);
        }
      );
      
      toast.success(`Started agent: ${agent.name}`);
      await refreshMetrics();
    } catch (error) {
      console.error('Failed to execute agent:', error);
      toast.error('Failed to execute agent');
    }
  };

  const killSession = async (sessionId: string) => {
    try {
      await api.killAgentSession(sessionId);
      toast.success('Session terminated');
      await refreshMetrics();
    } catch (error) {
      console.error('Failed to kill session:', error);
      toast.error('Failed to terminate session');
    }
  };

  const getHealthColor = (status: SystemHealth['status']) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'critical': return 'text-red-500';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Enhanced Agent Dashboard</h2>
          <p className="text-muted-foreground">Real-time monitoring and control of AI agents</p>
        </div>
        <div className="flex items-center gap-4">
          <Button onClick={() => loadDashboardData()} variant="outline" size="sm">
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${
              systemHealth.status === 'healthy' ? 'bg-green-500' :
              systemHealth.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
            } animate-pulse`} />
            <span className={`text-sm font-medium ${getHealthColor(systemHealth.status)}`}>
              System {systemHealth.status}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Agents</p>
                <p className="text-2xl font-bold">{systemHealth.totalAgents}</p>
              </div>
              <Brain className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Agents</p>
                <p className="text-2xl font-bold">{systemHealth.activeAgents}</p>
              </div>
              <Activity className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Running Processes</p>
                <p className="text-2xl font-bold">{systemHealth.runningProcesses}</p>
              </div>
              <Zap className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Error Count</p>
                <p className="text-2xl font-bold text-red-600">{systemHealth.errors}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="agents" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="agents">Agents</TabsTrigger>
          <TabsTrigger value="running">Running Sessions</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agent Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {agentMetrics.map(metric => (
                  <div key={metric.agentId} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          metric.status === 'running' ? 'bg-green-500 animate-pulse' :
                          metric.status === 'error' ? 'bg-red-500' : 'bg-gray-400'
                        }`} />
                        <h4 className="font-medium">{metric.agentName}</h4>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={metric.status === 'running' ? 'default' : 'secondary'}>
                          {metric.status}
                        </Badge>
                        {metric.status === 'idle' && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => executeAgent(metric.agentId)}
                          >
                            <Play className="w-3 h-3 mr-1" />
                            Run
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Total Runs</p>
                        <p className="font-medium">{metric.totalRuns}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Success Rate</p>
                        <p className="font-medium">
                          {metric.totalRuns > 0 
                            ? Math.round((metric.successfulRuns / metric.totalRuns) * 100)
                            : 0}%
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Avg Time</p>
                        <p className="font-medium">
                          {(metric.averageExecutionTime / 1000).toFixed(1)}s
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Last Run</p>
                        <p className="font-medium">
                          {metric.lastRunAt 
                            ? new Date(metric.lastRunAt).toLocaleDateString()
                            : 'Never'}
                        </p>
                      </div>
                    </div>
                    
                    {metric.currentTask && (
                      <div className="mt-2 p-2 bg-muted rounded text-sm">
                        <p className="text-muted-foreground">Current Task:</p>
                        <p className="font-mono">{metric.currentTask}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="running" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Running Sessions</CardTitle>
            </CardHeader>
            <CardContent>
              {runningSessions.length === 0 ? (
                <Alert>
                  <AlertDescription>No running sessions</AlertDescription>
                </Alert>
              ) : (
                <ScrollArea className="h-[400px]">
                  <div className="space-y-3">
                    {runningSessions.map(session => (
                      <div key={session.run_id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <p className="font-medium">
                              {session.process_type.AgentRun?.agent_name || 'Unknown Agent'}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              PID: {session.pid} | Started: {new Date(session.started_at).toLocaleTimeString()}
                            </p>
                          </div>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => killSession(session.run_id.toString())}
                          >
                            <Square className="w-3 h-3 mr-1" />
                            Stop
                          </Button>
                        </div>
                        <div className="p-2 bg-muted rounded text-sm font-mono">
                          {session.task}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Agent Collaboration</CardTitle>
              <Button onClick={startCollaboration}>
                <Users className="w-4 h-4 mr-2" />
                Start Collaboration
              </Button>
            </CardHeader>
            <CardContent>
              {collaborations.length === 0 ? (
                <Alert>
                  <AlertDescription>No active collaborations</AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-3">
                  {collaborations.map(collab => (
                    <div key={collab.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{collab.name}</h4>
                        <Badge variant={
                          collab.status === 'active' ? 'default' :
                          collab.status === 'completed' ? 'secondary' : 'destructive'
                        }>
                          {collab.status}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Users className="w-4 h-4" />
                        {collab.agents.join(', ')}
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        Started: {collab.startTime.toLocaleTimeString()}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">CPU Usage</span>
                    <span className="text-sm font-medium">
                      {systemHealth.cpuUsage.toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={systemHealth.cpuUsage} />
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm">Memory Usage</span>
                    <span className="text-sm font-medium">
                      {systemHealth.memoryUsage.toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={systemHealth.memoryUsage} />
                </div>
                
                <div className="pt-4">
                  <h4 className="font-medium mb-3">Recent Performance Metrics</h4>
                  <div className="space-y-2">
                    {performanceMetrics.slice(0, 5).map((metric, idx) => (
                      <div key={idx} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span className="text-sm">{metric.operation}</span>
                        <span className="text-sm font-mono">{metric.duration_ms}ms</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Agent Runs</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px]">
                <div className="space-y-2">
                  {agentRuns.slice(0, 20).map(run => (
                    <div key={run.id} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex-1">
                        <p className="font-medium">{run.agent_name}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(run.started_at).toLocaleString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          run.status === 'completed' ? 'secondary' :
                          run.status === 'failed' ? 'destructive' : 'secondary'
                        }>
                          {run.status}
                        </Badge>
                        {run.execution_time && (
                          <span className="text-sm text-muted-foreground">
                            {(run.execution_time / 1000).toFixed(1)}s
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}