import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { MCPAIAssistant } from './MCPAIAssistant';
import { MCPIntelligentAutomation } from './MCPIntelligentAutomation';
import { MCPAdvancedAnalytics } from './MCPAdvancedAnalytics';
import { MCPServerMonitor } from './MCPServerMonitor';

export function MCPSmartDashboard() {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="ai-assistant" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="ai-assistant">AI Assistant</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="ai-assistant">
          <MCPAIAssistant />
        </TabsContent>

        <TabsContent value="automation">
          <MCPIntelligentAutomation />
        </TabsContent>

        <TabsContent value="analytics">
          <MCPAdvancedAnalytics />
        </TabsContent>

        <TabsContent value="monitoring">
          <MCPServerMonitor />
        </TabsContent>
      </Tabs>
    </div>
  );
}