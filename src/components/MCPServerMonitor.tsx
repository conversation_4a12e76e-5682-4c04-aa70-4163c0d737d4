import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import {
  Activity,
  Server,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Database,
  Network,
  Cpu,
  Memory,
  HardDrive
} from 'lucide-react';

interface ServerMetrics {
  id: string;
  name: string;
  status: 'healthy' | 'warning' | 'critical' | 'offline';
  uptime: number;
  requests: {
    total: number;
    successful: number;
    failed: number;
    avgResponseTime: number;
  };
  resources: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  errors: Array<{
    timestamp: string;
    message: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

export function MCPServerMonitor() {
  const [servers, setServers] = useState<ServerMetrics[]>([
    {
      id: 'fs-1',
      name: 'Filesystem Server',
      status: 'healthy',
      uptime: 86400,
      requests: {
        total: 1247,
        successful: 1244,
        failed: 3,
        avgResponseTime: 45
      },
      resources: {
        cpu: 12,
        memory: 128,
        disk: 45,
        network: 23
      },
      errors: [
        {
          timestamp: '2024-01-15T10:30:00Z',
          message: 'File permission denied: /restricted/file.txt',
          severity: 'medium'
        }
      ]
    },
    {
      id: 'git-1',
      name: 'Git Repository Server',
      status: 'warning',
      uptime: 72000,
      requests: {
        total: 892,
        successful: 885,
        failed: 7,
        avgResponseTime: 120
      },
      resources: {
        cpu: 25,
        memory: 256,
        disk: 67,
        network: 45
      },
      errors: [
        {
          timestamp: '2024-01-15T11:15:00Z',
          message: 'Git push failed: remote rejected',
          severity: 'high'
        },
        {
          timestamp: '2024-01-15T11:10:00Z',
          message: 'Connection timeout to remote repository',
          severity: 'medium'
        }
      ]
    }
  ]);

  const [selectedServer, setSelectedServer] = useState<ServerMetrics | null>(null);

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setServers(prev => prev.map(server => ({
        ...server,
        requests: {
          ...server.requests,
          total: server.requests.total + Math.floor(Math.random() * 5),
          avgResponseTime: Math.max(20, server.requests.avgResponseTime + (Math.random() - 0.5) * 10)
        },
        resources: {
          cpu: Math.max(0, Math.min(100, server.resources.cpu + (Math.random() - 0.5) * 10)),
          memory: Math.max(0, Math.min(100, server.resources.memory + (Math.random() - 0.5) * 5)),
          disk: server.resources.disk,
          network: Math.max(0, Math.min(100, server.resources.network + (Math.random() - 0.5) * 15))
        }
      })));
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: ServerMetrics['status']) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'critical': return 'text-red-500';
      case 'offline': return 'text-gray-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: ServerMetrics['status']) => {
    switch (status) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'critical': return AlertTriangle;
      case 'offline': return Clock;
      default: return Clock;
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}d ${hours}h ${minutes}m`;
  };

  const getSuccessRate = (server: ServerMetrics) => {
    return ((server.requests.successful / server.requests.total) * 100).toFixed(1);
  };

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Server className="w-5 h-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{servers.length}</div>
                <div className="text-sm text-muted-foreground">Total Servers</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">
                  {servers.filter(s => s.status === 'healthy').length}
                </div>
                <div className="text-sm text-muted-foreground">Healthy</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold">
                  {servers.reduce((sum, s) => sum + s.requests.total, 0)}
                </div>
                <div className="text-sm text-muted-foreground">Total Requests</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="w-5 h-5 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">
                  {Math.round(
                    servers.reduce((sum, s) => sum + s.requests.avgResponseTime, 0) / servers.length
                  )}ms
                </div>
                <div className="text-sm text-muted-foreground">Avg Response</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Server List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Server Status</h3>
          <div className="space-y-4">
            {servers.map((server) => {
              const StatusIcon = getStatusIcon(server.status);
              
              return (
                <Card 
                  key={server.id}
                  className={`cursor-pointer transition-colors ${
                    selectedServer?.id === server.id ? 'border-primary' : ''
                  }`}
                  onClick={() => setSelectedServer(server)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Server className="w-5 h-5" />
                        <div>
                          <h4 className="font-medium">{server.name}</h4>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <StatusIcon className={`w-4 h-4 ${getStatusColor(server.status)}`} />
                            <span>{server.status}</span>
                            <span>• {formatUptime(server.uptime)}</span>
                          </div>
                        </div>
                      </div>
                      <Badge variant={server.status === 'healthy' ? 'default' : 'destructive'}>
                        {getSuccessRate(server)}% success
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Requests:</span>
                        <div className="font-medium">{server.requests.total}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Avg Response:</span>
                        <div className="font-medium">{Math.round(server.requests.avgResponseTime)}ms</div>
                      </div>
                    </div>

                    <div className="mt-3 space-y-2">
                      <div>
                        <div className="flex justify-between text-xs mb-1">
                          <span>CPU</span>
                          <span>{server.resources.cpu}%</span>
                        </div>
                        <Progress value={server.resources.cpu} className="h-1" />
                      </div>
                      <div>
                        <div className="flex justify-between text-xs mb-1">
                          <span>Memory</span>
                          <span>{server.resources.memory}MB</span>
                        </div>
                        <Progress value={(server.resources.memory / 512) * 100} className="h-1" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Server Details */}
        <div>
          {selectedServer ? (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Server Details</h3>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {selectedServer.requests.successful}
                      </div>
                      <div className="text-xs text-muted-foreground">Successful</div>
                    </div>
                    <div className="text-center p-3 bg-muted rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {selectedServer.requests.failed}
                      </div>
                      <div className="text-xs text-muted-foreground">Failed</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Cpu className="w-4 h-4" />
                      <div className="flex-1">
                        <div className="flex justify-between text-sm mb-1">
                          <span>CPU Usage</span>
                          <span>{selectedServer.resources.cpu}%</span>
                        </div>
                        <Progress value={selectedServer.resources.cpu} className="h-2" />
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Memory className="w-4 h-4" />
                      <div className="flex-1">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Memory</span>
                          <span>{selectedServer.resources.memory}MB</span>
                        </div>
                        <Progress value={(selectedServer.resources.memory / 512) * 100} className="h-2" />
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Network className="w-4 h-4" />
                      <div className="flex-1">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Network</span>
                          <span>{selectedServer.resources.network}%</span>
                        </div>
                        <Progress value={selectedServer.resources.network} className="h-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {selectedServer.errors.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                      Recent Errors
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {selectedServer.errors.slice(0, 3).map((error, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-1">
                            <Badge variant={error.severity === 'high' ? 'destructive' : 'secondary'}>
                              {error.severity}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(error.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <p className="text-sm">{error.message}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Activity className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">
                  Select a server to view detailed metrics and performance data
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}