/**
 * Parallel Agents System Integration
 * 
 * Comprehensive integration component that initializes and connects all parallel agents systems:
 * - Advanced Parallel Orchestrator
 * - Intelligent Task Distributor
 * - Real-time Analytics
 * - Performance Optimizer
 * - Workflow Automation Engine
 * - Monitoring and Alerting System
 * - Enterprise Dashboard
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  AdvancedParallelOrchestrator
} from '../../lib/parallel-agents/AdvancedParallelOrchestrator';
import {
  IntelligentTaskDistributor
} from '../../lib/parallel-agents/IntelligentTaskDistributor';
import {
  RealTimeAnalytics
} from '../../lib/parallel-agents/RealTimeAnalytics';
import {
  defaultOrchestrationConfig,
  defaultDistributionConfig,
  defaultAnalyticsConfig
} from '../../lib/parallel-agents/types';
import {
  PerformanceOptimizer,
  defaultOptimizationConfig
} from '../../lib/parallel-agents/PerformanceOptimizer';
import {
  WorkflowAutomationEngine
} from '../../lib/parallel-agents/WorkflowAutomation';
import {
  MonitoringAndAlertingSystem,
  defaultMonitoringConfig
} from '../../lib/parallel-agents/MonitoringAndAlerting';
import { ParallelAgentsManager } from '../../lib/ai-agents/ParallelAgentsManager';
import { EnhancedParallelSystem } from '../../lib/ai-agents/EnhancedParallelSystem';
import { ParallelExecutionEngine } from '../../lib/ai-agents/ParallelExecutionEngine';
import { BulkOperationsManager } from '../../lib/ai-agents/BulkOperations';
import { TaskMaster } from '../../lib/ai-agents/TaskMaster';
import EnterpriseParallelAgentsDashboard from './EnterpriseParallelAgentsDashboard';

// Integration status types
type SystemStatus = 'initializing' | 'starting' | 'running' | 'stopping' | 'stopped' | 'error';

interface SystemComponent {
  name: string;
  status: SystemStatus;
  instance?: any;
  error?: string;
  startTime?: Date;
}

interface IntegrationConfig {
  autoStart: boolean;
  enableMonitoring: boolean;
  enableOptimization: boolean;
  enableWorkflows: boolean;
  enableAnalytics: boolean;
  debugMode: boolean;
}

interface ParallelAgentsSystemIntegrationProps {
  config?: Partial<IntegrationConfig>;
  onSystemReady?: () => void;
  onSystemError?: (error: Error) => void;
}

const defaultIntegrationConfig: IntegrationConfig = {
  autoStart: true,
  enableMonitoring: true,
  enableOptimization: true,
  enableWorkflows: true,
  enableAnalytics: true,
  debugMode: false
};

const ParallelAgentsSystemIntegration: React.FC<ParallelAgentsSystemIntegrationProps> = ({
  config = {},
  onSystemReady,
  onSystemError
}) => {
  const finalConfig = { ...defaultIntegrationConfig, ...config };
  
  // System state
  const [systemStatus, setSystemStatus] = useState<SystemStatus>('initializing');
  const [components, setComponents] = useState<Record<string, SystemComponent>>({});
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [initializationProgress, setInitializationProgress] = useState(0);
  
  // System instances
  const systemInstances = useRef<{
    parallelAgentsManager?: ParallelAgentsManager;
    enhancedParallelSystem?: EnhancedParallelSystem;
    parallelExecutionEngine?: ParallelExecutionEngine;
    bulkOperationsManager?: BulkOperationsManager;
    taskMaster?: TaskMaster;
    orchestrator?: AdvancedParallelOrchestrator;
    distributor?: IntelligentTaskDistributor;
    analytics?: RealTimeAnalytics;
    optimizer?: PerformanceOptimizer;
    workflowEngine?: WorkflowAutomationEngine;
    monitoring?: MonitoringAndAlertingSystem;
  }>({});
  
  // Update component status
  const updateComponentStatus = useCallback((name: string, status: SystemStatus, error?: string) => {
    setComponents(prev => ({
      ...prev,
      [name]: {
        ...prev[name],
        name,
        status,
        error,
        startTime: status === 'running' ? new Date() : prev[name]?.startTime
      }
    }));
    
    if (finalConfig.debugMode) {
      console.log(`🔧 Component ${name}: ${status}${error ? ` (${error})` : ''}`);
    }
  }, [finalConfig.debugMode]);
  
  // Initialize core systems
  const initializeCoreSystem = useCallback(async () => {
    try {
      updateComponentStatus('core', 'starting');
      
      // Initialize Parallel Execution Engine
      const parallelExecutionEngine = new ParallelExecutionEngine();
      systemInstances.current.parallelExecutionEngine = parallelExecutionEngine;
      updateComponentStatus('execution-engine', 'running');
      
      // Initialize Enhanced Parallel System
      const enhancedParallelSystem = new EnhancedParallelSystem();
      systemInstances.current.enhancedParallelSystem = enhancedParallelSystem;
      updateComponentStatus('enhanced-system', 'running');
      
      // Initialize Parallel Agents Manager
      const parallelAgentsManager = new ParallelAgentsManager();
      systemInstances.current.parallelAgentsManager = parallelAgentsManager;
      updateComponentStatus('agents-manager', 'running');
      
      // Initialize Bulk Operations Manager
      const bulkOperationsManager = new BulkOperationsManager();
      systemInstances.current.bulkOperationsManager = bulkOperationsManager;
      updateComponentStatus('bulk-operations', 'running');
      
      // Initialize Task Master
      const taskMaster = new TaskMaster();
      systemInstances.current.taskMaster = taskMaster;
      updateComponentStatus('task-master', 'running');
      
      updateComponentStatus('core', 'running');
      setInitializationProgress(20);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('core', 'error', errorMessage);
      throw error;
    }
  }, [updateComponentStatus]);
  
  // Initialize orchestrator
  const initializeOrchestrator = useCallback(async () => {
    try {
      updateComponentStatus('orchestrator', 'starting');
      
      const orchestrator = new AdvancedParallelOrchestrator(
        defaultOrchestrationConfig
      );
      
      await orchestrator.start();
      systemInstances.current.orchestrator = orchestrator;
      updateComponentStatus('orchestrator', 'running');
      setInitializationProgress(35);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('orchestrator', 'error', errorMessage);
      throw error;
    }
  }, [updateComponentStatus]);
  
  // Initialize task distributor
  const initializeDistributor = useCallback(async () => {
    try {
      updateComponentStatus('distributor', 'starting');
      
      const distributor = new IntelligentTaskDistributor(defaultDistributionConfig);
      await distributor.start();
      systemInstances.current.distributor = distributor;
      updateComponentStatus('distributor', 'running');
      setInitializationProgress(50);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('distributor', 'error', errorMessage);
      throw error;
    }
  }, [updateComponentStatus]);
  
  // Initialize analytics
  const initializeAnalytics = useCallback(async () => {
    if (!finalConfig.enableAnalytics) {
      setInitializationProgress(65);
      return;
    }
    
    try {
      updateComponentStatus('analytics', 'starting');
      
      const analytics = new RealTimeAnalytics(
        { ...defaultAnalyticsConfig }
      );
      
      await analytics.start();
      systemInstances.current.analytics = analytics;
      updateComponentStatus('analytics', 'running');
      setInitializationProgress(65);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('analytics', 'error', errorMessage);
      throw error;
    }
  }, [finalConfig.enableAnalytics, updateComponentStatus]);
  
  // Initialize optimizer
  const initializeOptimizer = useCallback(async () => {
    if (!finalConfig.enableOptimization) {
      setInitializationProgress(80);
      return;
    }
    
    try {
      updateComponentStatus('optimizer', 'starting');
      
      const optimizer = new PerformanceOptimizer(
        defaultOptimizationConfig,
        systemInstances.current.agentsManager!,
        systemInstances.current.parallelSystem!,
        systemInstances.current.analytics!,
        systemInstances.current.distributor!
      );
      
      await optimizer.start();
      systemInstances.current.optimizer = optimizer;
      updateComponentStatus('optimizer', 'running');
      setInitializationProgress(80);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('optimizer', 'error', errorMessage);
      throw error;
    }
  }, [finalConfig.enableOptimization, updateComponentStatus]);
  
  // Initialize workflow engine
  const initializeWorkflowEngine = useCallback(async () => {
    if (!finalConfig.enableWorkflows) {
      setInitializationProgress(90);
      return;
    }
    
    try {
      updateComponentStatus('workflow-engine', 'starting');
      
      const workflowEngine = new WorkflowAutomationEngine(
        systemInstances.current.orchestrator!,
        systemInstances.current.distributor!,
        systemInstances.current.analytics!,
        systemInstances.current.optimizer!,
        systemInstances.current.bulkOperations!
      );
      
      await workflowEngine.start();
      systemInstances.current.workflowEngine = workflowEngine;
      updateComponentStatus('workflow-engine', 'running');
      setInitializationProgress(90);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('workflow-engine', 'error', errorMessage);
      throw error;
    }
  }, [finalConfig.enableWorkflows, updateComponentStatus]);
  
  // Initialize monitoring
  const initializeMonitoring = useCallback(async () => {
    if (!finalConfig.enableMonitoring) {
      setInitializationProgress(100);
      return;
    }
    
    try {
      updateComponentStatus('monitoring', 'starting');
      
      const monitoring = new MonitoringAndAlertingSystem(
        defaultMonitoringConfig,
        systemInstances.current.orchestrator!,
        systemInstances.current.distributor!,
        systemInstances.current.analytics!,
        systemInstances.current.optimizer!,
        systemInstances.current.workflowEngine!
      );
      
      await monitoring.start();
      systemInstances.current.monitoring = monitoring;
      updateComponentStatus('monitoring', 'running');
      setInitializationProgress(100);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      updateComponentStatus('monitoring', 'error', errorMessage);
      throw error;
    }
  }, [finalConfig.enableMonitoring, updateComponentStatus]);
  
  // Initialize entire system
  const initializeSystem = useCallback(async () => {
    try {
      setSystemStatus('starting');
      setError(null);
      setInitializationProgress(0);
      
      console.log('🚀 Initializing Parallel Agents System...');
      
      // Initialize components in dependency order
      await initializeCoreSystem();
      await initializeOrchestrator();
      await initializeDistributor();
      await initializeAnalytics();
      await initializeOptimizer();
      await initializeWorkflowEngine();
      await initializeMonitoring();
      
      // System is ready
      setSystemStatus('running');
      setIsReady(true);
      setInitializationProgress(100);
      
      console.log('✅ Parallel Agents System initialized successfully');
      
      // Setup system event listeners
      setupSystemEventListeners();
      
      // Notify parent component
      if (onSystemReady) {
        onSystemReady();
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ Failed to initialize Parallel Agents System:', error);
      
      setSystemStatus('error');
      setError(errorMessage);
      
      if (onSystemError) {
        onSystemError(error instanceof Error ? error : new Error(errorMessage));
      }
    }
  }, [
    initializeCoreSystem,
    initializeOrchestrator,
    initializeDistributor,
    initializeAnalytics,
    initializeOptimizer,
    initializeWorkflowEngine,
    initializeMonitoring,
    onSystemReady,
    onSystemError
  ]);
  
  // Setup system event listeners
  const setupSystemEventListeners = useCallback(() => {
    const { orchestrator, analytics, monitoring, workflowEngine } = systemInstances.current;
    
    if (orchestrator) {
      orchestrator.on('error', (error) => {
        console.error('Orchestrator error:', error);
        updateComponentStatus('orchestrator', 'error', error.message);
      });
    }
    
    if (analytics) {
      analytics.on('anomalyDetected', (anomaly) => {
        console.warn('Performance anomaly detected:', anomaly);
      });
    }
    
    if (monitoring) {
      monitoring.on('alertCreated', (alert) => {
        console.warn('System alert:', alert.title);
      });
    }
    
    if (workflowEngine) {
      workflowEngine.on('workflowFailed', (execution) => {
        console.error('Workflow failed:', execution.workflowId);
      });
    }
  }, [updateComponentStatus]);
  
  // Shutdown system
  const shutdownSystem = useCallback(async () => {
    try {
      setSystemStatus('stopping');
      console.log('🛑 Shutting down Parallel Agents System...');
      
      const { monitoring, workflowEngine, optimizer, analytics, distributor, orchestrator } = systemInstances.current;
      
      // Stop components in reverse order
      if (monitoring) {
        await monitoring.stop();
        updateComponentStatus('monitoring', 'stopped');
      }
      
      if (workflowEngine) {
        await workflowEngine.stop();
        updateComponentStatus('workflow-engine', 'stopped');
      }
      
      if (optimizer) {
        await optimizer.stop();
        updateComponentStatus('optimizer', 'stopped');
      }
      
      if (analytics) {
        await analytics.stop();
        updateComponentStatus('analytics', 'stopped');
      }
      
      if (distributor) {
        await distributor.stop();
        updateComponentStatus('distributor', 'stopped');
      }
      
      if (orchestrator) {
        await orchestrator.stop();
        updateComponentStatus('orchestrator', 'stopped');
      }
      
      updateComponentStatus('core', 'stopped');
      
      setSystemStatus('stopped');
      setIsReady(false);
      
      console.log('✅ Parallel Agents System shut down successfully');
      
    } catch (error) {
      console.error('❌ Error during system shutdown:', error);
      setSystemStatus('error');
      setError(error instanceof Error ? error.message : 'Shutdown error');
    }
  }, [updateComponentStatus]);
  
  // Restart system
  const restartSystem = useCallback(async () => {
    await shutdownSystem();
    setTimeout(() => {
      initializeSystem();
    }, 1000);
  }, [shutdownSystem, initializeSystem]);
  
  // Initialize system on mount
  useEffect(() => {
    if (finalConfig.autoStart) {
      initializeSystem();
    }
    
    // Cleanup on unmount
    return () => {
      if (systemStatus === 'running') {
        shutdownSystem();
      }
    };
  }, [finalConfig.autoStart, initializeSystem, shutdownSystem, systemStatus]);
  
  // Get system health
  const getSystemHealth = useCallback(() => {
    const componentStatuses = Object.values(components);
    const runningCount = componentStatuses.filter(c => c.status === 'running').length;
    const errorCount = componentStatuses.filter(c => c.status === 'error').length;
    const totalCount = componentStatuses.length;
    
    if (errorCount > 0) {
      return 'unhealthy';
    } else if (runningCount === totalCount && totalCount > 0) {
      return 'healthy';
    } else {
      return 'degraded';
    }
  }, [components]);
  
  // Render system status
  const renderSystemStatus = () => {
    const health = getSystemHealth();
    const healthColors = {
      healthy: 'text-green-600 bg-green-100',
      degraded: 'text-yellow-600 bg-yellow-100',
      unhealthy: 'text-red-600 bg-red-100'
    };
    
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">System Status</h2>
          <div className="flex items-center space-x-4">
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${healthColors[health]}`}>
              {health.charAt(0).toUpperCase() + health.slice(1)}
            </div>
            <div className="text-sm text-gray-600">
              Status: <span className="font-medium capitalize">{systemStatus}</span>
            </div>
          </div>
        </div>
        
        {systemStatus === 'starting' && (
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>Initialization Progress</span>
              <span>{initializationProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${initializationProgress}%` }}
              />
            </div>
          </div>
        )}
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-red-800 font-medium">System Error</div>
            <div className="text-red-600 text-sm mt-1">{error}</div>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.values(components).map(component => {
            const statusColors = {
              initializing: 'bg-gray-100 text-gray-600',
              starting: 'bg-blue-100 text-blue-600',
              running: 'bg-green-100 text-green-600',
              stopping: 'bg-yellow-100 text-yellow-600',
              stopped: 'bg-gray-100 text-gray-600',
              error: 'bg-red-100 text-red-600'
            };
            
            return (
              <div key={component.name} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-sm">{component.name}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusColors[component.status]}`}>
                    {component.status}
                  </span>
                </div>
                {component.error && (
                  <div className="text-xs text-red-600 mb-2">{component.error}</div>
                )}
                {component.startTime && (
                  <div className="text-xs text-gray-500">
                    Started: {component.startTime.toLocaleTimeString()}
                  </div>
                )}
              </div>
            );
          })}
        </div>
        
        <div className="flex space-x-3 mt-4">
          {!finalConfig.autoStart && systemStatus === 'initializing' && (
            <button
              onClick={initializeSystem}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              🚀 Start System
            </button>
          )}
          
          {systemStatus === 'running' && (
            <>
              <button
                onClick={restartSystem}
                className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 text-sm"
              >
                🔄 Restart System
              </button>
              <button
                onClick={shutdownSystem}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
              >
                🛑 Shutdown System
              </button>
            </>
          )}
          
          {systemStatus === 'stopped' && (
            <button
              onClick={initializeSystem}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
            >
              ▶️ Start System
            </button>
          )}
        </div>
      </div>
    );
  };
  
  return (
    <div className="w-full h-full">
      {/* System Status Panel */}
      {renderSystemStatus()}
      
      {/* Enterprise Dashboard */}
      {isReady && systemInstances.current.orchestrator && (
        <EnterpriseParallelAgentsDashboard
          orchestrator={systemInstances.current.orchestrator}
          distributor={systemInstances.current.distributor!}
          analytics={systemInstances.current.analytics!}
          optimizer={systemInstances.current.optimizer!}
          workflowEngine={systemInstances.current.workflowEngine!}
          monitoring={systemInstances.current.monitoring!}
        />
      )}
      
      {/* Loading State */}
      {!isReady && systemStatus !== 'error' && systemStatus !== 'stopped' && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">
              {systemStatus === 'initializing' ? 'Preparing system...' :
               systemStatus === 'starting' ? 'Starting components...' :
               'Loading...'}
            </p>
          </div>
        </div>
      )}
      
      {/* Error State */}
      {systemStatus === 'error' && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-6xl mb-4">❌</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">System Error</h3>
            <p className="text-gray-600 mb-4">The parallel agents system encountered an error.</p>
            <button
              onClick={initializeSystem}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              🔄 Retry Initialization
            </button>
          </div>
        </div>
      )}
      
      {/* Stopped State */}
      {systemStatus === 'stopped' && (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-6xl mb-4">⏹️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">System Stopped</h3>
            <p className="text-gray-600 mb-4">The parallel agents system is currently stopped.</p>
            <button
              onClick={initializeSystem}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              ▶️ Start System
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ParallelAgentsSystemIntegration;
export type { IntegrationConfig, SystemStatus, SystemComponent };