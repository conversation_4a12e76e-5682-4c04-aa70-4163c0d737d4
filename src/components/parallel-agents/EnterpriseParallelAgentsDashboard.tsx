/**
 * Enterprise Parallel Agents Dashboard
 * 
 * Comprehensive enterprise-grade dashboard integrating all parallel agents systems:
 * - Advanced orchestration and task distribution
 * - Real-time monitoring and alerting
 * - Performance optimization and analytics
 * - Workflow automation and management
 * - Bulk operations and task management
 * - System health and SLA monitoring
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { AdvancedParallelOrchestrator } from '../../lib/parallel-agents/AdvancedParallelOrchestrator';
import { IntelligentTaskDistributor } from '../../lib/parallel-agents/IntelligentTaskDistributor';
import { RealTimeAnalytics } from '../../lib/parallel-agents/RealTimeAnalytics';
import { PerformanceOptimizer } from '../../lib/parallel-agents/PerformanceOptimizer';
import { WorkflowAutomationEngine } from '../../lib/parallel-agents/WorkflowAutomation';
import { MonitoringAndAlertingSystem } from '../../lib/parallel-agents/MonitoringAndAlerting';
import {
  Task,
  TaskType,
  TaskPriority,
  BulkOperation,
  OrchestrationMetrics,
  DistributionMetrics,
  AgentInfo,
  PerformanceSnapshot,
  OptimizationRecommendation,
  CostAnalysis,
  Alert,
  SystemHealth,
  SystemRecommendation,
  HealthCheck,
  WorkflowDefinition,
  WorkflowExecution,
  WorkflowTemplate,
  WorkflowMetrics
} from '../../lib/parallel-agents/types';

// UI Components
interface TabProps {
  id: string;
  label: string;
  icon: string;
  active: boolean;
  onClick: () => void;
}

const Tab: React.FC<TabProps> = ({ id, label, icon, active, onClick }) => (
  <button
    onClick={onClick}
    className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
      active
        ? 'bg-blue-100 text-blue-700 border border-blue-200'
        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
    }`}
  >
    <span className="mr-2">{icon}</span>
    {label}
  </button>
);

interface MetricCardProps {
  title: string;
  value: string | number;
  unit?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: string;
  status?: 'good' | 'warning' | 'critical';
  icon?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  unit,
  trend,
  trendValue,
  status = 'good',
  icon
}) => {
  const statusColors = {
    good: 'border-green-200 bg-green-50',
    warning: 'border-yellow-200 bg-yellow-50',
    critical: 'border-red-200 bg-red-50'
  };

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    stable: 'text-gray-600'
  };

  const trendIcons = {
    up: '↗️',
    down: '↘️',
    stable: '➡️'
  };

  return (
    <div className={`p-4 rounded-lg border ${statusColors[status]}`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        {icon && <span className="text-lg">{icon}</span>}
      </div>
      <div className="flex items-baseline">
        <span className="text-2xl font-bold text-gray-900">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </span>
        {unit && <span className="ml-1 text-sm text-gray-500">{unit}</span>}
      </div>
      {trend && trendValue && (
        <div className={`flex items-center mt-1 text-xs ${trendColors[trend]}`}>
          <span className="mr-1">{trendIcons[trend]}</span>
          <span>{trendValue}</span>
        </div>
      )}
    </div>
  );
};

interface AlertBadgeProps {
  alert: Alert;
  onClick?: () => void;
}

const AlertBadge: React.FC<AlertBadgeProps> = ({ alert, onClick }) => {
  const severityColors = {
    info: 'bg-blue-100 text-blue-800 border-blue-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    critical: 'bg-red-100 text-red-800 border-red-200',
    emergency: 'bg-purple-100 text-purple-800 border-purple-200'
  };

  return (
    <div
      className={`p-3 rounded-lg border cursor-pointer hover:shadow-md transition-shadow ${
        severityColors[alert.severity]
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-1">
        <span className="text-xs font-medium uppercase">{alert.severity}</span>
        <span className="text-xs text-gray-500">
          {alert.timestamp.toLocaleTimeString()}
        </span>
      </div>
      <h4 className="font-medium text-sm mb-1">{alert.title}</h4>
      <p className="text-xs opacity-75 line-clamp-2">{alert.description}</p>
      <div className="flex items-center mt-2 text-xs">
        <span className="px-2 py-1 bg-white bg-opacity-50 rounded">
          {alert.component}
        </span>
      </div>
    </div>
  );
};

interface ProgressBarProps {
  value: number;
  max: number;
  label?: string;
  color?: 'blue' | 'green' | 'yellow' | 'red';
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max,
  label,
  color = 'blue'
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500'
  };

  return (
    <div className="w-full">
      {label && (
        <div className="flex justify-between text-xs text-gray-600 mb-1">
          <span>{label}</span>
          <span>{value}/{max}</span>
        </div>
      )}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${colorClasses[color]}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

// Main Dashboard Component
interface EnterpriseParallelAgentsDashboardProps {
  orchestrator: AdvancedParallelOrchestrator;
  distributor: IntelligentTaskDistributor;
  analytics: RealTimeAnalytics;
  optimizer: PerformanceOptimizer;
  workflowEngine: WorkflowAutomationEngine;
  monitoring: MonitoringAndAlertingSystem;
}

const EnterpriseParallelAgentsDashboard: React.FC<EnterpriseParallelAgentsDashboardProps> = ({
  orchestrator,
  distributor,
  analytics,
  optimizer,
  workflowEngine,
  monitoring
}) => {
  // State
  const [activeTab, setActiveTab] = useState('overview');
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30000);
  
  // Metrics state
  const [orchestrationMetrics, setOrchestrationMetrics] = useState<OrchestrationMetrics | null>(null);
  const [distributionMetrics, setDistributionMetrics] = useState<DistributionMetrics | null>(null);
  const [performanceSnapshot, setPerformanceSnapshot] = useState<PerformanceSnapshot | null>(null);
  const [systemAlerts, setSystemAlerts] = useState<Alert[]>([]);
  const [recommendations, setRecommendations] = useState<SystemRecommendation[]>([]);
  const [optimizationRecs, setOptimizationRecs] = useState<OptimizationRecommendation[]>([]);
  const [costAnalysis, setCostAnalysis] = useState<CostAnalysis | null>(null);
  const [healthStatus, setHealthStatus] = useState<SystemHealth | null>(null);
  const [workflows, setWorkflows] = useState<WorkflowDefinition[]>([]);
  const [workflowExecutions, setWorkflowExecutions] = useState<WorkflowExecution[]>([]);
  const [agents, setAgents] = useState<AgentInfo[]>([]);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [isOptimizing, setIsOptimizing] = useState(false);
  
  // Refresh data
  const refreshData = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Fetch all metrics in parallel
      const [
        orchMetrics,
        distMetrics,
        perfSnapshot,
        alerts,
        recs,
        optRecs,
        cost,
        health,
        wfDefinitions,
        agentList
      ] = await Promise.all([
        orchestrator.getMetrics(),
        distributor.getMetrics(),
        analytics.getCurrentSnapshot(),
        monitoring.getActiveAlerts(),
        analytics.getRecommendations(),
        optimizer.getRecommendations(),
        optimizer.getCostAnalysis(),
        monitoring.getHealthStatus(),
        workflowEngine.getWorkflows(),
        distributor.getAgents()
      ]);
      
      setOrchestrationMetrics(orchMetrics);
      setDistributionMetrics(distMetrics);
      setPerformanceSnapshot(perfSnapshot);
      setSystemAlerts(alerts);
      setRecommendations(recs);
      setOptimizationRecs(optRecs);
      setCostAnalysis(cost);
      setHealthStatus(health);
      setWorkflows(wfDefinitions);
      setAgents(agentList);
      
      // Get recent workflow executions
      const recentExecutions = wfDefinitions.flatMap(wf => 
        workflowEngine.getWorkflowExecutions(wf.id).slice(0, 5)
      );
      setWorkflowExecutions(recentExecutions);
      
    } catch (error) {
      console.error('Failed to refresh dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [orchestrator, distributor, analytics, optimizer, workflowEngine, monitoring]);
  
  // Auto-refresh effect
  useEffect(() => {
    refreshData();
    
    if (isAutoRefresh) {
      const interval = setInterval(refreshData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshData, isAutoRefresh, refreshInterval]);
  
  // Optimization actions
  const handleOptimizeSystem = useCallback(async () => {
    setIsOptimizing(true);
    try {
      const recommendations = await optimizer.getRecommendations();
      for (const rec of recommendations.slice(0, 3)) { // Apply top 3 recommendations
        await optimizer.applyOptimization(rec.id);
      }
      await refreshData();
    } catch (error) {
      console.error('Optimization failed:', error);
    } finally {
      setIsOptimizing(false);
    }
  }, [optimizer, refreshData]);
  
  // Task submission
  const handleSubmitTask = useCallback(async (task: Omit<Task, 'id'>) => {
    try {
      const fullTask: Task = {
        ...task,
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };
      
      await orchestrator.submitTask(fullTask);
      await refreshData();
    } catch (error) {
      console.error('Failed to submit task:', error);
    }
  }, [orchestrator, refreshData]);
  
  // Workflow execution
  const handleExecuteWorkflow = useCallback(async (workflowId: string, inputs: Record<string, any>) => {
    try {
      await workflowEngine.executeWorkflow(workflowId, inputs);
      await refreshData();
    } catch (error) {
      console.error('Failed to execute workflow:', error);
    }
  }, [workflowEngine, refreshData]);
  
  // Tab configuration
  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'agents', label: 'Agents', icon: '🤖' },
    { id: 'tasks', label: 'Tasks', icon: '📋' },
    { id: 'workflows', label: 'Workflows', icon: '🔄' },
    { id: 'performance', label: 'Performance', icon: '⚡' },
    { id: 'monitoring', label: 'Monitoring', icon: '👁️' },
    { id: 'optimization', label: 'Optimization', icon: '🎯' },
    { id: 'analytics', label: 'Analytics', icon: '📈' }
  ];
  
  // Computed values
  const systemStatus = useMemo(() => {
    if (!healthStatus) return 'unknown';
    return healthStatus.overall;
  }, [healthStatus]);
  
  const criticalAlerts = useMemo(() => {
    return systemAlerts.filter(alert => alert.severity === 'critical' || alert.severity === 'emergency');
  }, [systemAlerts]);
  
  const activeAgentsCount = useMemo(() => {
    return agents.filter(agent => agent.status === 'active').length;
  }, [agents]);
  
  const totalTasks = useMemo(() => {
    return orchestrationMetrics?.totalTasks || 0;
  }, [orchestrationMetrics]);
  
  const successRate = useMemo(() => {
    return orchestrationMetrics?.successRate || 0;
  }, [orchestrationMetrics]);
  
  // Render loading state
  if (isLoading && !orchestrationMetrics) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Enterprise Dashboard...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="w-full h-full bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Enterprise Parallel Agents Dashboard
            </h1>
            <p className="text-sm text-gray-600 mt-1">
              Advanced orchestration, monitoring, and optimization platform
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* System Status */}
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${
                systemStatus === 'healthy' ? 'bg-green-500' :
                systemStatus === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <span className="text-sm font-medium capitalize">{systemStatus}</span>
            </div>
            
            {/* Critical Alerts */}
            {criticalAlerts.length > 0 && (
              <div className="flex items-center space-x-1 text-red-600">
                <span className="text-sm">🚨</span>
                <span className="text-sm font-medium">{criticalAlerts.length} Critical</span>
              </div>
            )}
            
            {/* Auto-refresh toggle */}
            <label className="flex items-center space-x-2 text-sm">
              <input
                type="checkbox"
                checked={isAutoRefresh}
                onChange={(e) => setIsAutoRefresh(e.target.checked)}
                className="rounded"
              />
              <span>Auto-refresh</span>
            </label>
            
            {/* Refresh button */}
            <button
              onClick={refreshData}
              disabled={isLoading}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
            >
              {isLoading ? '🔄' : '↻'} Refresh
            </button>
            
            {/* Optimize button */}
            <button
              onClick={handleOptimizeSystem}
              disabled={isOptimizing}
              className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm"
            >
              {isOptimizing ? '⚡ Optimizing...' : '🎯 Optimize'}
            </button>
          </div>
        </div>
      </div>
      
      {/* Navigation Tabs */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex space-x-2 overflow-x-auto">
          {tabs.map(tab => (
            <Tab
              key={tab.id}
              id={tab.id}
              label={tab.label}
              icon={tab.icon}
              active={activeTab === tab.id}
              onClick={() => setActiveTab(tab.id)}
            />
          ))}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <MetricCard
                title="Active Agents"
                value={activeAgentsCount}
                icon="🤖"
                status={activeAgentsCount > 0 ? 'good' : 'warning'}
              />
              <MetricCard
                title="Total Tasks"
                value={totalTasks}
                icon="📋"
                trend="up"
                trendValue="+12% today"
              />
              <MetricCard
                title="Success Rate"
                value={successRate.toFixed(1)}
                unit="%"
                icon="✅"
                status={successRate >= 95 ? 'good' : successRate >= 90 ? 'warning' : 'critical'}
              />
              <MetricCard
                title="Avg Response Time"
                value={performanceSnapshot?.responseTime.toFixed(0) || '0'}
                unit="ms"
                icon="⚡"
                status={performanceSnapshot && performanceSnapshot.responseTime < 1000 ? 'good' : 'warning'}
              />
            </div>
            
            {/* System Health & Alerts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* System Health */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold mb-4">System Health</h3>
                {healthStatus && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {healthStatus.components.filter(c => c.status === 'healthy').length}
                        </div>
                        <div className="text-green-700">Healthy</div>
                      </div>
                      <div className="text-center p-3 bg-yellow-50 rounded-lg">
                        <div className="text-2xl font-bold text-yellow-600">
                          {healthStatus.components.filter(c => c.status === 'degraded').length}
                        </div>
                        <div className="text-yellow-700">Degraded</div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      {healthStatus.components.slice(0, 5).map(component => (
                        <div key={component.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                          <span className="text-sm font-medium">{component.name}</span>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              component.status === 'healthy' ? 'bg-green-500' :
                              component.status === 'degraded' ? 'bg-yellow-500' : 'bg-red-500'
                            }`} />
                            <span className="text-xs text-gray-500">
                              {component.responseTime}ms
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Active Alerts */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold mb-4">Active Alerts</h3>
                <div className="space-y-3 max-h-80 overflow-y-auto">
                  {systemAlerts.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <span className="text-2xl">✅</span>
                      <p className="mt-2">No active alerts</p>
                    </div>
                  ) : (
                    systemAlerts.slice(0, 5).map(alert => (
                      <AlertBadge
                        key={alert.id}
                        alert={alert}
                        onClick={() => setActiveTab('monitoring')}
                      />
                    ))
                  )}
                </div>
              </div>
            </div>
            
            {/* Recent Activity */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">Recent Workflow Executions</h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-2">Workflow</th>
                      <th className="text-left py-2">Status</th>
                      <th className="text-left py-2">Progress</th>
                      <th className="text-left py-2">Duration</th>
                      <th className="text-left py-2">Started</th>
                    </tr>
                  </thead>
                  <tbody>
                    {workflowExecutions.slice(0, 5).map(execution => (
                      <tr key={execution.id} className="border-b border-gray-100 last:border-b-0">
                        <td className="py-3">
                          <div className="font-medium">{execution.workflowId}</div>
                          <div className="text-xs text-gray-500">{execution.id}</div>
                        </td>
                        <td className="py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            execution.status === 'completed' ? 'bg-green-100 text-green-800' :
                            execution.status === 'running' ? 'bg-blue-100 text-blue-800' :
                            execution.status === 'failed' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {execution.status}
                          </span>
                        </td>
                        <td className="py-3">
                          <ProgressBar
                            value={execution.progress.completedSteps}
                            max={execution.progress.totalSteps}
                            color={execution.status === 'failed' ? 'red' : 'blue'}
                          />
                        </td>
                        <td className="py-3">
                          {execution.duration ? `${Math.round(execution.duration / 1000)}s` : '-'}
                        </td>
                        <td className="py-3 text-gray-500">
                          {execution.startTime.toLocaleTimeString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {/* Agents Tab */}
        {activeTab === 'agents' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">Agent Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {agents.map(agent => (
                  <div key={agent.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{agent.name}</h4>
                      <div className={`w-3 h-3 rounded-full ${
                        agent.status === 'active' ? 'bg-green-500' :
                        agent.status === 'busy' ? 'bg-yellow-500' : 'bg-gray-500'
                      }`} />
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{agent.type}</p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span>Load:</span>
                        <span>{agent.currentLoad}/{agent.maxCapacity}</span>
                      </div>
                      <ProgressBar
                        value={agent.currentLoad}
                        max={agent.maxCapacity}
                        color={agent.currentLoad / agent.maxCapacity > 0.8 ? 'red' : 'green'}
                      />
                      <div className="flex justify-between">
                        <span>Success Rate:</span>
                        <span>{(agent.successRate * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {/* Tasks Tab */}
        {activeTab === 'tasks' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Task Management</h3>
                <button
                  onClick={() => {
                    const task = {
                      title: 'Sample Task',
                      description: 'A sample task for testing',
                      type: 'analysis' as TaskType,
                      priority: 'medium' as TaskPriority,
                      status: 'pending' as const,
                      inputs: { data: 'sample data' },
                      metadata: {
                        created_at: new Date(),
                        estimated_duration: 300
                      }
                    };
                    handleSubmitTask(task);
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                >
                  + Submit Sample Task
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <MetricCard
                  title="Queued Tasks"
                  value={orchestrationMetrics?.queuedTasks || 0}
                  icon="⏳"
                />
                <MetricCard
                  title="Running Tasks"
                  value={orchestrationMetrics?.runningTasks || 0}
                  icon="🏃"
                />
                <MetricCard
                  title="Completed Today"
                  value={orchestrationMetrics?.completedTasks || 0}
                  icon="✅"
                />
              </div>
              
              {distributionMetrics && (
                <div className="space-y-4">
                  <h4 className="font-medium">Task Distribution</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium mb-2">By Type</h5>
                      <div className="space-y-2">
                        {Object.entries(distributionMetrics.tasksByType).map(([type, count]) => (
                          <div key={type} className="flex items-center justify-between text-sm">
                            <span className="capitalize">{type}</span>
                            <span className="font-medium">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium mb-2">By Priority</h5>
                      <div className="space-y-2">
                        {Object.entries(distributionMetrics.tasksByPriority).map(([priority, count]) => (
                          <div key={priority} className="flex items-center justify-between text-sm">
                            <span className="capitalize">{priority}</span>
                            <span className="font-medium">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* Workflows Tab */}
        {activeTab === 'workflows' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Workflow Management</h3>
                <button
                  onClick={() => {
                    if (workflows.length > 0) {
                      handleExecuteWorkflow(workflows[0].id, { projectPath: '/sample/project' });
                    }
                  }}
                  disabled={workflows.length === 0}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm"
                >
                  ▶️ Execute Sample Workflow
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {workflows.map(workflow => (
                  <div key={workflow.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{workflow.name}</h4>
                      <span className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded">
                        v{workflow.version}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{workflow.description}</p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span>Steps:</span>
                        <span>{workflow.steps.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Complexity:</span>
                        <span className="capitalize">{workflow.metadata.complexity}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Est. Duration:</span>
                        <span>{workflow.metadata.estimatedDuration}m</span>
                      </div>
                    </div>
                    <div className="mt-3 flex space-x-2">
                      <button
                        onClick={() => handleExecuteWorkflow(workflow.id, {})}
                        className="flex-1 px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700"
                      >
                        Execute
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <div className="space-y-6">
            {performanceSnapshot && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <MetricCard
                  title="Throughput"
                  value={performanceSnapshot.throughput.toFixed(1)}
                  unit="req/s"
                  icon="🚀"
                />
                <MetricCard
                  title="Response Time"
                  value={performanceSnapshot.responseTime.toFixed(0)}
                  unit="ms"
                  icon="⚡"
                />
                <MetricCard
                  title="Error Rate"
                  value={performanceSnapshot.errorRate.toFixed(2)}
                  unit="%"
                  icon="❌"
                  status={performanceSnapshot.errorRate < 1 ? 'good' : performanceSnapshot.errorRate < 5 ? 'warning' : 'critical'}
                />
                <MetricCard
                  title="Resource Usage"
                  value={performanceSnapshot.resourceUsage.cpu.toFixed(1)}
                  unit="%"
                  icon="💻"
                  status={performanceSnapshot.resourceUsage.cpu < 70 ? 'good' : performanceSnapshot.resourceUsage.cpu < 90 ? 'warning' : 'critical'}
                />
              </div>
            )}
            
            {costAnalysis && (
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold mb-4">Cost Analysis</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      ${costAnalysis.totalCost.toFixed(2)}
                    </div>
                    <div className="text-blue-700 text-sm">Total Cost</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      ${costAnalysis.costPerTask.toFixed(3)}
                    </div>
                    <div className="text-green-700 text-sm">Cost per Task</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {costAnalysis.efficiency.toFixed(1)}%
                    </div>
                    <div className="text-purple-700 text-sm">Efficiency</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* Monitoring Tab */}
        {activeTab === 'monitoring' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Alerts */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold mb-4">All Alerts</h3>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {systemAlerts.map(alert => (
                    <AlertBadge key={alert.id} alert={alert} />
                  ))}
                </div>
              </div>
              
              {/* System Health Details */}
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold mb-4">Component Health</h3>
                {healthStatus && (
                  <div className="space-y-3">
                    {healthStatus.components.map(component => (
                      <div key={component.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div>
                          <div className="font-medium">{component.name}</div>
                          <div className="text-sm text-gray-600">{component.component}</div>
                          {component.message && (
                            <div className="text-xs text-gray-500 mt-1">{component.message}</div>
                          )}
                        </div>
                        <div className="text-right">
                          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            component.status === 'healthy' ? 'bg-green-100 text-green-800' :
                            component.status === 'degraded' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {component.status}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {component.responseTime}ms
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        
        {/* Optimization Tab */}
        {activeTab === 'optimization' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Optimization Recommendations</h3>
                <button
                  onClick={handleOptimizeSystem}
                  disabled={isOptimizing}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm"
                >
                  {isOptimizing ? '⚡ Optimizing...' : '🎯 Apply Top Recommendations'}
                </button>
              </div>
              
              <div className="space-y-4">
                {optimizationRecs.map(rec => (
                  <div key={rec.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{rec.title}</h4>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                          rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {rec.priority} priority
                        </span>
                        <span className="text-sm text-green-600 font-medium">
                          +{rec.expectedImprovement.toFixed(1)}% improvement
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Category: {rec.category}</span>
                      <button
                        onClick={() => optimizer.applyOptimization(rec.id)}
                        className="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold mb-4">System Recommendations</h3>
              <div className="space-y-4">
                {recommendations.map(rec => (
                  <div key={rec.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{rec.title}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                        rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {rec.priority}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{rec.description}</p>
                    <div className="text-xs text-gray-500">
                      Category: {rec.category} • Impact: {rec.impact}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnterpriseParallelAgentsDashboard;