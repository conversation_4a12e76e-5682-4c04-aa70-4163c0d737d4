import React from 'react';
import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface LoadingFallbackProps {
  className?: string;
  variant?: 'spinner' | 'card' | 'minimal';
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const LoadingFallback: React.FC<LoadingFallbackProps> = ({
  className,
  variant = 'spinner',
  message = 'Loading...',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  const containerClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center justify-center gap-2', className)}>
        <Loader2 className={cn('animate-spin text-muted-foreground', sizeClasses[size])} />
        {message && <span className="text-sm text-muted-foreground">{message}</span>}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <Card className={cn(className)}>
        <CardContent className={cn('flex flex-col items-center justify-center text-center', containerClasses[size])}>
          <Loader2 className={cn('animate-spin text-muted-foreground mb-3', sizeClasses[size])} />
          <p className="text-sm text-muted-foreground">{message}</p>
        </CardContent>
      </Card>
    );
  }

  // Default spinner variant
  return (
    <div className={cn('flex flex-col items-center justify-center text-center', containerClasses[size], className)}>
      <Loader2 className={cn('animate-spin text-muted-foreground mb-3', sizeClasses[size])} />
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
};

// Specific loading components for common use cases
export const PageLoadingFallback: React.FC<{ message?: string }> = ({ message = 'Loading page...' }) => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingFallback variant="spinner" size="lg" message={message} />
  </div>
);

export const ComponentLoadingFallback: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <LoadingFallback variant="card" size="md" message={message} />
);

export const InlineLoadingFallback: React.FC<{ message?: string }> = ({ message }) => (
  <LoadingFallback variant="minimal" size="sm" message={message} />
);