import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Activity,
  Cpu,
  Memory,
  Zap,
  Users,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  Settings,
  BarChart3,
  Network,
  AlertTriangle,
  CheckCircle,
  Clock,
  Layers,
  GitBranch,
  Workflow,
  Scale,
  Shield,
  Code,
  FileText,
  TestTube
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ClusterMetrics {
  id: string;
  name: string;
  agents: number;
  activeAgents: number;
  utilization: number;
  throughput: number;
  successRate: number;
  averageResponseTime: number;
  specialization: string;
  status: 'healthy' | 'warning' | 'critical';
}

interface SystemMetrics {
  totalClusters: number;
  totalAgents: number;
  activeAgents: number;
  averageUtilization: number;
  overallSuccessRate: number;
  averageResponseTime: number;
  tasksProcessed: number;
  tasksInQueue: number;
}

interface ExecutionPlan {
  id: string;
  name: string;
  strategy: 'parallel' | 'pipeline' | 'hybrid';
  tasks: number;
  estimatedDuration: number;
  progress: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

const CLUSTER_ICONS = {
  code_analysis: Code,
  content_generation: FileText,
  testing: TestTube,
  security: Shield
};

export function ParallelAgentsDashboard() {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics>({
    totalClusters: 4,
    totalAgents: 24,
    activeAgents: 18,
    averageUtilization: 0.75,
    overallSuccessRate: 0.94,
    averageResponseTime: 2340,
    tasksProcessed: 1247,
    tasksInQueue: 23
  });

  const [clusters, setClusters] = useState<ClusterMetrics[]>([
    {
      id: 'code-analysis',
      name: 'Code Analysis',
      agents: 8,
      activeAgents: 6,
      utilization: 0.85,
      throughput: 12.4,
      successRate: 0.96,
      averageResponseTime: 2100,
      specialization: 'code_analysis',
      status: 'healthy'
    },
    {
      id: 'content-generation',
      name: 'Content Generation',
      agents: 6,
      activeAgents: 5,
      utilization: 0.72,
      throughput: 8.7,
      successRate: 0.92,
      averageResponseTime: 3200,
      specialization: 'content_generation',
      status: 'healthy'
    },
    {
      id: 'testing',
      name: 'Testing & QA',
      agents: 5,
      activeAgents: 4,
      utilization: 0.68,
      throughput: 6.3,
      successRate: 0.89,
      averageResponseTime: 2800,
      specialization: 'testing',
      status: 'warning'
    },
    {
      id: 'security',
      name: 'Security Analysis',
      agents: 5,
      activeAgents: 3,
      utilization: 0.78,
      throughput: 4.2,
      successRate: 0.98,
      averageResponseTime: 4100,
      specialization: 'security',
      status: 'healthy'
    }
  ]);

  const [executionPlans, setExecutionPlans] = useState<ExecutionPlan[]>([
    {
      id: 'plan-1',
      name: 'Full Repository Analysis',
      strategy: 'hybrid',
      tasks: 156,
      estimatedDuration: 1800,
      progress: 67,
      status: 'running'
    },
    {
      id: 'plan-2',
      name: 'Security Audit Batch',
      strategy: 'parallel',
      tasks: 43,
      estimatedDuration: 900,
      progress: 100,
      status: 'completed'
    },
    {
      id: 'plan-3',
      name: 'Documentation Generation',
      strategy: 'pipeline',
      tasks: 89,
      estimatedDuration: 1200,
      progress: 23,
      status: 'running'
    }
  ]);

  const [scalingConfig, setScalingConfig] = useState({
    autoScale: true,
    minAgents: 3,
    maxAgents: 50,
    scaleUpThreshold: 80,
    scaleDownThreshold: 30
  });

  const [isSystemRunning, setIsSystemRunning] = useState(true);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      updateMetrics();
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const updateMetrics = useCallback(() => {
    setClusters(prev => prev.map(cluster => ({
      ...cluster,
      utilization: Math.max(0.1, Math.min(1, cluster.utilization + (Math.random() - 0.5) * 0.1)),
      throughput: Math.max(1, cluster.throughput + (Math.random() - 0.5) * 2),
      averageResponseTime: Math.max(1000, cluster.averageResponseTime + (Math.random() - 0.5) * 500),
      activeAgents: Math.max(1, Math.min(cluster.agents, cluster.activeAgents + Math.floor((Math.random() - 0.5) * 2)))
    })));

    setExecutionPlans(prev => prev.map(plan => 
      plan.status === 'running' ? {
        ...plan,
        progress: Math.min(100, plan.progress + Math.random() * 3)
      } : plan
    ));
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'critical': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization > 0.8) return 'bg-red-500';
    if (utilization > 0.6) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header */}
      <div className="border-b p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Parallel Agents Dashboard</h1>
            <p className="text-muted-foreground">
              Real-time monitoring and control of distributed AI agent clusters
            </p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={cn("w-2 h-2 rounded-full", isSystemRunning ? "bg-green-500" : "bg-red-500")} />
              <span className="text-sm font-medium">
                {isSystemRunning ? 'System Active' : 'System Paused'}
              </span>
            </div>
            <Button
              variant={isSystemRunning ? "destructive" : "default"}
              size="sm"
              onClick={() => setIsSystemRunning(!isSystemRunning)}
            >
              {isSystemRunning ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
              {isSystemRunning ? 'Pause' : 'Resume'}
            </Button>
          </div>
        </div>
      </div>

      {/* System Overview */}
      <div className="p-6 border-b">
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Layers className="w-4 h-4 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold">{systemMetrics.totalClusters}</div>
                  <div className="text-xs text-muted-foreground">Clusters</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{systemMetrics.totalAgents}</div>
                  <div className="text-xs text-muted-foreground">Total Agents</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Activity className="w-4 h-4 text-orange-500" />
                <div>
                  <div className="text-2xl font-bold">{systemMetrics.activeAgents}</div>
                  <div className="text-xs text-muted-foreground">Active</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Cpu className="w-4 h-4 text-purple-500" />
                <div>
                  <div className="text-2xl font-bold">{Math.round(systemMetrics.averageUtilization * 100)}%</div>
                  <div className="text-xs text-muted-foreground">Utilization</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{Math.round(systemMetrics.overallSuccessRate * 100)}%</div>
                  <div className="text-xs text-muted-foreground">Success Rate</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-500" />
                <div>
                  <div className="text-2xl font-bold">{(systemMetrics.averageResponseTime / 1000).toFixed(1)}s</div>
                  <div className="text-xs text-muted-foreground">Avg Response</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <div>
                  <div className="text-2xl font-bold">{formatNumber(systemMetrics.tasksProcessed)}</div>
                  <div className="text-xs text-muted-foreground">Processed</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Workflow className="w-4 h-4 text-yellow-500" />
                <div>
                  <div className="text-2xl font-bold">{systemMetrics.tasksInQueue}</div>
                  <div className="text-xs text-muted-foreground">In Queue</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <Tabs defaultValue="clusters" className="h-full flex flex-col">
          <TabsList className="mx-6 mt-4">
            <TabsTrigger value="clusters">Agent Clusters</TabsTrigger>
            <TabsTrigger value="execution">Execution Plans</TabsTrigger>
            <TabsTrigger value="monitoring">Real-time Monitoring</TabsTrigger>
            <TabsTrigger value="scaling">Auto Scaling</TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden p-6">
            <TabsContent value="clusters" className="h-full">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                {clusters.map((cluster) => {
                  const Icon = CLUSTER_ICONS[cluster.specialization as keyof typeof CLUSTER_ICONS] || Code;
                  
                  return (
                    <motion.div
                      key={cluster.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="h-fit"
                    >
                      <Card>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Icon className="w-6 h-6" />
                              <div>
                                <CardTitle className="text-lg">{cluster.name}</CardTitle>
                                <CardDescription>
                                  {cluster.activeAgents}/{cluster.agents} agents active
                                </CardDescription>
                              </div>
                            </div>
                            <Badge variant={cluster.status === 'healthy' ? 'default' : 'destructive'}>
                              {cluster.status}
                            </Badge>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {/* Utilization */}
                          <div>
                            <div className="flex justify-between text-sm mb-2">
                              <span>Utilization</span>
                              <span>{Math.round(cluster.utilization * 100)}%</span>
                            </div>
                            <Progress 
                              value={cluster.utilization * 100} 
                              className="h-2"
                            />
                          </div>

                          {/* Metrics Grid */}
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-3 bg-muted rounded-lg">
                              <div className="text-2xl font-bold text-green-600">
                                {cluster.throughput.toFixed(1)}
                              </div>
                              <div className="text-xs text-muted-foreground">Tasks/min</div>
                            </div>
                            <div className="text-center p-3 bg-muted rounded-lg">
                              <div className="text-2xl font-bold text-blue-600">
                                {Math.round(cluster.successRate * 100)}%
                              </div>
                              <div className="text-xs text-muted-foreground">Success Rate</div>
                            </div>
                          </div>

                          {/* Response Time */}
                          <div className="text-center p-3 bg-muted rounded-lg">
                            <div className="text-lg font-bold">
                              {(cluster.averageResponseTime / 1000).toFixed(1)}s
                            </div>
                            <div className="text-xs text-muted-foreground">Avg Response Time</div>
                          </div>

                          {/* Actions */}
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline" className="flex-1">
                              <Scale className="w-4 h-4 mr-2" />
                              Scale
                            </Button>
                            <Button size="sm" variant="outline" className="flex-1">
                              <Settings className="w-4 h-4 mr-2" />
                              Configure
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="execution" className="h-full">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Active Execution Plans</h3>
                  <Button>
                    <GitBranch className="w-4 h-4 mr-2" />
                    Create New Plan
                  </Button>
                </div>

                <div className="grid gap-4">
                  {executionPlans.map((plan) => (
                    <Card key={plan.id}>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h4 className="font-semibold">{plan.name}</h4>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>Strategy: {plan.strategy}</span>
                              <span>Tasks: {plan.tasks}</span>
                              <span>ETA: {formatDuration(plan.estimatedDuration)}</span>
                            </div>
                          </div>
                          <Badge variant={
                            plan.status === 'completed' ? 'default' :
                            plan.status === 'running' ? 'secondary' :
                            plan.status === 'failed' ? 'destructive' : 'outline'
                          }>
                            {plan.status}
                          </Badge>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{Math.round(plan.progress)}%</span>
                          </div>
                          <Progress value={plan.progress} />
                        </div>

                        {plan.status === 'running' && (
                          <div className="flex gap-2 mt-4">
                            <Button size="sm" variant="outline">
                              <Pause className="w-4 h-4 mr-2" />
                              Pause
                            </Button>
                            <Button size="sm" variant="outline">
                              <BarChart3 className="w-4 h-4 mr-2" />
                              Details
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="monitoring" className="h-full">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                <Card>
                  <CardHeader>
                    <CardTitle>System Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="h-48 bg-muted rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <BarChart3 className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">Real-time performance chart</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Resource Utilization</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>CPU Usage</span>
                          <span>72%</span>
                        </div>
                        <Progress value={72} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Memory Usage</span>
                          <span>68%</span>
                        </div>
                        <Progress value={68} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Token Usage</span>
                          <span>45%</span>
                        </div>
                        <Progress value={45} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle>System Alerts</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <AlertTriangle className="w-5 h-5 text-yellow-600" />
                        <div>
                          <div className="font-medium">Testing cluster performance degraded</div>
                          <div className="text-sm text-muted-foreground">Success rate dropped to 89%</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <div>
                          <div className="font-medium">Auto-scaling triggered</div>
                          <div className="text-sm text-muted-foreground">Added 2 agents to code analysis cluster</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="scaling" className="h-full">
              <div className="max-w-2xl space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Auto Scaling Configuration</CardTitle>
                    <CardDescription>
                      Configure automatic scaling behavior for agent clusters
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="auto-scale">Enable Auto Scaling</Label>
                      <Switch
                        id="auto-scale"
                        checked={scalingConfig.autoScale}
                        onCheckedChange={(checked) => 
                          setScalingConfig(prev => ({ ...prev, autoScale: checked }))
                        }
                      />
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label>Minimum Agents: {scalingConfig.minAgents}</Label>
                        <Slider
                          value={[scalingConfig.minAgents]}
                          onValueChange={([value]) => 
                            setScalingConfig(prev => ({ ...prev, minAgents: value }))
                          }
                          max={20}
                          min={1}
                          step={1}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Maximum Agents: {scalingConfig.maxAgents}</Label>
                        <Slider
                          value={[scalingConfig.maxAgents]}
                          onValueChange={([value]) => 
                            setScalingConfig(prev => ({ ...prev, maxAgents: value }))
                          }
                          max={100}
                          min={5}
                          step={5}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Scale Up Threshold: {scalingConfig.scaleUpThreshold}%</Label>
                        <Slider
                          value={[scalingConfig.scaleUpThreshold]}
                          onValueChange={([value]) => 
                            setScalingConfig(prev => ({ ...prev, scaleUpThreshold: value }))
                          }
                          max={100}
                          min={50}
                          step={5}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Scale Down Threshold: {scalingConfig.scaleDownThreshold}%</Label>
                        <Slider
                          value={[scalingConfig.scaleDownThreshold]}
                          onValueChange={([value]) => 
                            setScalingConfig(prev => ({ ...prev, scaleDownThreshold: value }))
                          }
                          max={50}
                          min={10}
                          step={5}
                          className="mt-2"
                        />
                      </div>
                    </div>

                    <Button className="w-full">
                      Apply Configuration
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Manual Scaling</CardTitle>
                    <CardDescription>
                      Manually adjust cluster sizes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {clusters.map((cluster) => (
                        <div key={cluster.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="font-medium">{cluster.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {cluster.agents} agents
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline">
                              <TrendingDown className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <TrendingUp className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
}