import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON><PERSON>, 
  Zap, 
  Brain, 
  Palette, 
  Mic, 
  Eye, 
  Gamepad2, 
  Workflow,
  MessageCircle,
  Code,
  Search,
  Layers
} from 'lucide-react';

interface AmazingFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  category: 'AI' | 'UX' | 'Productivity' | 'Creative';
  status: 'concept' | 'development' | 'beta' | 'ready';
  impact: 'low' | 'medium' | 'high' | 'revolutionary';
}

const amazingFeatures: AmazingFeature[] = [
  {
    id: 'ai-context-awareness',
    title: 'AI Context Awareness',
    description: '<PERSON> automatically understands your project context, coding style, and preferences across sessions',
    icon: <Brain className="w-6 h-6" />,
    category: 'AI',
    status: 'concept',
    impact: 'revolutionary'
  },
  {
    id: 'voice-coding',
    title: 'Voice-to-Code',
    description: 'Speak your ideas and watch them transform into clean, executable code in real-time',
    icon: <Mic className="w-6 h-6" />,
    category: 'Productivity',
    status: 'concept',
    impact: 'high'
  },
  {
    id: 'visual-debugging',
    title: 'Visual Code Flow',
    description: 'See your code execution flow in beautiful, interactive diagrams that help debug complex logic',
    icon: <Eye className="w-6 h-6" />,
    category: 'Productivity',
    status: 'development',
    impact: 'high'
  },
  {
    id: 'ai-pair-programming',
    title: 'AI Pair Programming',
    description: 'Real-time collaborative coding with AI that suggests, refactors, and optimizes as you type',
    icon: <Gamepad2 className="w-6 h-6" />,
    category: 'AI',
    status: 'beta',
    impact: 'revolutionary'
  },
  {
    id: 'smart-themes',
    title: 'Adaptive Themes',
    description: 'Themes that change based on time of day, project type, and your mood for optimal focus',
    icon: <Palette className="w-6 h-6" />,
    category: 'UX',
    status: 'ready',
    impact: 'medium'
  },
  {
    id: 'workflow-automation',
    title: 'Workflow Automation',
    description: 'Create custom automation workflows that handle repetitive tasks across your entire development pipeline',
    icon: <Workflow className="w-6 h-6" />,
    category: 'Productivity',
    status: 'development',
    impact: 'high'
  },
  {
    id: 'natural-language-search',
    title: 'Natural Language Search',
    description: 'Search your codebase using plain English: "Find the function that handles user authentication"',
    icon: <Search className="w-6 h-6" />,
    category: 'Productivity',
    status: 'concept',
    impact: 'high'
  },
  {
    id: 'code-storytelling',
    title: 'Code Storytelling',
    description: 'Generate beautiful documentation that tells the story of your code with interactive examples',
    icon: <MessageCircle className="w-6 h-6" />,
    category: 'Creative',
    status: 'beta',
    impact: 'medium'
  },
  {
    id: 'multi-dimensional-coding',
    title: 'Multi-Dimensional Coding',
    description: 'Work with code in 3D space, visualizing complex relationships and dependencies',
    icon: <Layers className="w-6 h-6" />,
    category: 'Creative',
    status: 'concept',
    impact: 'revolutionary'
  },
  {
    id: 'ai-code-evolution',
    title: 'AI Code Evolution',
    description: 'Watch your code evolve and improve automatically based on best practices and performance metrics',
    icon: <Zap className="w-6 h-6" />,
    category: 'AI',
    status: 'concept',
    impact: 'revolutionary'
  }
];

const statusColors = {
  concept: 'bg-gray-100 text-gray-800',
  development: 'bg-blue-100 text-blue-800',
  beta: 'bg-yellow-100 text-yellow-800',
  ready: 'bg-green-100 text-green-800'
};

const impactColors = {
  low: 'border-gray-200',
  medium: 'border-blue-200',
  high: 'border-orange-200',
  revolutionary: 'border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50'
};

interface AmazingFeaturesProps {
  onClose: () => void;
}

export const AmazingFeatures: React.FC<AmazingFeaturesProps> = ({ onClose }) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [hoveredFeature, setHoveredFeature] = useState<string | null>(null);

  const categories = ['all', 'AI', 'UX', 'Productivity', 'Creative'];
  
  const filteredFeatures = selectedCategory === 'all' 
    ? amazingFeatures 
    : amazingFeatures.filter(f => f.category === selectedCategory);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-background rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
      >
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold flex items-center gap-2">
                <Sparkles className="w-6 h-6 text-purple-500" />
                Amazing Features Coming Soon
              </h2>
              <p className="text-muted-foreground mt-1">
                Revolutionary features that will transform your coding experience
              </p>
            </div>
            <Button variant="ghost" onClick={onClose}>✕</Button>
          </div>
          
          {/* Category Filter */}
          <div className="flex gap-2 mt-4">
            {categories.map(category => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="capitalize"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence mode="popLayout">
              {filteredFeatures.map((feature, index) => (
                <motion.div
                  key={feature.id}
                  layout
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                  onHoverStart={() => setHoveredFeature(feature.id)}
                  onHoverEnd={() => setHoveredFeature(null)}
                >
                  <Card className={`h-full hover-lift ${impactColors[feature.impact]}`}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {feature.icon}
                          <Badge className={statusColors[feature.status]}>
                            {feature.status}
                          </Badge>
                        </div>
                        {feature.impact === 'revolutionary' && (
                          <Sparkles className="w-4 h-4 text-purple-500" />
                        )}
                      </div>
                      <CardTitle className="text-lg">{feature.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {feature.description}
                      </p>
                      
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ 
                          opacity: hoveredFeature === feature.id ? 1 : 0,
                          height: hoveredFeature === feature.id ? 'auto' : 0
                        }}
                        className="mt-3 pt-3 border-t"
                      >
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-muted-foreground">Impact:</span>
                          <Badge variant="outline" className="capitalize">
                            {feature.impact}
                          </Badge>
                        </div>
                      </motion.div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>

        <div className="p-6 border-t bg-muted/30">
          <div className="text-center">
            <p className="text-sm text-muted-foreground mb-3">
              Want to help shape the future of Claudia? Join our beta program!
            </p>
            <div className="flex gap-2 justify-center">
              <Button size="sm">Join Beta Program</Button>
              <Button variant="outline" size="sm">Request Feature</Button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};