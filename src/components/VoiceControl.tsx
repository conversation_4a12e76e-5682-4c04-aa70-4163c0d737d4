import { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Popover } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  Settings2,
  Zap,
  AlertCircle,
  CheckCircle2,
  Info
} from 'lucide-react';
import { useFeatureFlags } from '@/lib/featureFlags';

interface VoiceControlProps {
  onCommand: (command: string) => void;
  isLoading?: boolean;
  className?: string;
}

interface VoiceCommand {
  pattern: RegExp;
  action: string;
  description: string;
}

const VOICE_COMMANDS: VoiceCommand[] = [
  // Session Management
  { pattern: /^(run|execute) code review$/i, action: 'code-review', description: 'Run code review' },
  { pattern: /^(show|open) timeline$/i, action: 'show-timeline', description: 'Show timeline' },
  { pattern: /^(show|open) settings$/i, action: 'show-settings', description: 'Show settings' },
  { pattern: /^(create|make) checkpoint$/i, action: 'create-checkpoint', description: 'Create checkpoint' },
  { pattern: /^(send|submit) prompt (.+)$/i, action: 'send-prompt', description: 'Send prompt' },
  { pattern: /^(cancel|stop) execution$/i, action: 'cancel-execution', description: 'Cancel execution' },
  
  // Navigation
  { pattern: /^(scroll|go) to top$/i, action: 'scroll-top', description: 'Scroll to top' },
  { pattern: /^(scroll|go) to bottom$/i, action: 'scroll-bottom', description: 'Scroll to bottom' },
  
  // Model Switching
  { pattern: /^(switch|change) to opus$/i, action: 'switch-opus', description: 'Switch to Opus model' },
  { pattern: /^(switch|change) to sonnet$/i, action: 'switch-sonnet', description: 'Switch to Sonnet model' },
  
  // NEW: Customization Commands
  { pattern: /^(show|open) customizer$/i, action: 'show-customizer', description: 'Show session customizer' },
  { pattern: /^(show|open) visual effects$/i, action: 'show-visual-effects', description: 'Show visual effects panel' },
  
  // NEW: Mode Switching
  { pattern: /^(switch|change) to development mode$/i, action: 'mode-development', description: 'Switch to development mode' },
  { pattern: /^(switch|change) to analysis mode$/i, action: 'mode-analysis', description: 'Switch to analysis mode' },
  { pattern: /^(switch|change) to creative mode$/i, action: 'mode-creative', description: 'Switch to creative mode' },
  { pattern: /^(switch|change) to secure mode$/i, action: 'mode-secure', description: 'Switch to secure mode' },
  
  // NEW: Visual Theme Commands
  { pattern: /^(enable|activate) cyberpunk mode$/i, action: 'theme-cyberpunk', description: 'Enable cyberpunk theme' },
  { pattern: /^(enable|activate) holographic mode$/i, action: 'theme-holographic', description: 'Enable holographic theme' },
  { pattern: /^(enable|activate) neon mode$/i, action: 'theme-neon', description: 'Enable neon theme' },
  
  // NEW: Feature Toggles
  { pattern: /^(enable|turn on) collaboration$/i, action: 'enable-collaboration', description: 'Enable collaboration' },
  { pattern: /^(disable|turn off) collaboration$/i, action: 'disable-collaboration', description: 'Disable collaboration' },
  { pattern: /^(enable|turn on) code flow$/i, action: 'enable-codeflow', description: 'Enable code flow' },
  { pattern: /^(disable|turn off) code flow$/i, action: 'disable-codeflow', description: 'Disable code flow' },
  { pattern: /^(enable|turn on) particles$/i, action: 'enable-particles', description: 'Enable particle effects' },
  { pattern: /^(disable|turn off) particles$/i, action: 'disable-particles', description: 'Disable particle effects' },
  
  // NEW: MCP Commands
  { pattern: /^(show|open) mcp marketplace$/i, action: 'show-mcp-marketplace', description: 'Show MCP marketplace' },
  { pattern: /^(enable|activate) (.+) mcp$/i, action: 'enable-mcp', description: 'Enable MCP server' },
  { pattern: /^(disable|deactivate) (.+) mcp$/i, action: 'disable-mcp', description: 'Disable MCP server' },
];

export function VoiceControl({ onCommand, isLoading = false, className }: VoiceControlProps) {
  const { flags } = useFeatureFlags();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(true);
  const [browserInfo, setBrowserInfo] = useState<string>('');
  const [language, setLanguage] = useState('en-US');
  const [continuous, setContinuous] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [showCommands, setShowCommands] = useState(false);
  const [lastCommand, setLastCommand] = useState<string | null>(null);
  const [showBrowserWarning, setShowBrowserWarning] = useState(false);
  
  const recognitionRef = useRef<any>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  useEffect(() => {
    // Check if feature is enabled
    if (!flags.voiceControl.enabled) {
      setIsSupported(false);
      return;
    }

    // Detect environment
    const userAgent = navigator.userAgent.toLowerCase();
    const isTauri = (window as any).__TAURI__ !== undefined;
    
    let browser = 'Unknown browser';
    if (isTauri) {
      // In Tauri, check the underlying webview
      if (userAgent.includes('safari') || userAgent.includes('webkit')) {
        browser = 'Tauri (WebKit/Safari)';
      } else if (userAgent.includes('chrome')) {
        browser = 'Tauri (Chromium)';
      } else {
        browser = 'Tauri';
      }
    } else if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      browser = 'Google Chrome';
    } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
      browser = 'Safari';
    } else if (userAgent.includes('firefox')) {
      browser = 'Firefox';
    } else if (userAgent.includes('edg')) {
      browser = 'Microsoft Edge';
    }
    setBrowserInfo(browser);

    // Check if Web Speech API is supported
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      setIsSupported(false);
      setShowBrowserWarning(flags.voiceControl.showUnsupportedWarning);
      console.error('Web Speech API not available. Browser:', browser, 'User Agent:', userAgent);
      return;
    }

    // Initialize speech recognition
    const recognition = new SpeechRecognition();
    recognition.continuous = continuous;
    recognition.interimResults = true;
    recognition.lang = language;

    recognition.onstart = () => {
      setIsListening(true);
      setError(null);
      playSound('start');
    };

    recognition.onresult = (event: any) => {
      let interimTranscript = '';
      let finalTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript + ' ';
        } else {
          interimTranscript += transcript;
        }
      }

      setInterimTranscript(interimTranscript);
      
      if (finalTranscript) {
        const trimmedTranscript = finalTranscript.trim();
        setTranscript(trimmedTranscript);
        processCommand(trimmedTranscript);
      }
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event);
      let errorMessage = `Error: ${event.error}`;
      
      // Provide more detailed error messages
      switch(event.error) {
        case 'not-allowed':
          errorMessage = 'Microphone permission denied. Please allow microphone access.';
          break;
        case 'no-speech':
          errorMessage = 'No speech detected. Please try again.';
          break;
        case 'audio-capture':
          errorMessage = 'No microphone found. Please check your audio input device.';
          break;
        case 'network':
          errorMessage = 'Network error. Speech recognition requires an internet connection.';
          break;
        case 'aborted':
          errorMessage = 'Speech recognition aborted.';
          break;
        case 'language-not-supported':
          errorMessage = `Language ${language} is not supported.`;
          break;
        case 'service-not-allowed':
          errorMessage = 'Speech recognition service is not available in this context. This might be due to browser restrictions.';
          break;
      }
      
      setError(errorMessage);
      setIsListening(false);
      playSound('error');
    };

    recognition.onend = () => {
      setIsListening(false);
      if (continuous && !error) {
        // Restart if continuous mode is enabled
        setTimeout(() => {
          if (continuous) {
            recognition.start();
          }
        }, 100);
      } else {
        playSound('end');
      }
    };

    recognitionRef.current = recognition;

    // Initialize audio context for sound effects
    if (soundEnabled) {
      audioContextRef.current = new AudioContext();
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [language, continuous, soundEnabled, flags]);

  const playSound = useCallback((type: 'start' | 'end' | 'success' | 'error') => {
    if (!soundEnabled || !audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);
    
    gainNode.gain.setValueAtTime(0.1, audioContextRef.current.currentTime);
    
    switch (type) {
      case 'start':
        oscillator.frequency.setValueAtTime(800, audioContextRef.current.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(1200, audioContextRef.current.currentTime + 0.1);
        break;
      case 'end':
        oscillator.frequency.setValueAtTime(1200, audioContextRef.current.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, audioContextRef.current.currentTime + 0.1);
        break;
      case 'success':
        oscillator.frequency.setValueAtTime(1000, audioContextRef.current.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(1500, audioContextRef.current.currentTime + 0.1);
        break;
      case 'error':
        oscillator.frequency.setValueAtTime(400, audioContextRef.current.currentTime);
        break;
    }
    
    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + 0.1);
  }, [soundEnabled]);

  const processCommand = useCallback((text: string) => {
    for (const command of VOICE_COMMANDS) {
      const match = text.match(command.pattern);
      if (match) {
        setLastCommand(command.description);
        playSound('success');
        
        // Handle different command types
        if (command.action === 'send-prompt' && match[2]) {
          onCommand(`${command.action}:${match[2]}`);
        } else if (command.action === 'enable-mcp' && match[2]) {
          onCommand(`${command.action}:${match[2]}`);
        } else if (command.action === 'disable-mcp' && match[2]) {
          onCommand(`${command.action}:${match[2]}`);
        } else {
          onCommand(command.action);
        }
        
        // Clear transcript after processing
        setTimeout(() => {
          setTranscript('');
          setInterimTranscript('');
        }, 1000);
        
        return;
      }
    }
    
    // No command matched
    setError(`Command not recognized: "${text}"`);
    playSound('error');
    
    // Clear error after a few seconds
    setTimeout(() => {
      setError(null);
    }, 3000);
  }, [onCommand, playSound]);

  const toggleListening = useCallback(() => {
    if (!recognitionRef.current) return;

    if (isListening) {
      recognitionRef.current.stop();
    } else {
      setTranscript('');
      setInterimTranscript('');
      setError(null);
      recognitionRef.current.start();
    }
  }, [isListening]);

  if (!flags.voiceControl.enabled) {
    return null; // Feature disabled
  }

  if (!isSupported) {
    return (
      <div className={className}>
        <Badge 
          variant="secondary" 
          className="gap-1 cursor-pointer"
          onClick={() => setShowBrowserWarning(!showBrowserWarning)}
        >
          <AlertCircle className="w-3 h-3" />
          Voice not supported
        </Badge>
        
        <AnimatePresence>
          {showBrowserWarning && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full mt-2 left-0 right-0 z-50 max-w-md"
            >
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Voice Control Not Supported</AlertTitle>
                <AlertDescription className="mt-2">
                  <p className="mb-2">
                    {browserInfo.includes('Tauri') ? (
                      <>Voice control is not available in the Tauri app on your system. The Web Speech API is not supported by the current WebView.</>
                    ) : (
                      <>Your browser ({browserInfo}) doesn't support the Web Speech API required for voice control.</>
                    )}
                  </p>
                  {browserInfo.includes('Tauri') ? (
                    <>
                      <p className="text-sm mt-2">
                        <strong>Possible solutions:</strong>
                      </p>
                      <ul className="text-sm mt-1 ml-4 list-disc">
                        <li>On macOS: Ensure you're running macOS 10.15 or later</li>
                        <li>Check microphone permissions in System Preferences</li>
                        <li>Try using the web version in Chrome or Safari instead</li>
                      </ul>
                    </>
                  ) : (
                    <>
                      <p className="text-sm">
                        <strong>Supported browsers:</strong>
                      </p>
                      <ul className="text-sm mt-1 ml-4 list-disc">
                        <li>Google Chrome (recommended)</li>
                        <li>Microsoft Edge</li>
                        <li>Safari (macOS/iOS)</li>
                      </ul>
                      <p className="text-sm mt-2 text-muted-foreground">
                        Note: Firefox currently does not support the Web Speech API.
                      </p>
                    </>
                  )}
                </AlertDescription>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center gap-2">
        <Button
          variant={isListening ? "default" : "outline"}
          size="sm"
          onClick={toggleListening}
          disabled={isLoading}
          className="relative"
        >
          {isListening ? (
            <>
              <Mic className="w-4 h-4 mr-2" />
              <span className="animate-pulse">Listening...</span>
              <motion.div
                className="absolute -inset-0.5 bg-primary/20 rounded-md"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              />
            </>
          ) : (
            <>
              <MicOff className="w-4 h-4 mr-2" />
              Voice Control
            </>
          )}
        </Button>
        
        <Popover
          trigger={
            <Button variant="ghost" size="icon">
              <Settings2 className="w-4 h-4" />
            </Button>
          }
          content={
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Voice Settings</h4>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="continuous">Continuous listening</Label>
                  <Switch
                    id="continuous"
                    checked={continuous}
                    onCheckedChange={setContinuous}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="sound">Sound effects</Label>
                  <Switch
                    id="sound"
                    checked={soundEnabled}
                    onCheckedChange={setSoundEnabled}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="language">Language</Label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger id="language" className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en-US">English (US)</SelectItem>
                    <SelectItem value="en-GB">English (UK)</SelectItem>
                    <SelectItem value="es-ES">Spanish</SelectItem>
                    <SelectItem value="fr-FR">French</SelectItem>
                    <SelectItem value="de-DE">German</SelectItem>
                    <SelectItem value="it-IT">Italian</SelectItem>
                    <SelectItem value="pt-BR">Portuguese (Brazil)</SelectItem>
                    <SelectItem value="zh-CN">Chinese (Simplified)</SelectItem>
                    <SelectItem value="ja-JP">Japanese</SelectItem>
                    <SelectItem value="ko-KR">Korean</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCommands(!showCommands)}
                  className="w-full"
                >
                  {showCommands ? 'Hide' : 'Show'} Available Commands
                </Button>
              </div>
            </div>
          }
        />
      </div>
      
      {/* Transcript Display */}
      <AnimatePresence>
        {(transcript || interimTranscript) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full mt-2 left-0 right-0"
          >
            <Card>
              <CardContent className="p-3">
                <div className="flex items-start gap-2">
                  <Volume2 className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm">
                      {transcript && <span className="font-medium">{transcript}</span>}
                      {interimTranscript && <span className="text-muted-foreground"> {interimTranscript}</span>}
                    </p>
                    {lastCommand && (
                      <div className="flex items-center gap-1 mt-1">
                        <CheckCircle2 className="w-3 h-3 text-green-500" />
                        <span className="text-xs text-green-600">Command: {lastCommand}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full mt-2 left-0 right-0"
          >
            <Card className="border-destructive/50">
              <CardContent className="p-3">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-destructive" />
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Commands List */}
      <AnimatePresence>
        {showCommands && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full mt-12 left-0 right-0 z-50"
          >
            <Card>
              <CardContent className="p-4">
                <h4 className="text-sm font-medium mb-2">Available Voice Commands</h4>
                <div className="space-y-1">
                  {VOICE_COMMANDS.map((cmd, idx) => (
                    <div key={idx} className="flex items-center gap-2 text-xs">
                      <Zap className="w-3 h-3 text-primary" />
                      <span className="text-muted-foreground">{cmd.description}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}