import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useFeatureFlags } from '@/lib/featureFlags';
import { SmartFeatureSelection } from '@/components/SmartFeatureSelection';
import { 
  Settings, 
  Save, 
  RotateCcw,
  Info,
  Globe,
  Mic,
  GitBranch,
  Gauge,
  FileCode,
  Puzzle,
  Bot,
  ShoppingBag,
  Target,
  Sliders
} from 'lucide-react';

interface FeatureFlagsSettingsProps {
  className?: string;
}

export function FeatureFlagsSettings({ className }: FeatureFlagsSettingsProps) {
  const { flags, updateFlags } = useFeatureFlags();
  const [localFlags, setLocalFlags] = useState(flags);
  const [hasChanges, setHasChanges] = useState(false);
  const [showSmartSelection, setShowSmartSelection] = useState(false);

  const handleToggleFeature = (feature: keyof typeof flags, enabled: boolean) => {
    setLocalFlags(prev => ({
      ...prev,
      [feature]: {
        ...prev[feature],
        enabled
      }
    }));
    setHasChanges(true);
  };

  const handleCollaborationSettingChange = (key: string, value: any) => {
    setLocalFlags(prev => ({
      ...prev,
      collaboration: {
        ...prev.collaboration,
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    updateFlags(localFlags);
    setHasChanges(false);
  };

  const handleReset = () => {
    setLocalFlags(flags);
    setHasChanges(false);
  };

  const features = [
    {
      key: 'collaboration',
      name: 'Collaborative Sessions',
      description: 'Real-time collaboration with team members',
      icon: Globe,
      hasSettings: true
    },
    {
      key: 'voiceControl',
      name: 'Voice Control',
      description: 'Control the app with voice commands',
      icon: Mic,
      hasSettings: false
    },
    {
      key: 'codeFlow',
      name: 'Code Flow Visualization',
      description: 'Visual representation of code execution flow',
      icon: GitBranch,
      hasSettings: false
    },
    {
      key: 'performanceProfiler',
      name: 'Performance Profiler',
      description: 'Track API usage and performance metrics',
      icon: Gauge,
      hasSettings: false
    },
    {
      key: 'smartTemplates',
      name: 'Smart Templates',
      description: 'AI-powered code templates and snippets',
      icon: FileCode,
      hasSettings: false
    },
    {
      key: 'pluginSystem',
      name: 'Plugin System',
      description: 'Extend functionality with custom plugins',
      icon: Puzzle,
      hasSettings: false
    },
    {
      key: 'agentSystem',
      name: 'Agent System',
      description: 'Specialized AI agents for different tasks',
      icon: Bot,
      hasSettings: false
    },
    {
      key: 'mcpMarketplace',
      name: 'MCP Marketplace',
      description: 'Browse and install MCP servers',
      icon: ShoppingBag,
      hasSettings: false
    },
    {
      key: 'smartFeatureSelection',
      name: 'Smart Feature Selection',
      description: 'AI-powered feature visibility with focus mode',
      icon: Target,
      hasSettings: true
    }
  ];

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Feature Flags
          </CardTitle>
          <CardDescription>
            Enable or disable experimental features. Some features may require backend services.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Info Alert */}
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>About Feature Flags</AlertTitle>
            <AlertDescription>
              Feature flags allow you to control which features are active. Some features may be experimental
              or require additional setup. Disabled features won't appear in the UI.
            </AlertDescription>
          </Alert>

          {/* Features List */}
          <div className="space-y-4">
            {features.map((feature) => {
              const Icon = feature.icon;
              const featureData = localFlags[feature.key as keyof typeof localFlags];
              
              // Skip if feature doesn't exist in flags
              if (!featureData) {
                return null;
              }
              
              return (
                <div key={feature.key} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Icon className="w-5 h-5 text-muted-foreground" />
                      <div>
                        <Label htmlFor={feature.key} className="text-base font-medium">
                          {feature.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                    <Switch
                      id={feature.key}
                      checked={featureData.enabled}
                      onCheckedChange={(checked) => handleToggleFeature(feature.key as keyof typeof flags, checked)}
                    />
                  </div>

                  {/* Collaboration-specific settings */}
                  {feature.key === 'collaboration' && featureData.enabled && (
                    <div className="ml-8 p-4 bg-muted/50 rounded-lg space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="useLocalBackend" className="text-sm">
                          Use Local Backend
                        </Label>
                        <Switch
                          id="useLocalBackend"
                          checked={localFlags.collaboration.useLocalBackend}
                          onCheckedChange={(checked) => handleCollaborationSettingChange('useLocalBackend', checked)}
                        />
                      </div>
                      
                      {localFlags.collaboration.useLocalBackend && (
                        <>
                          <div className="space-y-2">
                            <Label htmlFor="localWsUrl" className="text-sm">
                              Local WebSocket URL
                            </Label>
                            <Input
                              id="localWsUrl"
                              value={localFlags.collaboration.localWsUrl}
                              onChange={(e) => handleCollaborationSettingChange('localWsUrl', e.target.value)}
                              placeholder="ws://localhost:8080/ws"
                              className="font-mono text-sm"
                            />
                          </div>
                          
                          <div className="space-y-2">
                            <Label htmlFor="localShareUrl" className="text-sm">
                              Local Share URL
                            </Label>
                            <Input
                              id="localShareUrl"
                              value={localFlags.collaboration.localShareUrl}
                              onChange={(e) => handleCollaborationSettingChange('localShareUrl', e.target.value)}
                              placeholder="http://localhost:3000/join"
                              className="font-mono text-sm"
                            />
                          </div>
                        </>
                      )}
                      
                      <div className="pt-2">
                        <Badge variant="secondary" className="text-xs">
                          {localFlags.collaboration.useLocalBackend ? 'Local Development Mode' : 'Production Mode'}
                        </Badge>
                      </div>
                    </div>
                  )}

                  {/* Smart Feature Selection settings */}
                  {feature.key === 'smartFeatureSelection' && featureData.enabled && (
                    <div className="ml-8 p-4 bg-muted/50 rounded-lg">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowSmartSelection(true)}
                        className="w-full"
                      >
                        <Sliders className="w-4 h-4 mr-2" />
                        Configure Smart Feature Selection
                      </Button>
                    </div>
                  )}

                  <Separator />
                </div>
              );
            })}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={!hasChanges}
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            <Button
              onClick={handleSave}
              disabled={!hasChanges}
            >
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Smart Feature Selection Dialog */}
      <Dialog open={showSmartSelection} onOpenChange={setShowSmartSelection}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Configure Smart Feature Selection</DialogTitle>
          </DialogHeader>
          <SmartFeatureSelection 
            currentView="settings"
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}