/**
 * Advanced Parallel Agents Dashboard
 * 
 * This component provides a comprehensive, enterprise-grade dashboard for monitoring
 * and controlling the parallel agents system with:
 * - Real-time performance metrics and analytics
 * - Intelligent task distribution controls
 * - Advanced optimization recommendations
 * - Interactive system health monitoring
 * - Predictive analytics and forecasting
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Bot,
  Brain,
  CheckCircle,
  Clock,
  Cpu,
  Eye,
  Gauge,
  GitBranch,
  HelpCircle,
  Layers,
  LineChart,
  MemoryStick,
  Pause,
  Play,
  RefreshCw,
  Settings,
  Sparkles,
  Target,
  TrendingUp,
  Users,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

// Import our advanced parallel agents system
import { advancedOrchestrator, OrchestrationMetrics, OptimizationStrategy } from '@/lib/parallel-agents/AdvancedParallelOrchestrator';
import { intelligentDistributor, DistributionMetrics, AgentInfo } from '@/lib/parallel-agents/IntelligentTaskDistributor';
import { realTimeAnalytics, PerformanceSnapshot, SystemAlert, SystemRecommendation } from '@/lib/parallel-agents/RealTimeAnalytics';

// Types
interface DashboardState {
  isRunning: boolean;
  currentMetrics: OrchestrationMetrics | null;
  distributionMetrics: DistributionMetrics | null;
  performanceSnapshot: PerformanceSnapshot | null;
  activeAlerts: SystemAlert[];
  recommendations: SystemRecommendation[];
  agents: AgentInfo[];
  optimizationStrategies: OptimizationStrategy[];
}

interface MetricCard {
  title: string;
  value: string | number;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: string;
  description?: string;
}

interface ChartData {
  timestamp: string;
  throughput: number;
  latency: number;
  successRate: number;
  cpuUsage: number;
  memoryUsage: number;
}

/**
 * Advanced Parallel Agents Dashboard Component
 */
export const AdvancedParallelAgentsDashboard: React.FC = () => {
  const [state, setState] = useState<DashboardState>({
    isRunning: false,
    currentMetrics: null,
    distributionMetrics: null,
    performanceSnapshot: null,
    activeAlerts: [],
    recommendations: [],
    agents: [],
    optimizationStrategies: []
  });
  
  const [selectedTab, setSelectedTab] = useState('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [selectedStrategy, setSelectedStrategy] = useState('ml-optimized');
  const [chartData, setChartData] = useState<ChartData[]>([]);
  
  // Initialize and start the system
  useEffect(() => {
    const initializeSystem = async () => {
      try {
        // Start the orchestrator
        await advancedOrchestrator.start();
        
        // Start analytics
        realTimeAnalytics.start();
        
        setState(prev => ({ ...prev, isRunning: true }));
        
        console.log('🚀 Advanced Parallel Agents System initialized');
      } catch (error) {
        console.error('Failed to initialize system:', error);
      }
    };
    
    initializeSystem();
    
    // Cleanup on unmount
    return () => {
      advancedOrchestrator.stop();
      realTimeAnalytics.stop();
    };
  }, []);
  
  // Auto-refresh data
  useEffect(() => {
    if (!autoRefresh || !state.isRunning) return;
    
    const interval = setInterval(() => {
      refreshData();
    }, refreshInterval);
    
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, state.isRunning]);
  
  // Event listeners for real-time updates
  useEffect(() => {
    const handleMetricsUpdate = (metrics: OrchestrationMetrics) => {
      setState(prev => ({ ...prev, currentMetrics: metrics }));
    };
    
    const handleSnapshotUpdate = (snapshot: PerformanceSnapshot) => {
      setState(prev => ({ ...prev, performanceSnapshot: snapshot }));
      updateChartData(snapshot);
    };
    
    advancedOrchestrator.on('metricsUpdated', handleMetricsUpdate);
    realTimeAnalytics.on('snapshotCollected', handleSnapshotUpdate);
    
    return () => {
      advancedOrchestrator.off('metricsUpdated', handleMetricsUpdate);
      realTimeAnalytics.off('snapshotCollected', handleSnapshotUpdate);
    };
  }, []);
  
  // Refresh data manually
  const refreshData = useCallback(async () => {
    try {
      const [metrics, distributionMetrics, snapshot, alerts, recommendations, optimizations] = await Promise.all([
        advancedOrchestrator.getMetrics(),
        intelligentDistributor.getCurrentMetrics(),
        realTimeAnalytics.getCurrentSnapshot(),
        realTimeAnalytics.getActiveAlerts(),
        realTimeAnalytics.getRecommendations(),
        advancedOrchestrator.getOptimizationRecommendations()
      ]);
      
      setState(prev => ({
        ...prev,
        currentMetrics: metrics,
        distributionMetrics,
        performanceSnapshot: snapshot,
        activeAlerts: alerts,
        recommendations,
        optimizationStrategies: optimizations
      }));
    } catch (error) {
      console.error('Failed to refresh data:', error);
    }
  }, []);
  
  // Update chart data
  const updateChartData = useCallback((snapshot: PerformanceSnapshot) => {
    const newDataPoint: ChartData = {
      timestamp: snapshot.timestamp.toISOString(),
      throughput: snapshot.metrics.throughput.current,
      latency: snapshot.metrics.latency.current,
      successRate: snapshot.metrics.successRate.current,
      cpuUsage: snapshot.metrics.resources.cpu.current,
      memoryUsage: snapshot.metrics.resources.memory.current
    };
    
    setChartData(prev => {
      const updated = [...prev, newDataPoint];
      // Keep only last 50 data points
      return updated.slice(-50);
    });
  }, []);
  
  // Control functions
  const handleStartStop = async () => {
    try {
      if (state.isRunning) {
        await advancedOrchestrator.stop();
        realTimeAnalytics.stop();
        setState(prev => ({ ...prev, isRunning: false }));
      } else {
        await advancedOrchestrator.start();
        realTimeAnalytics.start();
        setState(prev => ({ ...prev, isRunning: true }));
      }
    } catch (error) {
      console.error('Failed to start/stop system:', error);
    }
  };
  
  const handleOptimize = async () => {
    try {
      await intelligentDistributor.optimizeStrategy();
      await refreshData();
    } catch (error) {
      console.error('Failed to optimize:', error);
    }
  };
  
  const handleApplyOptimization = async (strategy: OptimizationStrategy) => {
    try {
      await advancedOrchestrator.applyOptimization(strategy);
      await refreshData();
    } catch (error) {
      console.error('Failed to apply optimization:', error);
    }
  };
  
  const handleAcknowledgeAlert = async (alertId: string) => {
    realTimeAnalytics.acknowledgeAlert(alertId);
    await refreshData();
  };
  
  const handleDismissRecommendation = async (recommendationId: string) => {
    realTimeAnalytics.dismissRecommendation(recommendationId);
    await refreshData();
  };
  
  // Computed values
  const metricCards = useMemo((): MetricCard[] => {
    if (!state.currentMetrics) return [];
    
    const { currentMetrics } = state;
    
    return [
      {
        title: 'Throughput',
        value: `${currentMetrics.throughput.current.toFixed(1)} TPM`,
        change: ((currentMetrics.throughput.current - currentMetrics.throughput.average) / currentMetrics.throughput.average) * 100,
        trend: currentMetrics.throughput.current > currentMetrics.throughput.average ? 'up' : 'down',
        icon: <TrendingUp className="h-4 w-4" />,
        color: 'text-blue-600',
        description: 'Tasks per minute'
      },
      {
        title: 'Latency',
        value: `${currentMetrics.latency.current.toFixed(0)}ms`,
        change: ((currentMetrics.latency.current - currentMetrics.latency.average) / currentMetrics.latency.average) * 100,
        trend: currentMetrics.latency.current < currentMetrics.latency.average ? 'up' : 'down',
        icon: <Clock className="h-4 w-4" />,
        color: 'text-green-600',
        description: 'Average response time'
      },
      {
        title: 'Success Rate',
        value: `${currentMetrics.successRate.current.toFixed(1)}%`,
        change: currentMetrics.successRate.current - currentMetrics.successRate.average,
        trend: currentMetrics.successRate.current > currentMetrics.successRate.average ? 'up' : 'down',
        icon: <CheckCircle className="h-4 w-4" />,
        color: 'text-emerald-600',
        description: 'Task completion rate'
      },
      {
        title: 'Active Agents',
        value: currentMetrics.agents.active,
        change: 0,
        trend: 'stable',
        icon: <Bot className="h-4 w-4" />,
        color: 'text-purple-600',
        description: 'Currently processing tasks'
      },
      {
        title: 'CPU Usage',
        value: `${currentMetrics.resources.cpu.current.toFixed(1)}%`,
        change: 0,
        trend: 'stable',
        icon: <Cpu className="h-4 w-4" />,
        color: 'text-orange-600',
        description: 'System CPU utilization'
      },
      {
        title: 'Memory Usage',
        value: `${currentMetrics.resources.memory.current.toFixed(1)}%`,
        change: 0,
        trend: 'stable',
        icon: <MemoryStick className="h-4 w-4" />,
        color: 'text-red-600',
        description: 'System memory utilization'
      }
    ];
  }, [state.currentMetrics]);
  
  const systemHealthColor = useMemo(() => {
    if (!state.performanceSnapshot) return 'text-gray-500';
    
    const health = state.performanceSnapshot.systemHealth.overall;
    switch (health) {
      case 'excellent': return 'text-green-500';
      case 'good': return 'text-blue-500';
      case 'fair': return 'text-yellow-500';
      case 'poor': return 'text-orange-500';
      case 'critical': return 'text-red-500';
      default: return 'text-gray-500';
    }
  }, [state.performanceSnapshot]);
  
  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        {/* Header */}
        <div className="border-b bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
          <div className="container mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Brain className="h-8 w-8 text-blue-600" />
                  <div>
                    <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                      Advanced Parallel Agents
                    </h1>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      Enterprise-Grade AI Orchestration System
                    </p>
                  </div>
                </div>
                
                {/* System Status */}
                <div className="flex items-center space-x-2">
                  <div className={`h-3 w-3 rounded-full ${state.isRunning ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                  <span className="text-sm font-medium">
                    {state.isRunning ? 'Running' : 'Stopped'}
                  </span>
                  {state.performanceSnapshot && (
                    <Badge variant="outline" className={systemHealthColor}>
                      {state.performanceSnapshot.systemHealth.overall}
                    </Badge>
                  )}
                </div>
              </div>
              
              {/* Controls */}
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={autoRefresh}
                    onCheckedChange={setAutoRefresh}
                    id="auto-refresh"
                  />
                  <label htmlFor="auto-refresh" className="text-sm font-medium">
                    Auto Refresh
                  </label>
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshData}
                  disabled={!state.isRunning}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOptimize}
                  disabled={!state.isRunning}
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  Optimize
                </Button>
                
                <Button
                  variant={state.isRunning ? "destructive" : "default"}
                  size="sm"
                  onClick={handleStartStop}
                >
                  {state.isRunning ? (
                    <><Pause className="h-4 w-4 mr-2" /> Stop</>
                  ) : (
                    <><Play className="h-4 w-4 mr-2" /> Start</>
                  )}
                </Button>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Advanced Configuration</DialogTitle>
                      <DialogDescription>
                        Configure advanced settings for the parallel agents system
                      </DialogDescription>
                    </DialogHeader>
                    <AdvancedConfigurationPanel
                      refreshInterval={refreshInterval}
                      onRefreshIntervalChange={setRefreshInterval}
                      selectedStrategy={selectedStrategy}
                      onStrategyChange={setSelectedStrategy}
                    />
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </div>
        
        {/* Main Content */}
        <div className="container mx-auto px-6 py-6">
          {/* Alerts */}
          {state.activeAlerts.length > 0 && (
            <div className="mb-6">
              <AlertsPanel
                alerts={state.activeAlerts}
                onAcknowledge={handleAcknowledgeAlert}
              />
            </div>
          )}
          
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
            {metricCards.map((card, index) => (
              <MetricCard key={index} {...card} />
            ))}
          </div>
          
          {/* Main Dashboard */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="agents">Agents</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="optimization">Optimization</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-6">
              <OverviewPanel
                metrics={state.currentMetrics}
                distributionMetrics={state.distributionMetrics}
                chartData={chartData}
              />
            </TabsContent>
            
            <TabsContent value="agents" className="space-y-6">
              <AgentsPanel agents={state.agents} />
            </TabsContent>
            
            <TabsContent value="tasks" className="space-y-6">
              <TasksPanel metrics={state.currentMetrics} />
            </TabsContent>
            
            <TabsContent value="performance" className="space-y-6">
              <PerformancePanel
                snapshot={state.performanceSnapshot}
                chartData={chartData}
              />
            </TabsContent>
            
            <TabsContent value="optimization" className="space-y-6">
              <OptimizationPanel
                strategies={state.optimizationStrategies}
                recommendations={state.recommendations}
                onApplyOptimization={handleApplyOptimization}
                onDismissRecommendation={handleDismissRecommendation}
              />
            </TabsContent>
            
            <TabsContent value="analytics" className="space-y-6">
              <AnalyticsPanel
                snapshot={state.performanceSnapshot}
                chartData={chartData}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </TooltipProvider>
  );
};

// Metric Card Component
interface MetricCardProps extends MetricCard {}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  trend,
  icon,
  color,
  description
}) => {
  const getTrendIcon = () => {
    if (!change || Math.abs(change) < 0.1) return null;
    return trend === 'up' ? (
      <TrendingUp className="h-3 w-3 text-green-500" />
    ) : (
      <TrendingUp className="h-3 w-3 text-red-500 rotate-180" />
    );
  };
  
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className={`p-2 rounded-lg bg-slate-100 dark:bg-slate-800 ${color}`}>
            {icon}
          </div>
          {getTrendIcon()}
        </div>
        
        <div className="mt-3">
          <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">
            {value}
          </p>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            {title}
          </p>
          {description && (
            <p className="text-xs text-slate-500 dark:text-slate-500 mt-1">
              {description}
            </p>
          )}
          {change !== undefined && Math.abs(change) >= 0.1 && (
            <p className={`text-xs mt-1 ${
              change > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {change > 0 ? '+' : ''}{change.toFixed(1)}%
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Alerts Panel Component
interface AlertsPanelProps {
  alerts: SystemAlert[];
  onAcknowledge: (alertId: string) => void;
}

const AlertsPanel: React.FC<AlertsPanelProps> = ({ alerts, onAcknowledge }) => {
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'border-red-500 bg-red-50 dark:bg-red-950';
      case 'high': return 'border-orange-500 bg-orange-50 dark:bg-orange-950';
      case 'medium': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950';
      default: return 'border-blue-500 bg-blue-50 dark:bg-blue-950';
    }
  };
  
  return (
    <div className="space-y-3">
      {alerts.map((alert) => (
        <Alert key={alert.id} className={getSeverityColor(alert.severity)}>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle className="flex items-center justify-between">
            <span>{alert.message}</span>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{alert.severity}</Badge>
              {!alert.acknowledged && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onAcknowledge(alert.id)}
                >
                  Acknowledge
                </Button>
              )}
            </div>
          </AlertTitle>
          <AlertDescription>
            {alert.timestamp.toLocaleString()} • Source: {alert.source}
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
};

// Overview Panel Component
interface OverviewPanelProps {
  metrics: OrchestrationMetrics | null;
  distributionMetrics: DistributionMetrics | null;
  chartData: ChartData[];
}

const OverviewPanel: React.FC<OverviewPanelProps> = ({
  metrics,
  chartData
}) => {
  if (!metrics) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-slate-500">
            No metrics available. Start the system to begin monitoring.
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* System Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Gauge className="h-5 w-5" />
            <span>System Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-slate-600 dark:text-slate-400">Total Agents</p>
              <p className="text-2xl font-bold">{metrics.agents.total}</p>
            </div>
            <div>
              <p className="text-sm text-slate-600 dark:text-slate-400">Active Tasks</p>
              <p className="text-2xl font-bold">{metrics.tasks.processing}</p>
            </div>
            <div>
              <p className="text-sm text-slate-600 dark:text-slate-400">Queue Length</p>
              <p className="text-2xl font-bold">{metrics.tasks.queued}</p>
            </div>
            <div>
              <p className="text-sm text-slate-600 dark:text-slate-400">Completed</p>
              <p className="text-2xl font-bold">{metrics.tasks.completed}</p>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-3">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>CPU Usage</span>
                <span>{metrics.resources.cpu.current.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.resources.cpu.current} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Memory Usage</span>
                <span>{metrics.resources.memory.current.toFixed(1)}%</span>
              </div>
              <Progress value={metrics.resources.memory.current} className="h-2" />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Token Usage</span>
                <span>{((metrics.resources.tokens.used / (metrics.resources.tokens.used + metrics.resources.tokens.remaining)) * 100).toFixed(1)}%</span>
              </div>
              <Progress 
                value={(metrics.resources.tokens.used / (metrics.resources.tokens.used + metrics.resources.tokens.remaining)) * 100} 
                className="h-2" 
              />
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <LineChart className="h-5 w-5" />
            <span>Performance Trends</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-slate-500">
            {chartData.length > 0 ? (
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Chart visualization would be rendered here</p>
                <p className="text-xs">({chartData.length} data points)</p>
              </div>
            ) : (
              <div className="text-center">
                <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>Collecting performance data...</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Agents Panel Component
interface AgentsPanelProps {
  agents: AgentInfo[];
}

const AgentsPanel: React.FC<AgentsPanelProps> = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5" />
          <span>Agent Management</span>
        </CardTitle>
        <CardDescription>
          Monitor and manage individual agents in the system
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center text-slate-500 py-8">
          <Bot className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>Agent management interface</p>
          <p className="text-sm">Individual agent monitoring and control</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Tasks Panel Component
interface TasksPanelProps {
  metrics: OrchestrationMetrics | null;
}

const TasksPanel: React.FC<TasksPanelProps> = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Layers className="h-5 w-5" />
          <span>Task Management</span>
        </CardTitle>
        <CardDescription>
          Monitor task queues, execution, and performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center text-slate-500 py-8">
          <GitBranch className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>Task management interface</p>
          <p className="text-sm">Queue monitoring and task control</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Performance Panel Component
interface PerformancePanelProps {
  snapshot: PerformanceSnapshot | null;
  chartData: ChartData[];
}

const PerformancePanel: React.FC<PerformancePanelProps> = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Activity className="h-5 w-5" />
          <span>Performance Analytics</span>
        </CardTitle>
        <CardDescription>
          Detailed performance metrics and system health
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center text-slate-500 py-8">
          <Activity className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>Performance analytics dashboard</p>
          <p className="text-sm">Real-time metrics and historical analysis</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Optimization Panel Component
interface OptimizationPanelProps {
  strategies: OptimizationStrategy[];
  recommendations: SystemRecommendation[];
  onApplyOptimization: (strategy: OptimizationStrategy) => void;
  onDismissRecommendation: (id: string) => void;
}

const OptimizationPanel: React.FC<OptimizationPanelProps> = ({
  strategies,
  recommendations,
  onApplyOptimization,
  onDismissRecommendation
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Optimization Strategies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Optimization Strategies</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {strategies.length > 0 ? (
              strategies.map((strategy, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{strategy.name}</h4>
                    <Badge variant={strategy.impact === 'high' ? 'default' : 'secondary'}>
                      {strategy.impact} impact
                    </Badge>
                  </div>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-3">
                    {strategy.description}
                  </p>
                  <Button
                    size="sm"
                    onClick={() => onApplyOptimization(strategy)}
                    className="w-full"
                  >
                    Apply Optimization
                  </Button>
                </div>
              ))
            ) : (
              <div className="text-center text-slate-500 py-4">
                <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No optimization strategies available</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <HelpCircle className="h-5 w-5" />
            <span>Recommendations</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recommendations.length > 0 ? (
              recommendations.map((rec) => (
                <div key={rec.id} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{rec.title}</h4>
                    <div className="flex items-center space-x-2">
                      <Badge variant={rec.priority === 'high' ? 'destructive' : 'secondary'}>
                        {rec.priority}
                      </Badge>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onDismissRecommendation(rec.id)}
                      >
                        Dismiss
                      </Button>
                    </div>
                  </div>
                  <p className="text-sm text-slate-600 dark:text-slate-400 mb-2">
                    {rec.description}
                  </p>
                  <p className="text-xs text-slate-500">
                    Expected benefit: {rec.expectedBenefit}% • Effort: {rec.effort}
                  </p>
                </div>
              ))
            ) : (
              <div className="text-center text-slate-500 py-4">
                <HelpCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No recommendations available</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Analytics Panel Component
interface AnalyticsPanelProps {
  snapshot: PerformanceSnapshot | null;
  chartData: ChartData[];
}

const AnalyticsPanel: React.FC<AnalyticsPanelProps> = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Eye className="h-5 w-5" />
          <span>Advanced Analytics</span>
        </CardTitle>
        <CardDescription>
          Deep insights and predictive analytics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center text-slate-500 py-8">
          <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
          <p>Advanced analytics dashboard</p>
          <p className="text-sm">Predictive insights and trend analysis</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Advanced Configuration Panel Component
interface AdvancedConfigurationPanelProps {
  refreshInterval: number;
  onRefreshIntervalChange: (interval: number) => void;
  selectedStrategy: string;
  onStrategyChange: (strategy: string) => void;
}

const AdvancedConfigurationPanel: React.FC<AdvancedConfigurationPanelProps> = ({
  refreshInterval,
  onRefreshIntervalChange,
  selectedStrategy,
  onStrategyChange
}) => {
  return (
    <div className="space-y-6">
      <div>
        <label className="text-sm font-medium mb-2 block">
          Refresh Interval: {refreshInterval / 1000}s
        </label>
        <Slider
          value={[refreshInterval]}
          onValueChange={([value]) => onRefreshIntervalChange(value)}
          min={1000}
          max={30000}
          step={1000}
          className="w-full"
        />
      </div>
      
      <div>
        <label className="text-sm font-medium mb-2 block">
          Distribution Strategy
        </label>
        <Select value={selectedStrategy} onValueChange={onStrategyChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="round-robin">Round Robin</SelectItem>
            <SelectItem value="load-based">Load Based</SelectItem>
            <SelectItem value="capability-based">Capability Based</SelectItem>
            <SelectItem value="ml-optimized">ML Optimized</SelectItem>
            <SelectItem value="hybrid">Hybrid</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default AdvancedParallelAgentsDashboard;