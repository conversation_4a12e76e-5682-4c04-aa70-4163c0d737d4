import React, { useState, useEffect } from 'react';
import {
  <PERSON>tings,
  Zap,
  Brain,
  Network,
  Code,
  Shield,
  Gauge,
  Bot,
  Globe,
  Database,
  Cloud,
  Package,
  Save,
  RotateCcw,
  ChevronDown,
  ChevronUp,
  Sparkles,
  Eye,
  EyeOff,
  Play,
  Pause,
  X,
  Plus,
  Trash2,
  Copy,
  Download,
  Upload,
  Info,
  CheckCircle,
  AlertCircle,
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { useSuperClaude } from '@/hooks/useSuperClaude';
import { useFeatureFlags } from '@/lib/featureFlags';
import { useFocusMode } from '@/hooks/useFocusMode';
import { api, type MCPServer, type ServerStatus } from '@/lib/api';
import { cn } from '@/lib/utils';

interface SessionCustomizerProps {
  onClose: () => void;
  onApplySettings: (settings: SessionSettings) => void;
  currentSettings?: SessionSettings;
}

interface SessionSettings {
  mode: 'development' | 'analysis' | 'creative' | 'secure' | 'custom';
  mcpServers: string[];
  features: string[];
  performance: {
    tokenOptimization: boolean;
    caching: boolean;
    compression: boolean;
  };
  ui: {
    theme: 'auto' | 'light' | 'dark';
    layout: 'standard' | 'compact' | 'minimal';
    animations: boolean;
  };
  customPresets: SessionPreset[];
}

interface SessionPreset {
  id: string;
  name: string;
  description: string;
  icon: string;
  settings: Partial<SessionSettings>;
}

const DEFAULT_PRESETS: SessionPreset[] = [
  {
    id: 'development',
    name: 'Development Focus',
    description: 'Optimized for coding with essential development tools',
    icon: 'Code',
    settings: {
      mode: 'development',
      mcpServers: ['filesystem', 'git', 'github', 'memory'],
      features: ['codeFlow', 'performanceProfiler', 'smartTemplates'],
      performance: { tokenOptimization: true, caching: true, compression: false },
    }
  },
  {
    id: 'analysis',
    name: 'Data Analysis',
    description: 'Perfect for data analysis and research tasks',
    icon: 'Gauge',
    settings: {
      mode: 'analysis',
      mcpServers: ['brave-search', 'sqlite', 'fetch', 'memory'],
      features: ['performanceProfiler', 'mcpMarketplace'],
      performance: { tokenOptimization: false, caching: true, compression: true },
    }
  },
  {
    id: 'creative',
    name: 'Creative Mode',
    description: 'Enhanced for creative and experimental work',
    icon: 'Sparkles',
    settings: {
      mode: 'creative',
      mcpServers: ['everart', 'memory', 'fetch', 'puppeteer'],
      features: ['voiceControl', 'collaboration', 'smartTemplates'],
      performance: { tokenOptimization: false, caching: false, compression: false },
    }
  },
  {
    id: 'secure',
    name: 'Security Focused',
    description: 'Maximum security with minimal external connections',
    icon: 'Shield',
    settings: {
      mode: 'secure',
      mcpServers: ['filesystem', 'memory'],
      features: [],
      performance: { tokenOptimization: true, caching: false, compression: true },
    }
  }
];

export const SessionCustomizer: React.FC<SessionCustomizerProps> = ({
  onClose,
  onApplySettings,
  currentSettings,
}) => {
  const { settings: superClaudeSettings, updateSettings: updateSuperClaudeSettings } = useSuperClaude();
  const { flags: featureFlags, updateFlags: updateFeatureFlags } = useFeatureFlags();
  const { config: focusMode, updateEssentialFeatures, applyPreset } = useFocusMode();
  
  const [activeTab, setActiveTab] = useState('presets');
  const [sessionSettings, setSessionSettings] = useState<SessionSettings>(
    currentSettings || {
      mode: 'development',
      mcpServers: [],
      features: [],
      performance: { tokenOptimization: true, caching: true, compression: false },
      ui: { theme: 'auto', layout: 'standard', animations: true },
      customPresets: [],
    }
  );
  
  const [availableMCPServers, setAvailableMCPServers] = useState<MCPServer[]>([]);
  const [mcpServerStatuses, setMcpServerStatuses] = useState<Record<string, ServerStatus>>({});
  const [mcpLoading, setMcpLoading] = useState(false);
  const [showCreatePreset, setShowCreatePreset] = useState(false);
  const [newPresetName, setNewPresetName] = useState('');
  const [newPresetDescription, setNewPresetDescription] = useState('');
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  // Load available MCP servers
  useEffect(() => {
    loadMCPServers();
  }, []);

  const loadMCPServers = async () => {
    try {
      setMcpLoading(true);
      const [servers, statuses] = await Promise.all([
        api.mcpList(),
        api.mcpGetServerStatus()
      ]);
      setAvailableMCPServers(servers);
      setMcpServerStatuses(statuses);
    } catch (error) {
      console.error('Failed to load MCP servers:', error);
    } finally {
      setMcpLoading(false);
    }
  };

  const applyPresetSettings = (preset: SessionPreset) => {
    setSessionSettings(prev => ({
      ...prev,
      ...preset.settings,
    }));
    setSelectedPreset(preset.id);
  };

  const handleSaveCustomPreset = () => {
    if (!newPresetName.trim()) return;
    
    const newPreset: SessionPreset = {
      id: `custom-${Date.now()}`,
      name: newPresetName,
      description: newPresetDescription,
      icon: 'Package',
      settings: { ...sessionSettings },
    };
    
    setSessionSettings(prev => ({
      ...prev,
      customPresets: [...prev.customPresets, newPreset],
    }));
    
    setNewPresetName('');
    setNewPresetDescription('');
    setShowCreatePreset(false);
  };

  const handleApplySettings = () => {
    // Apply to SuperClaude settings
    updateSuperClaudeSettings({
      ...superClaudeSettings,
      coreModes: {
        ...superClaudeSettings.coreModes,
        tokenEconomy: sessionSettings.performance.tokenOptimization,
        ultraCompressed: sessionSettings.performance.compression,
      },
    });

    // Apply to feature flags
    const featureUpdates: any = {};
    sessionSettings.features.forEach(feature => {
      if (featureFlags[feature as keyof typeof featureFlags]) {
        featureUpdates[feature] = { enabled: true };
      }
    });
    updateFeatureFlags(featureUpdates);

    // Apply to focus mode
    updateEssentialFeatures(sessionSettings.features);

    onApplySettings(sessionSettings);
    onClose();
  };

  const allPresets = [...DEFAULT_PRESETS, ...sessionSettings.customPresets];

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Session Customizer
          </DialogTitle>
          <DialogDescription>
            Customize your Claude Code session with presets, MCP servers, and advanced features
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="presets" className="flex items-center gap-1">
                <Sparkles className="h-4 w-4" />
                Presets
              </TabsTrigger>
              <TabsTrigger value="mcps" className="flex items-center gap-1">
                <Network className="h-4 w-4" />
                MCPs
              </TabsTrigger>
              <TabsTrigger value="features" className="flex items-center gap-1">
                <Zap className="h-4 w-4" />
                Features
              </TabsTrigger>
              <TabsTrigger value="performance" className="flex items-center gap-1">
                <Gauge className="h-4 w-4" />
                Performance
              </TabsTrigger>
              <TabsTrigger value="advanced" className="flex items-center gap-1">
                <Brain className="h-4 w-4" />
                Advanced
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto mt-4">
              <TabsContent value="presets" className="space-y-4 mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {allPresets.map((preset) => (
                    <Card 
                      key={preset.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedPreset === preset.id ? 'ring-2 ring-primary' : ''
                      }`}
                      onClick={() => applyPresetSettings(preset)}
                    >
                      <CardHeader className="pb-2">
                        <CardTitle className="flex items-center gap-2 text-sm">
                          {preset.icon === 'Code' && <Code className="h-4 w-4" />}
                          {preset.icon === 'Gauge' && <Gauge className="h-4 w-4" />}
                          {preset.icon === 'Sparkles' && <Sparkles className="h-4 w-4" />}
                          {preset.icon === 'Shield' && <Shield className="h-4 w-4" />}
                          {preset.icon === 'Package' && <Package className="h-4 w-4" />}
                          {preset.name}
                          {selectedPreset === preset.id && (
                            <CheckCircle className="h-4 w-4 text-primary ml-auto" />
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-muted-foreground mb-2">{preset.description}</p>
                        <div className="flex flex-wrap gap-1">
                          {preset.settings.mcpServers?.slice(0, 3).map((server) => (
                            <Badge key={server} variant="secondary" className="text-xs">
                              {server}
                            </Badge>
                          ))}
                          {(preset.settings.mcpServers?.length || 0) > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{(preset.settings.mcpServers?.length || 0) - 3}
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Custom Presets</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowCreatePreset(true)}
                    className="flex items-center gap-1"
                  >
                    <Plus className="h-3 w-3" />
                    Create Preset
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="mcps" className="space-y-4 mt-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Available MCP Servers</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadMCPServers}
                    disabled={mcpLoading}
                    className="flex items-center gap-1"
                  >
                    <RotateCcw className={`h-3 w-3 ${mcpLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {availableMCPServers.map((server) => (
                    <Card key={server.name} className="p-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={sessionSettings.mcpServers.includes(server.name)}
                              onCheckedChange={(checked) => {
                                setSessionSettings(prev => ({
                                  ...prev,
                                  mcpServers: checked
                                    ? [...prev.mcpServers, server.name]
                                    : prev.mcpServers.filter(s => s !== server.name)
                                }));
                              }}
                            />
                            <Label className="text-sm font-medium">{server.name}</Label>
                            <Badge
                              variant={mcpServerStatuses[server.name]?.running ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {mcpServerStatuses[server.name]?.running ? 'running' : 'inactive'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="features" className="space-y-4 mt-0">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(featureFlags).map(([key, flag]) => (
                    <Card key={key} className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {key === 'collaboration' && <Globe className="h-4 w-4" />}
                          {key === 'voiceControl' && <Bot className="h-4 w-4" />}
                          {key === 'codeFlow' && <Code className="h-4 w-4" />}
                          {key === 'performanceProfiler' && <Gauge className="h-4 w-4" />}
                          <div>
                            <Label className="text-sm font-medium capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </Label>
                            <p className="text-xs text-muted-foreground">
                              {getFeatureDescription(key)}
                            </p>
                          </div>
                        </div>
                        <Switch
                          checked={sessionSettings.features.includes(key)}
                          onCheckedChange={(checked) => {
                            setSessionSettings(prev => ({
                              ...prev,
                              features: checked
                                ? [...prev.features, key]
                                : prev.features.filter(f => f !== key)
                            }));
                          }}
                        />
                      </div>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="performance" className="space-y-4 mt-0">
                <Card className="p-4">
                  <h3 className="text-sm font-medium mb-3">Performance Optimization</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm">Token Optimization</Label>
                        <p className="text-xs text-muted-foreground">Reduce token usage for cost efficiency</p>
                      </div>
                      <Switch
                        checked={sessionSettings.performance.tokenOptimization}
                        onCheckedChange={(checked) => 
                          setSessionSettings(prev => ({
                            ...prev,
                            performance: { ...prev.performance, tokenOptimization: checked }
                          }))
                        }
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm">Response Caching</Label>
                        <p className="text-xs text-muted-foreground">Cache responses for faster interactions</p>
                      </div>
                      <Switch
                        checked={sessionSettings.performance.caching}
                        onCheckedChange={(checked) => 
                          setSessionSettings(prev => ({
                            ...prev,
                            performance: { ...prev.performance, caching: checked }
                          }))
                        }
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm">Data Compression</Label>
                        <p className="text-xs text-muted-foreground">Compress data for bandwidth efficiency</p>
                      </div>
                      <Switch
                        checked={sessionSettings.performance.compression}
                        onCheckedChange={(checked) => 
                          setSessionSettings(prev => ({
                            ...prev,
                            performance: { ...prev.performance, compression: checked }
                          }))
                        }
                      />
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="text-sm font-medium mb-3">UI Preferences</h3>
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm">Theme</Label>
                      <Select
                        value={sessionSettings.ui.theme}
                        onValueChange={(value: 'auto' | 'light' | 'dark') =>
                          setSessionSettings(prev => ({
                            ...prev,
                            ui: { ...prev.ui, theme: value }
                          }))
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="auto">Auto</SelectItem>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label className="text-sm">Layout</Label>
                      <Select
                        value={sessionSettings.ui.layout}
                        onValueChange={(value: 'standard' | 'compact' | 'minimal') =>
                          setSessionSettings(prev => ({
                            ...prev,
                            ui: { ...prev.ui, layout: value }
                          }))
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">Standard</SelectItem>
                          <SelectItem value="compact">Compact</SelectItem>
                          <SelectItem value="minimal">Minimal</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Label className="text-sm">Animations</Label>
                        <p className="text-xs text-muted-foreground">Enable smooth transitions</p>
                      </div>
                      <Switch
                        checked={sessionSettings.ui.animations}
                        onCheckedChange={(checked) => 
                          setSessionSettings(prev => ({
                            ...prev,
                            ui: { ...prev.ui, animations: checked }
                          }))
                        }
                      />
                    </div>
                  </div>
                </Card>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4 mt-0">
                <Card className="p-4">
                  <h3 className="text-sm font-medium mb-3">SuperClaude Integration</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Introspection Mode</Label>
                      <Switch
                        checked={superClaudeSettings.coreModes.introspection}
                        onCheckedChange={(checked) => 
                          updateSuperClaudeSettings({
                            ...superClaudeSettings,
                            coreModes: { ...superClaudeSettings.coreModes, introspection: checked }
                          })
                        }
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Label className="text-sm">Ultra Compressed</Label>
                      <Switch
                        checked={superClaudeSettings.coreModes.ultraCompressed}
                        onCheckedChange={(checked) => 
                          updateSuperClaudeSettings({
                            ...superClaudeSettings,
                            coreModes: { ...superClaudeSettings.coreModes, ultraCompressed: checked }
                          })
                        }
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm">Thinking Depth</Label>
                      <Select
                        value={superClaudeSettings.thinkingDepth.level}
                        onValueChange={(value: 'minimal' | 'standard' | 'deep' | 'exhaustive') =>
                          updateSuperClaudeSettings({
                            ...superClaudeSettings,
                            thinkingDepth: { ...superClaudeSettings.thinkingDepth, level: value }
                          })
                        }
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="minimal">Minimal</SelectItem>
                          <SelectItem value="standard">Standard</SelectItem>
                          <SelectItem value="deep">Deep</SelectItem>
                          <SelectItem value="exhaustive">Exhaustive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h3 className="text-sm font-medium mb-3">Export/Import Settings</h3>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const data = JSON.stringify(sessionSettings, null, 2);
                        const blob = new Blob([data], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'session-settings.json';
                        a.click();
                        URL.revokeObjectURL(url);
                      }}
                      className="flex items-center gap-1"
                    >
                      <Download className="h-3 w-3" />
                      Export
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = '.json';
                        input.onchange = (e) => {
                          const file = (e.target as HTMLInputElement).files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              try {
                                const settings = JSON.parse(e.target?.result as string);
                                setSessionSettings(settings);
                              } catch (error) {
                                console.error('Failed to import settings:', error);
                              }
                            };
                            reader.readAsText(file);
                          }
                        };
                        input.click();
                      }}
                      className="flex items-center gap-1"
                    >
                      <Upload className="h-3 w-3" />
                      Import
                    </Button>
                  </div>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setSessionSettings({
                  mode: 'development',
                  mcpServers: [],
                  features: [],
                  performance: { tokenOptimization: true, caching: true, compression: false },
                  ui: { theme: 'auto', layout: 'standard', animations: true },
                  customPresets: [],
                });
                setSelectedPreset(null);
              }}
              className="flex items-center gap-1"
            >
              <RotateCcw className="h-3 w-3" />
              Reset
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleApplySettings} className="flex items-center gap-1">
              <Save className="h-3 w-3" />
              Apply Settings
            </Button>
          </div>
        </DialogFooter>

        {/* Create Preset Dialog */}
        <Dialog open={showCreatePreset} onOpenChange={setShowCreatePreset}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create Custom Preset</DialogTitle>
              <DialogDescription>
                Save your current configuration as a reusable preset
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="preset-name">Preset Name</Label>
                <Input
                  id="preset-name"
                  value={newPresetName}
                  onChange={(e) => setNewPresetName(e.target.value)}
                  placeholder="My Custom Preset"
                />
              </div>
              <div>
                <Label htmlFor="preset-description">Description</Label>
                <Textarea
                  id="preset-description"
                  value={newPresetDescription}
                  onChange={(e) => setNewPresetDescription(e.target.value)}
                  placeholder="Describe what this preset is optimized for..."
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreatePreset(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveCustomPreset} disabled={!newPresetName.trim()}>
                Create Preset
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </DialogContent>
    </Dialog>
  );
};

function getFeatureDescription(feature: string): string {
  const descriptions: Record<string, string> = {
    collaboration: 'Real-time collaborative editing and sharing',
    voiceControl: 'Voice commands and speech recognition',
    codeFlow: 'Visual code flow and dependency tracking',
    performanceProfiler: 'Performance monitoring and optimization',
    smartTemplates: 'AI-powered code templates and snippets',
    pluginSystem: 'Third-party plugin support',
    agentSystem: 'Autonomous agent capabilities',
    mcpMarketplace: 'Browse and install MCP servers',
    smartFeatureSelection: 'AI-powered feature recommendations',
  };
  return descriptions[feature] || 'Advanced feature';
}