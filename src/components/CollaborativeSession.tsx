import { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { api } from '@/lib/api';
import { useFeatureFlags } from '@/lib/featureFlags';
import { motion } from 'framer-motion';
import { 
  Users, 
  Copy, 
  UserPlus,
  UserMinus,
  MessageSquare,
  Crown,
  Edit3,
  Eye,
  CheckCircle,
  XCircle,
  Wifi,
  WifiOff,
  EyeOff,
  Video,
  Mic,
  MicOff,
  Settings,
  AlertCircle
} from 'lucide-react';

interface CollaborativeSessionProps {
  sessionId: string;
  projectPath: string;
  onClose?: () => void;
}

interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer';
  status: 'online' | 'offline' | 'away';
  cursor?: { line: number; column: number };
  color: string;
}

interface SessionPermissions {
  canEdit: boolean;
  canInvite: boolean;
  canRemove: boolean;
  canChat: boolean;
  canVoice: boolean;
}

interface CollaborativeMessage {
  id: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  type: 'text' | 'system' | 'code';
}

export const CollaborativeSession = memo(function CollaborativeSession({ sessionId, projectPath, onClose }: CollaborativeSessionProps) {
  const { flags } = useFeatureFlags();
  const [isConnected, setIsConnected] = useState(false);
  // const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [sessionCode, setSessionCode] = useState('');
  const [shareUrl, setShareUrl] = useState('');
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [permissions, setPermissions] = useState<SessionPermissions>({
    canEdit: true,
    canInvite: true,
    canRemove: true,
    canChat: true,
    canVoice: true
  });
  const [activeTab, setActiveTab] = useState('collaborators');
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'viewer'>('editor');
  const [chatMessages, setChatMessages] = useState<CollaborativeMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [showActivity, setShowActivity] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);

  const websocketRef = useRef<WebSocket | null>(null);
  const chatScrollRef = useRef<HTMLDivElement>(null);

  const addSystemMessage = useCallback((message: string) => {
    setChatMessages(prev => [...prev, {
      id: Date.now().toString(),
      userId: 'system',
      userName: 'System',
      message,
      timestamp: new Date(),
      type: 'system'
    }]);
  }, []);

  useEffect(() => {
    // Initialize collaborative session
    startCollaborativeSession();
    
    return () => {
      // Cleanup WebSocket connection
      if (websocketRef.current) {
        // Remove all event listeners before closing
        websocketRef.current.onopen = null;
        websocketRef.current.onmessage = null;
        websocketRef.current.onclose = null;
        websocketRef.current.onerror = null;
        
        // Close connection if still open
        if (websocketRef.current.readyState === WebSocket.OPEN) {
          websocketRef.current.close();
        }
        
        websocketRef.current = null;
      }
    };
  }, [sessionId]);

  const startCollaborativeSession = async () => {
    // setIsConnecting(true);
    setConnectionError(null);
    
    try {
      // Check if feature is enabled
      if (!flags.collaboration.enabled) {
        setConnectionError('Collaborative sessions are currently disabled. Enable them in settings.');
        return;
      }
      
      // Start collaborative session through API with feature flag settings
      const result = await api.startCollaborativeSession(
        sessionId, 
        projectPath,
        flags.collaboration.useLocalBackend,
        flags.collaboration.localWsUrl,
        flags.collaboration.localShareUrl
      );
      
      setSessionCode(result.sessionCode);
      setShareUrl(result.shareUrl);
      
      // Connect to WebSocket for real-time updates
      connectWebSocket(result.wsUrl);
    } catch (error) {
      console.error('Failed to start collaborative session:', error);
      setConnectionError('Failed to start collaborative session. The collaboration backend may not be running.');
    } finally {
      // setIsConnecting(false);
    }
  };

  const connectWebSocket = (wsUrl: string) => {
    try {
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        setIsConnected(true);
        setConnectionError(null);
        addSystemMessage('Connected to collaborative session');
      };
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };
      
      ws.onclose = () => {
        setIsConnected(false);
        // setIsConnecting(false);
        addSystemMessage('Disconnected from collaborative session');
      };
      
      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
        // setIsConnecting(false);
        setConnectionError('Unable to connect to collaboration server. Please ensure the backend is running or try using local mode.');
      };
      
      websocketRef.current = ws;
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      setConnectionError('Failed to establish WebSocket connection. The URL may be invalid.');
    }
  };

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'collaborator-joined':
        setCollaborators(prev => [...prev, data.collaborator]);
        addSystemMessage(`${data.collaborator.name} joined the session`);
        break;
      case 'collaborator-left':
        setCollaborators(prev => prev.filter(c => c.id !== data.collaboratorId));
        addSystemMessage(`${data.collaboratorName} left the session`);
        break;
      case 'collaborator-update':
        setCollaborators(prev => prev.map(c => 
          c.id === data.collaborator.id ? data.collaborator : c
        ));
        break;
      case 'chat-message':
        setChatMessages(prev => [...prev, data.message]);
        break;
      case 'cursor-update':
        setCollaborators(prev => prev.map(c => 
          c.id === data.userId ? { ...c, cursor: data.cursor } : c
        ));
        break;
      case 'permissions-update':
        setPermissions(data.permissions);
        break;
    }
  };

  const handleCopyLink = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  }, [shareUrl]);

  const handleInviteCollaborator = useCallback(async () => {
    if (!inviteEmail) return;
    
    try {
      await api.inviteCollaborator(sessionId, inviteEmail, inviteRole);
      setShowInviteDialog(false);
      setInviteEmail('');
      addSystemMessage(`Invitation sent to ${inviteEmail}`);
    } catch (error) {
      console.error('Failed to invite collaborator:', error);
    }
  }, [sessionId, inviteEmail, inviteRole]);

  const handleRemoveCollaborator = useCallback(async (collaboratorId: string) => {
    try {
      await api.removeCollaborator(sessionId, collaboratorId);
    } catch (error) {
      console.error('Failed to remove collaborator:', error);
    }
  }, [sessionId]);

  const handleSendMessage = useCallback(() => {
    if (!chatInput.trim() || !websocketRef.current) return;
    
    const message: CollaborativeMessage = {
      id: Date.now().toString(),
      userId: 'current-user',
      userName: 'You',
      message: chatInput,
      timestamp: new Date(),
      type: 'text'
    };
    
    websocketRef.current.send(JSON.stringify({
      type: 'chat-message',
      message
    }));
    
    setChatMessages(prev => [...prev, message]);
    setChatInput('');
  }, [chatInput]);

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="w-3 h-3" />;
      case 'editor':
        return <Edit3 className="w-3 h-3" />;
      case 'viewer':
        return <Eye className="w-3 h-3" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Collaborative Session
            </CardTitle>
            <CardDescription className="mt-1">
              Real-time collaboration with your team
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={isConnected ? "default" : "secondary"} className="gap-1">
              {isConnected ? <Wifi className="w-3 h-3" /> : <WifiOff className="w-3 h-3" />}
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <XCircle className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 overflow-hidden flex flex-col">
        {/* Connection Error Alert */}
        {connectionError && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {connectionError}
              {flags.collaboration.useLocalBackend && (
                <div className="mt-2 text-xs">
                  <strong>Current settings:</strong>
                  <br />
                  WebSocket URL: {flags.collaboration.localWsUrl}
                  <br />
                  Share URL: {flags.collaboration.localShareUrl}
                </div>
              )}
            </AlertDescription>
          </Alert>
        )}
        {/* Share Section */}
        <div className="mb-4 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <Label className="text-sm font-medium">Session Code</Label>
            <Badge variant="outline" className="font-mono">
              {sessionCode || 'Generating...'}
            </Badge>
          </div>
          <div className="flex gap-2">
            <Input
              value={shareUrl}
              readOnly
              placeholder="Share URL will appear here..."
              className="flex-1"
            />
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={handleCopyLink}
                    disabled={!shareUrl}
                  >
                    {copySuccess ? <CheckCircle className="w-4 h-4 text-green-500" /> : <Copy className="w-4 h-4" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {copySuccess ? 'Copied!' : 'Copy share link'}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Button
              size="icon"
              variant="outline"
              onClick={() => setShowInviteDialog(true)}
              disabled={!permissions.canInvite}
            >
              <UserPlus className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="collaborators" className="gap-1">
              <Users className="w-3 h-3" />
              Collaborators ({collaborators.length})
            </TabsTrigger>
            <TabsTrigger value="chat" className="gap-1">
              <MessageSquare className="w-3 h-3" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="activity" className="gap-1">
              <Eye className="w-3 h-3" />
              Activity
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="collaborators" className="flex-1 overflow-auto">
            <ScrollArea className="h-full">
              <div className="space-y-2">
                {collaborators.map((collaborator) => (
                  <div
                    key={collaborator.id}
                    className="flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={collaborator.avatar} />
                          <AvatarFallback style={{ backgroundColor: collaborator.color }}>
                            {collaborator.name.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className={`absolute bottom-0 right-0 w-2.5 h-2.5 rounded-full border-2 border-background ${getStatusColor(collaborator.status)}`} />
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{collaborator.name}</span>
                          <Badge variant="secondary" className="gap-1 text-xs">
                            {getRoleIcon(collaborator.role)}
                            {collaborator.role}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">{collaborator.email}</p>
                      </div>
                    </div>
                    {permissions.canRemove && collaborator.role !== 'owner' && (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveCollaborator(collaborator.id)}
                      >
                        <UserMinus className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="chat" className="flex-1 flex flex-col">
            <ScrollArea className="flex-1 p-4" ref={chatScrollRef}>
              <div className="space-y-3">
                {chatMessages.map((msg) => (
                  <motion.div
                    key={msg.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`flex ${msg.type === 'system' ? 'justify-center' : msg.userId === 'current-user' ? 'justify-end' : 'justify-start'}`}
                  >
                    {msg.type === 'system' ? (
                      <Badge variant="secondary" className="text-xs">
                        {msg.message}
                      </Badge>
                    ) : (
                      <div className={`max-w-[80%] ${msg.userId === 'current-user' ? 'bg-primary text-primary-foreground' : 'bg-muted'} rounded-lg p-3`}>
                        <p className="text-xs font-medium mb-1">{msg.userName}</p>
                        <p className="text-sm">{msg.message}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {new Date(msg.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </ScrollArea>
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Input
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type a message..."
                  disabled={!permissions.canChat}
                />
                <Button onClick={handleSendMessage} disabled={!permissions.canChat}>
                  Send
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="activity" className="flex-1 overflow-auto">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-sm font-medium">Live Activity</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowActivity(!showActivity)}
                >
                  {showActivity ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              {showActivity && (
                <div className="space-y-2">
                  {collaborators
                    .filter(c => c.cursor)
                    .map(c => (
                      <div key={c.id} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 rounded-full" style={{ backgroundColor: c.color }} />
                        <span className="font-medium">{c.name}</span>
                        <span className="text-muted-foreground">
                          editing line {c.cursor?.line}, col {c.cursor?.column}
                        </span>
                      </div>
                    ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
        
        {/* Voice/Video Controls */}
        <div className="mt-4 pt-4 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant={isVoiceEnabled ? "default" : "outline"}
                size="sm"
                onClick={() => setIsVoiceEnabled(!isVoiceEnabled)}
                disabled={!permissions.canVoice}
              >
                <Video className="w-4 h-4 mr-2" />
                {isVoiceEnabled ? 'End Call' : 'Start Call'}
              </Button>
              {isVoiceEnabled && (
                <Button
                  variant={isMuted ? "secondary" : "outline"}
                  size="icon"
                  onClick={() => setIsMuted(!isMuted)}
                >
                  {isMuted ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                </Button>
              )}
            </div>
            <Button variant="ghost" size="icon">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </CardContent>
      
      {/* Invite Dialog */}
      <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Collaborator</DialogTitle>
            <DialogDescription>
              Send an invitation to collaborate on this session
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <div className="flex gap-2 mt-1">
                <Button
                  variant={inviteRole === 'editor' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setInviteRole('editor')}
                  className="flex-1"
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  Editor
                </Button>
                <Button
                  variant={inviteRole === 'viewer' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setInviteRole('viewer')}
                  className="flex-1"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Viewer
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleInviteCollaborator} disabled={!inviteEmail}>
              Send Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
});