import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Code2, 
  Users, 
  FileText, 
  Cpu, 
  <PERSON>uzzle, 
  Bot,
  Sparkles,
  Zap,
  Shield,
  Workflow,
  Split,
  Brain,
  Network
} from 'lucide-react';

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
  category: string;
  priority?: number;
}

interface DynamicNavigationCardsProps {
  visibleFeatures: string[];
  suggestedFeatures: string[];
  onFeatureClick?: (featureId: string) => void;
}

const FEATURE_MAP: Record<string, Feature> = {
  projects: {
    id: 'projects',
    title: 'CC Projects',
    description: 'Browse and manage your Claude Code sessions',
    icon: Code2,
    category: 'core',
    priority: 10
  },
  agents: {
    id: 'agents',
    title: 'CC Agents',
    description: 'Specialized AI agents for development tasks',
    icon: Bot,
    category: 'core',
    priority: 9
  },
  settings: {
    id: 'settings',
    title: 'Settings',
    description: 'Configure application preferences',
    icon: Shield,
    category: 'core',
    priority: 8
  },
  mcp: {
    id: 'mcp',
    title: 'MCP Manager',
    description: 'Manage Model Context Protocol servers',
    icon: Cpu,
    category: 'core',
    priority: 7
  },
  templates: {
    id: 'templates',
    title: 'Smart Templates',
    description: 'Reusable code templates with variables',
    icon: FileText,
    category: 'productivity'
  },
  performance: {
    id: 'performance',
    title: 'Performance Profiler',
    description: 'Monitor API usage and optimize performance',
    icon: Zap,
    category: 'monitoring'
  },
  plugins: {
    id: 'plugins',
    title: 'Plugin Manager',
    description: 'Extend functionality with custom plugins',
    icon: Puzzle,
    category: 'extensibility'
  },
  'agent-system': {
    id: 'agent-system',
    title: 'Agent System',
    description: 'Intelligent AI agents for complex tasks',
    icon: Sparkles,
    category: 'ai'
  },
  'parallel-agents': {
    id: 'parallel-agents',
    title: 'Parallel Agents',
    description: 'Massive parallel AI processing with auto-scaling',
    icon: Split,
    category: 'ai',
    priority: 8
  },
  collaboration: {
    id: 'collaboration',
    title: 'Real-time Collaboration',
    description: 'Work together on code in real-time',
    icon: Users,
    category: 'collaboration'
  },
  workflow: {
    id: 'workflow',
    title: 'Workflow Automation',
    description: 'Automate repetitive development tasks',
    icon: Workflow,
    category: 'automation'
  },
  'bulk-tasks': {
    id: 'bulk-tasks',
    title: 'Bulk Task Manager',
    description: 'Process 5000+ tasks with AI agent delegation',
    icon: Split,
    category: 'ai',
    priority: 6
  },
  'enhanced-agents': {
    id: 'enhanced-agents',
    title: 'Enhanced Agent Dashboard',
    description: 'Advanced agent monitoring and collaboration',
    icon: Users,
    category: 'ai',
    priority: 7
  },
  'visual-effects': {
    id: 'visual-effects',
    title: 'Visual Effects Manager',
    description: 'Customize themes, animations, and visual effects',
    icon: Sparkles,
    category: 'customization',
    priority: 5
  },
  'agent-execution': {
    id: 'agent-execution',
    title: 'Agent Execution Demo',
    description: 'Interactive demo of agent execution and tools',
    icon: Bot,
    category: 'demo',
    priority: 4
  },
  'smart-assistant': {
    id: 'smart-assistant',
    title: 'Smart Code Assistant',
    description: 'AI-powered code suggestions and optimizations',
    icon: Brain,
    category: 'productivity',
    priority: 6
  },
  'smart-features': {
    id: 'smart-features',
    title: 'Smart Feature Selection',
    description: 'Intelligent feature recommendations and management',
    icon: Sparkles,
    category: 'productivity',
    priority: 5
  },
  'workflow-automation': {
    id: 'workflow-automation',
    title: 'Workflow Automation',
    description: 'Automate development workflows and tasks',
    icon: Workflow,
    category: 'automation',
    priority: 7
  },
  'enhanced-mcp': {
    id: 'enhanced-mcp',
    title: 'Enhanced MCP Manager',
    description: 'Advanced MCP server management with monitoring',
    icon: Cpu,
    category: 'core',
    priority: 8
  },
  'enterprise-parallel-agents': {
    id: 'enterprise-parallel-agents',
    title: 'Enterprise Parallel Agents',
    description: 'World-class parallel processing with ML optimization, real-time analytics, and enterprise monitoring',
    icon: Network,
    category: 'ai',
    priority: 10
  },
  'context-engineering': {
    id: 'context-engineering',
    title: 'Context Engineering',
    description: 'Advanced AI context design, cognitive tools, and neural field orchestration',
    icon: Brain,
    category: 'ai',
    priority: 9
  }
};

const categoryColors: Record<string, string> = {
  core: 'bg-gray-500/10 text-gray-600 dark:text-gray-400',
  productivity: 'bg-blue-500/10 text-blue-500',
  monitoring: 'bg-purple-500/10 text-purple-500',
  extensibility: 'bg-green-500/10 text-green-500',
  ai: 'bg-amber-500/10 text-amber-500',
  collaboration: 'bg-pink-500/10 text-pink-500',
  automation: 'bg-indigo-500/10 text-indigo-500',
  customization: 'bg-violet-500/10 text-violet-500',
  demo: 'bg-cyan-500/10 text-cyan-500'
};

export function DynamicNavigationCards({ 
  visibleFeatures, 
  suggestedFeatures,
  onFeatureClick 
}: DynamicNavigationCardsProps) {
  // Ensure we show all available features from FEATURE_MAP that are in visibleFeatures
  const features = Object.values(FEATURE_MAP)
    .filter(feature => visibleFeatures.includes(feature.id))
    .sort((a, b) => (b.priority || 0) - (a.priority || 0));

  if (features.length === 0) return null;

  // Separate featured and regular features
  const featuredFeatures = features.filter(f => ['projects', 'agents'].includes(f.id));
  const regularFeatures = features.filter(f => !['projects', 'agents'].includes(f.id));

  // Group regular features by category
  const groupedFeatures = regularFeatures.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as Record<string, Feature[]>);

  const categoryTitles: Record<string, string> = {
    core: 'Core Development',
    ai: 'AI & Automation',
    productivity: 'Productivity Tools',
    monitoring: 'Monitoring & Analytics',
    automation: 'Workflow Automation',
    customization: 'Customization & Themes',
    extensibility: 'Extensions & Plugins',
    collaboration: 'Collaboration',
    demo: 'Demos & Examples'
  };

  const renderFeatureCard = (feature: Feature, index: number, isFeatured = false) => {
    const Icon = feature.icon;
    const isSuggested = suggestedFeatures.includes(feature.id);
    const isNew = suggestedFeatures.includes(feature.id) && !visibleFeatures.includes(feature.id);

    return (
      <motion.div
        key={feature.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className={isFeatured ? 'md:col-span-2 lg:col-span-1' : ''}
      >
        <Card 
          className={`hover:shadow-xl transition-all duration-300 cursor-pointer group relative overflow-hidden border-2 hover:border-primary/20 ${
            isFeatured ? 'h-full bg-gradient-to-br from-primary/5 to-purple-500/5' : 'hover:scale-[1.02]'
          }`}
          onClick={() => onFeatureClick?.(feature.id)}
        >
          {isNew && (
            <div className="absolute top-3 right-3 z-10">
              <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white border-0 shadow-lg">
                <Sparkles className="w-3 h-3 mr-1" />
                New
              </Badge>
            </div>
          )}
          
          {isSuggested && !isNew && (
            <div className="absolute top-3 right-3 z-10">
              <Badge variant="outline" className="border-amber-500/50 text-amber-600 bg-amber-50 dark:bg-amber-950/50">
                <Zap className="w-3 h-3 mr-1" />
                Suggested
              </Badge>
            </div>
          )}

          <CardHeader className={isFeatured ? 'pb-4' : 'pb-3'}>
            <div className="flex items-start justify-between">
              <div className={`p-3 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 group-hover:scale-110 transition-all duration-300 shadow-sm ${
                isFeatured ? 'p-4' : ''
              }`}>
                <Icon className={`text-gray-700 dark:text-gray-300 ${isFeatured ? 'w-8 h-8' : 'w-6 h-6'}`} />
              </div>
            </div>
            <CardTitle className={`mt-4 group-hover:text-primary transition-colors ${isFeatured ? 'text-xl' : 'text-lg'}`}>
              {feature.title}
            </CardTitle>
          </CardHeader>
          
          <CardContent>
            <CardDescription className={`leading-relaxed ${isFeatured ? 'text-base' : 'text-sm'}`}>
              {feature.description}
            </CardDescription>
            <div className="mt-4 flex items-center justify-between">
              <Badge 
                variant="secondary" 
                className={`${categoryColors[feature.category]} border-0 font-medium`}
              >
                {feature.category}
              </Badge>
              {isFeatured && (
                <Badge variant="outline" className="border-primary/30 text-primary">
                  Featured
                </Badge>
              )}
            </div>
          </CardContent>

          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary/0 via-primary/60 to-primary/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="space-y-12">
      {/* Featured Features Section */}
      {featuredFeatures.length > 0 && (
        <div>
          <motion.h2 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="text-2xl font-bold mb-6 text-center bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent"
          >
            Get Started
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {featuredFeatures.map((feature, index) => renderFeatureCard(feature, index, true))}
          </div>
        </div>
      )}

      {/* Categorized Features */}
      {Object.entries(groupedFeatures).map(([category, categoryFeatures], categoryIndex) => (
        <div key={category}>
          <motion.h3 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: categoryIndex * 0.1 }}
            className="text-xl font-semibold mb-6 flex items-center gap-3"
          >
            <div className={`w-1 h-6 rounded-full ${categoryColors[category].includes('blue') ? 'bg-blue-500' : 
              categoryColors[category].includes('green') ? 'bg-green-500' :
              categoryColors[category].includes('purple') ? 'bg-purple-500' :
              categoryColors[category].includes('amber') ? 'bg-amber-500' :
              categoryColors[category].includes('pink') ? 'bg-pink-500' :
              categoryColors[category].includes('indigo') ? 'bg-indigo-500' :
              categoryColors[category].includes('violet') ? 'bg-violet-500' :
              categoryColors[category].includes('cyan') ? 'bg-cyan-500' : 'bg-gray-500'
            }`} />
            {categoryTitles[category] || category}
          </motion.h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categoryFeatures.map((feature, index) => renderFeatureCard(feature, index + featuredFeatures.length))}
          </div>
        </div>
      ))}
    </div>
  );
}