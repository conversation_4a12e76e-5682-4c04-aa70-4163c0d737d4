import { useState, useEffect, useRef, useMemo, useC<PERSON>back, memo } from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent, CardDescription } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { api } from '@/lib/api';
import { 
  LineChart, 
  Line, 
  // AreaChart, Area - not used in this version 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { 
  Activity, 
  Zap, 
  Clock, 
  DollarSign,
  AlertCircle,
  Download,
  RefreshCw,
  Calendar,
  Loader2,
  Timer
} from 'lucide-react';

interface PerformanceProfilerProps {
  sessionId: string;
  className?: string;
}

interface PerformanceMetrics {
  timestamp: Date;
  apiCalls: number;
  totalTokens: number;
  inputTokens: number;
  outputTokens: number;
  latency: number;
  cost: number;
  model: string;
  endpoint: string;
  success: boolean;
  errorRate: number;
  cacheHits: number;
  cacheMisses: number;
}

interface AggregatedMetrics {
  totalCalls: number;
  totalTokens: number;
  totalCost: number;
  averageLatency: number;
  errorRate: number;
  cacheHitRate: number;
  tokensPerMinute: number;
  costPerHour: number;
  modelUsage: Record<string, number>;
  endpointUsage: Record<string, number>;
  peakUsageTime: Date;
  bottlenecks: Bottleneck[];
}

interface Bottleneck {
  type: 'latency' | 'cost' | 'error' | 'token';
  severity: 'low' | 'medium' | 'high';
  description: string;
  recommendation: string;
  impact: number;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: Date;
  metric: string;
  value: number;
  threshold: number;
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'];

export const PerformanceProfiler = memo(function PerformanceProfiler({ sessionId, className }: PerformanceProfilerProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [aggregatedMetrics, setAggregatedMetrics] = useState<AggregatedMetrics | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');
  const [isLoading, setIsLoading] = useState(true);
  const [isRecording, setIsRecording] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState<'latency' | 'tokens' | 'cost' | 'errors'>('latency');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    loadMetrics();
    
    // Set up real-time monitoring
    if (isRecording) {
      intervalRef.current = setInterval(() => {
        loadMetrics();
      }, 5000); // Update every 5 seconds
    }
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [sessionId, timeRange, isRecording]);
  
  // Clear interval when recording stops
  useEffect(() => {
    if (!isRecording && intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [isRecording]);

  const loadMetrics = async () => {
    setIsLoading(true);
    try {
      const data = await api.getPerformanceMetrics(sessionId, timeRange);
      setMetrics(data.metrics);
      setAggregatedMetrics(data.aggregated);
      checkForAlerts(data.metrics, data.aggregated);
    } catch (error) {
      console.error('Failed to load performance metrics:', error);
      // Use mock data for demonstration
      const mockData = generateMockMetrics();
      setMetrics(mockData.metrics);
      setAggregatedMetrics(mockData.aggregated);
      checkForAlerts(mockData.metrics, mockData.aggregated);
    } finally {
      setIsLoading(false);
    }
  };

  const generateMockMetrics = (): { metrics: PerformanceMetrics[], aggregated: AggregatedMetrics } => {
    const now = new Date();
    const metrics: PerformanceMetrics[] = [];
    
    // Generate time series data
    for (let i = 0; i < 100; i++) {
      const timestamp = new Date(now.getTime() - (100 - i) * 60000); // Every minute
      const isError = Math.random() < 0.05;
      
      metrics.push({
        timestamp,
        apiCalls: Math.floor(Math.random() * 10) + 1,
        totalTokens: Math.floor(Math.random() * 2000) + 500,
        inputTokens: Math.floor(Math.random() * 1000) + 200,
        outputTokens: Math.floor(Math.random() * 1000) + 300,
        latency: Math.random() * 2000 + 200 + (isError ? 5000 : 0),
        cost: Math.random() * 0.05 + 0.01,
        model: Math.random() > 0.3 ? 'claude-3-opus' : 'claude-3-sonnet',
        endpoint: ['complete', 'chat', 'edit'][Math.floor(Math.random() * 3)],
        success: !isError,
        errorRate: isError ? 1 : 0,
        cacheHits: Math.floor(Math.random() * 5),
        cacheMisses: Math.floor(Math.random() * 3)
      });
    }
    
    // Calculate aggregated metrics
    const totalCalls = metrics.reduce((sum, m) => sum + m.apiCalls, 0);
    const totalTokens = metrics.reduce((sum, m) => sum + m.totalTokens, 0);
    const totalCost = metrics.reduce((sum, m) => sum + (m.cost || 0), 0);
    const averageLatency = metrics.reduce((sum, m) => sum + m.latency, 0) / metrics.length;
    const errorCount = metrics.filter(m => !m.success).length;
    const errorRate = (errorCount / metrics.length) * 100;
    const totalCacheHits = metrics.reduce((sum, m) => sum + m.cacheHits, 0);
    const totalCacheMisses = metrics.reduce((sum, m) => sum + m.cacheMisses, 0);
    const cacheHitRate = (totalCacheHits / (totalCacheHits + totalCacheMisses)) * 100;
    
    const modelUsage: Record<string, number> = {};
    const endpointUsage: Record<string, number> = {};
    
    metrics.forEach(m => {
      modelUsage[m.model] = (modelUsage[m.model] || 0) + 1;
      endpointUsage[m.endpoint] = (endpointUsage[m.endpoint] || 0) + 1;
    });
    
    const bottlenecks: Bottleneck[] = [];
    
    if (averageLatency > 2000) {
      bottlenecks.push({
        type: 'latency',
        severity: averageLatency > 5000 ? 'high' : 'medium',
        description: `High average latency: ${averageLatency.toFixed(0)}ms`,
        recommendation: 'Consider implementing request batching or caching',
        impact: 80
      });
    }
    
    if (errorRate > 5) {
      bottlenecks.push({
        type: 'error',
        severity: errorRate > 10 ? 'high' : 'medium',
        description: `High error rate: ${errorRate.toFixed(1)}%`,
        recommendation: 'Review error logs and implement retry logic',
        impact: 90
      });
    }
    
    if (totalCost / metrics.length > 0.1) {
      bottlenecks.push({
        type: 'cost',
        severity: 'medium',
        description: `High average cost per request: $${(totalCost / metrics.length).toFixed(3)}`,
        recommendation: 'Consider using smaller models for simple tasks',
        impact: 60
      });
    }
    
    const aggregated: AggregatedMetrics = {
      totalCalls,
      totalTokens,
      totalCost,
      averageLatency,
      errorRate,
      cacheHitRate,
      tokensPerMinute: totalTokens / 100,
      costPerHour: (totalCost / 100) * 60,
      modelUsage,
      endpointUsage,
      peakUsageTime: metrics[Math.floor(metrics.length / 2)].timestamp,
      bottlenecks
    };
    
    return { metrics, aggregated };
  };

  const checkForAlerts = (_metrics: PerformanceMetrics[], aggregated: AggregatedMetrics) => {
    const newAlerts: PerformanceAlert[] = [];
    
    // Check for high latency
    if (aggregated.averageLatency > 3000) {
      newAlerts.push({
        id: 'high-latency',
        type: 'warning',
        message: 'API latency is above normal levels',
        timestamp: new Date(),
        metric: 'latency',
        value: aggregated.averageLatency,
        threshold: 3000
      });
    }
    
    // Check for high error rate
    if (aggregated.errorRate > 5) {
      newAlerts.push({
        id: 'high-errors',
        type: 'error',
        message: 'Error rate exceeds acceptable threshold',
        timestamp: new Date(),
        metric: 'errorRate',
        value: aggregated.errorRate,
        threshold: 5
      });
    }
    
    // Check for cost spike
    if (aggregated.costPerHour > 10) {
      newAlerts.push({
        id: 'high-cost',
        type: 'warning',
        message: 'API costs are higher than expected',
        timestamp: new Date(),
        metric: 'costPerHour',
        value: aggregated.costPerHour,
        threshold: 10
      });
    }
    
    setAlerts(newAlerts);
  };

  const exportMetrics = () => {
    if (!metrics || !aggregatedMetrics) return;
    
    const report = {
      sessionId,
      timeRange,
      generatedAt: new Date().toISOString(),
      summary: aggregatedMetrics,
      detailedMetrics: metrics,
      alerts
    };
    
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `performance-report-${sessionId}-${new Date().getTime()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const formatDuration = useCallback((ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  }, []);

  const formatCost = useCallback((cost: number): string => {
    if (cost === undefined || cost === null) return '$0.0000';
    return `$${cost.toFixed(4)}`;
  }, []);

  const getMetricChartData = useMemo(() => {
    return metrics.map(m => ({
      time: new Date(m.timestamp).toLocaleTimeString(),
      latency: m.latency,
      tokens: m.totalTokens,
      cost: m.cost * 1000, // Scale for visibility
      errors: m.errorRate * 100
    }));
  }, [metrics]);

  const getModelUsageData = useMemo(() => {
    if (!aggregatedMetrics || !aggregatedMetrics.modelUsage) return [];
    
    return Object.entries(aggregatedMetrics.modelUsage).map(([model, count]) => ({
      name: model,
      value: count,
      percentage: (aggregatedMetrics?.totalCalls || 0) > 0 ? (count / (aggregatedMetrics?.totalCalls || 1)) * 100 : 0
    }));
  }, [aggregatedMetrics]);

  const getEndpointUsageData = useMemo(() => {
    if (!aggregatedMetrics || !aggregatedMetrics.endpointUsage) return [];
    
    return Object.entries(aggregatedMetrics.endpointUsage).map(([endpoint, count]) => ({
      name: endpoint,
      value: count
    }));
  }, [aggregatedMetrics]);

  const getPerformanceRadarData = useMemo(() => {
    if (!aggregatedMetrics) return [];
    
    return [
      {
        metric: 'Latency',
        value: Math.max(0, 100 - ((aggregatedMetrics?.averageLatency || 0) / 50)),
        fullMark: 100
      },
      {
        metric: 'Success Rate',
        value: 100 - (aggregatedMetrics?.errorRate || 0),
        fullMark: 100
      },
      {
        metric: 'Cache Efficiency',
        value: aggregatedMetrics?.cacheHitRate || 0,
        fullMark: 100
      },
      {
        metric: 'Cost Efficiency',
        value: Math.max(0, 100 - ((aggregatedMetrics?.costPerHour || 0) * 5)),
        fullMark: 100
      },
      {
        metric: 'Token Usage',
        value: Math.max(0, 100 - ((aggregatedMetrics?.tokensPerMinute || 0) / 100)),
        fullMark: 100
      }
    ];
  }, [aggregatedMetrics]);

  return (
    <div className={`h-full flex flex-col ${className}`}>
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Performance Profiler
              </CardTitle>
              <CardDescription>
                Real-time API usage monitoring and optimization
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Select value={timeRange} onValueChange={(v) => setTimeRange(v as any)}>
                <SelectTrigger className="w-[120px]">
                  <Calendar className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="24h">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant={isRecording ? "default" : "outline"}
                size="sm"
                onClick={() => setIsRecording(!isRecording)}
              >
                {isRecording ? (
                  <>
                    <Timer className="w-4 h-4 mr-2 animate-pulse" />
                    Recording
                  </>
                ) : (
                  <>
                    <Timer className="w-4 h-4 mr-2" />
                    Paused
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={loadMetrics}
              >
                <RefreshCw className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={exportMetrics}
              >
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 overflow-auto">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="w-8 h-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-6">
              {/* Alerts */}
              {alerts.length > 0 && (
                <div className="space-y-2">
                  {alerts.map(alert => (
                    <Alert key={alert.id} variant={alert.type === 'error' ? 'destructive' : 'default'}>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        {alert.message} (Current: {alert.value.toFixed(2)}, Threshold: {alert.threshold})
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}
              
              {/* Summary Cards */}
              {aggregatedMetrics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center justify-between">
                        <span>Total API Calls</span>
                        <Zap className="w-4 h-4 text-muted-foreground" />
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{aggregatedMetrics?.totalCalls || 0}</div>
                      <p className="text-xs text-muted-foreground">
                        {metrics.length > 0 ? ((aggregatedMetrics?.totalCalls || 0) / metrics.length).toFixed(1) : '0.0'} calls/min
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center justify-between">
                        <span>Average Latency</span>
                        <Clock className="w-4 h-4 text-muted-foreground" />
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatDuration(aggregatedMetrics?.averageLatency || 0)}</div>
                      <Progress value={(3000 - (aggregatedMetrics?.averageLatency || 0)) / 30} className="mt-2" />
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center justify-between">
                        <span>Total Cost</span>
                        <DollarSign className="w-4 h-4 text-muted-foreground" />
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatCost(aggregatedMetrics?.totalCost || 0)}</div>
                      <p className="text-xs text-muted-foreground">
                        {formatCost(aggregatedMetrics?.costPerHour || 0)}/hour
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center justify-between">
                        <span>Error Rate</span>
                        <AlertCircle className="w-4 h-4 text-muted-foreground" />
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{aggregatedMetrics?.errorRate?.toFixed(1) || '0.0'}%</div>
                      <Progress 
                        value={100 - (aggregatedMetrics?.errorRate || 0)} 
                        className="mt-2"
                        // @ts-ignore - custom color
                        indicatorClassName={(aggregatedMetrics?.errorRate || 0) > 5 ? "bg-red-500" : ""}
                      />
                    </CardContent>
                  </Card>
                </div>
              )}
              
              {/* Charts */}
              <Tabs defaultValue="timeline" className="space-y-4">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="timeline">Timeline</TabsTrigger>
                  <TabsTrigger value="distribution">Distribution</TabsTrigger>
                  <TabsTrigger value="performance">Performance</TabsTrigger>
                  <TabsTrigger value="bottlenecks">Bottlenecks</TabsTrigger>
                  <TabsTrigger value="optimization">Optimization</TabsTrigger>
                </TabsList>
                
                <TabsContent value="timeline" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Metrics Over Time</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={getMetricChartData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="time" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line 
                              type="monotone" 
                              dataKey={selectedMetric} 
                              stroke="#3B82F6" 
                              strokeWidth={2}
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                      <div className="flex justify-center gap-2 mt-4">
                        <Button
                          size="sm"
                          variant={selectedMetric === 'latency' ? 'default' : 'outline'}
                          onClick={() => setSelectedMetric('latency')}
                        >
                          Latency
                        </Button>
                        <Button
                          size="sm"
                          variant={selectedMetric === 'tokens' ? 'default' : 'outline'}
                          onClick={() => setSelectedMetric('tokens')}
                        >
                          Tokens
                        </Button>
                        <Button
                          size="sm"
                          variant={selectedMetric === 'cost' ? 'default' : 'outline'}
                          onClick={() => setSelectedMetric('cost')}
                        >
                          Cost
                        </Button>
                        <Button
                          size="sm"
                          variant={selectedMetric === 'errors' ? 'default' : 'outline'}
                          onClick={() => setSelectedMetric('errors')}
                        >
                          Errors
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="distribution" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm">Model Usage</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-64">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Pie
                                data={getModelUsageData}
                                dataKey="value"
                                nameKey="name"
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                label={({ percentage }) => `${percentage.toFixed(0)}%`}
                              >
                                {getModelUsageData.map((_entry, index) => (
                                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                              </Pie>
                              <Tooltip />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm">Endpoint Usage</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-64">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={getEndpointUsageData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="name" />
                              <YAxis />
                              <Tooltip />
                              <Bar dataKey="value" fill="#3B82F6" />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
                
                <TabsContent value="performance" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Performance Radar</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RadarChart data={getPerformanceRadarData}>
                            <PolarGrid />
                            <PolarAngleAxis dataKey="metric" />
                            <PolarRadiusAxis angle={90} domain={[0, 100]} />
                            <Radar
                              name="Performance"
                              dataKey="value"
                              stroke="#3B82F6"
                              fill="#3B82F6"
                              fillOpacity={0.6}
                            />
                            <Tooltip />
                          </RadarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="bottlenecks" className="space-y-4">
                  {aggregatedMetrics && aggregatedMetrics.bottlenecks && aggregatedMetrics.bottlenecks.length > 0 ? (
                    <div className="space-y-4">
                      {aggregatedMetrics.bottlenecks.map((bottleneck, index) => (
                        <Card key={index}>
                          <CardHeader>
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-sm flex items-center gap-2">
                                <Badge variant={
                                  bottleneck.severity === 'high' ? 'destructive' :
                                  bottleneck.severity === 'medium' ? 'default' : 'secondary'
                                }>
                                  {bottleneck.severity}
                                </Badge>
                                {bottleneck.description}
                              </CardTitle>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-muted-foreground">Impact</span>
                                <Progress value={bottleneck.impact} className="w-20" />
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent>
                            <p className="text-sm text-muted-foreground">
                              {bottleneck.recommendation}
                            </p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="flex items-center justify-center h-32">
                        <p className="text-muted-foreground">No bottlenecks detected</p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
                
                <TabsContent value="optimization" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Optimization Recommendations</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {aggregatedMetrics && (
                        <>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm">Cache Hit Rate</span>
                              <div className="flex items-center gap-2">
                                <Progress value={aggregatedMetrics?.cacheHitRate || 0} className="w-32" />
                                <span className="text-sm font-medium">{(aggregatedMetrics?.cacheHitRate || 0).toFixed(1)}%</span>
                              </div>
                            </div>
                            {(aggregatedMetrics?.cacheHitRate || 0) < 70 && (
                              <p className="text-xs text-muted-foreground">
                                Consider implementing more aggressive caching strategies
                              </p>
                            )}
                          </div>
                          
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium">Cost Optimization</h4>
                            <ul className="space-y-1 text-sm text-muted-foreground">
                              <li>• Use claude-3-sonnet for simple tasks (currently {getModelUsageData.find(m => m.name.includes('sonnet'))?.percentage.toFixed(0) || 0}%)</li>
                              <li>• Batch similar requests to reduce API calls</li>
                              <li>• Implement request deduplication</li>
                            </ul>
                          </div>
                          
                          <div className="space-y-2">
                            <h4 className="text-sm font-medium">Performance Optimization</h4>
                            <ul className="space-y-1 text-sm text-muted-foreground">
                              <li>• Average latency can be reduced by {Math.max(0, (aggregatedMetrics?.averageLatency || 0) - 1000).toFixed(0)}ms</li>
                              <li>• Consider implementing request prioritization</li>
                              <li>• Use streaming for long responses</li>
                            </ul>
                          </div>
                        </>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
});