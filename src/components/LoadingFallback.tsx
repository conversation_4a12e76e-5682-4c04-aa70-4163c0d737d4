import { Loader2 } from "lucide-react";
import { Card } from "@/components/ui/card";

export function LoadingFallback() {
  return (
    <Card className="flex-1 flex items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <p className="text-muted-foreground">Loading component...</p>
      </div>
    </Card>
  );
}