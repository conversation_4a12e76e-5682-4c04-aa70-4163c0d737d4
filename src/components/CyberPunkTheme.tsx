import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface CyberPunkThemeProps {
  enabled: boolean;
  glitchIntensity: number;
}

export const CyberPunkTheme: React.FC<CyberPunkThemeProps> = ({
  enabled,
  glitchIntensity,
}) => {
  const [glitchActive, setGlitchActive] = useState(false);

  useEffect(() => {
    if (!enabled) return;

    const interval = setInterval(() => {
      setGlitchActive(true);
      setTimeout(() => setGlitchActive(false), 100);
    }, 3000 + Math.random() * 2000);

    return () => clearInterval(interval);
  }, [enabled]);

  if (!enabled) return null;

  return (
    <>
      {/* Cyberpunk grid overlay */}
      <div className="fixed inset-0 pointer-events-none z-0">
        <div
          className="w-full h-full opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px',
          }}
        />
      </div>

      {/* Glitch effect */}
      {glitchActive && (
        <motion.div
          className="fixed inset-0 pointer-events-none z-20"
          initial={{ opacity: 0 }}
          animate={{ opacity: glitchIntensity / 100 }}
          exit={{ opacity: 0 }}
        >
          <div
            className="w-full h-full mix-blend-difference"
            style={{
              background: `
                repeating-linear-gradient(
                  0deg,
                  transparent,
                  transparent 2px,
                  rgba(255, 0, 255, 0.1) 2px,
                  rgba(255, 0, 255, 0.1) 4px
                )
              `,
            }}
          />
        </motion.div>
      )}

      {/* Neon glow effects */}
      <style jsx global>{`
        .cyberpunk-glow {
          box-shadow: 
            0 0 5px #00ffff,
            0 0 10px #00ffff,
            0 0 15px #00ffff,
            0 0 20px #00ffff;
        }
        
        .cyberpunk-text {
          text-shadow: 
            0 0 5px #00ffff,
            0 0 10px #00ffff,
            0 0 15px #00ffff;
        }
      `}</style>
    </>
  );
};