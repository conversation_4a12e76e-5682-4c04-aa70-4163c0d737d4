import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { AIAgentOrchestrator } from '@/lib/ai/AIAgentOrchestrator';
import { useMCPServers } from '@/hooks/useMCPServers';
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Zap,
  Clock,
  BarChart3
} from 'lucide-react';

interface PredictiveInsight {
  id: string;
  type: 'performance' | 'capacity' | 'failure' | 'optimization';
  title: string;
  prediction: string;
  confidence: number;
  timeframe: string;
  impact: 'low' | 'medium' | 'high';
  trend: 'up' | 'down' | 'stable';
}

export function MCPPredictiveInsights() {
  const { servers, getServerMetrics } = useMCPServers();
  const [insights, setInsights] = useState<PredictiveInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const aiOrchestrator = AIAgentOrchestrator.getInstance();

  useEffect(() => {
    loadPredictiveInsights();
  }, [servers]);

  const loadPredictiveInsights = async () => {
    setLoading(true);
    try {
      const allInsights: PredictiveInsight[] = [];

      for (const server of servers.filter(s => s.status === 'running')) {
        const serverInsights = await aiOrchestrator.getPredictiveInsights(server.id);
        allInsights.push(...serverInsights);
      }

      setInsights(allInsights);
    } catch (error) {
      console.error('Failed to load predictive insights:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'performance': return Zap;
      case 'capacity': return BarChart3;
      case 'failure': return AlertTriangle;
      case 'optimization': return Target;
      default: return TrendingUp;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return TrendingUp;
      case 'down': return TrendingDown;
      default: return Target;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-500';
      case 'medium': return 'text-yellow-500';
      case 'low': return 'text-green-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {insights.map((insight) => {
          const TypeIcon = getTypeIcon(insight.type);
          const TrendIcon = getTrendIcon(insight.trend);
          
          return (
            <Card key={insight.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TypeIcon className="w-5 h-5" />
                    <CardTitle className="text-base">{insight.title}</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendIcon className={`w-4 h-4 ${getImpactColor(insight.impact)}`} />
                    <Badge variant={insight.impact === 'high' ? 'destructive' : 'secondary'}>
                      {insight.impact}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm">{insight.prediction}</p>
                
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>{insight.timeframe}</span>
                  </div>
                  <Badge variant="outline">
                    {Math.round(insight.confidence * 100)}% confidence
                  </Badge>
                </div>
                
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Prediction Confidence</span>
                    <span>{Math.round(insight.confidence * 100)}%</span>
                  </div>
                  <Progress value={insight.confidence * 100} className="h-1" />
                </div>
                
                {insight.impact === 'high' && (
                  <Button size="sm" className="w-full">
                    Take Action
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}