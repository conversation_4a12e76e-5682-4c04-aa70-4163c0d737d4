import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface FeatureWrapperProps {
  children: React.ReactNode;
  onBack?: () => void;
  title?: string;
  showBackButton?: boolean;
}

export function FeatureWrapper({ 
  children, 
  onBack, 
  title,
  showBackButton = true 
}: FeatureWrapperProps) {
  return (
    <div className="flex flex-col h-full">
      {showBackButton && onBack && (
        <div className="px-6 pt-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          {title && (
            <h1 className="text-2xl font-bold mb-4">{title}</h1>
          )}
        </div>
      )}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
}