import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Bot,
  Zap,
  Shield,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Settings,
  Brain,
  Target,
  Workflow,
  Clock,
  Activity
} from 'lucide-react';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: string;
  action: string;
  enabled: boolean;
  lastTriggered?: Date;
  executionCount: number;
}

interface IntelligentWorkflow {
  id: string;
  name: string;
  steps: WorkflowStep[];
  status: 'active' | 'paused' | 'completed';
  progress: number;
  aiOptimized: boolean;
}

interface WorkflowStep {
  id: string;
  name: string;
  type: 'condition' | 'action' | 'ai_decision';
  status: 'pending' | 'running' | 'completed' | 'failed';
  aiConfidence?: number;
}

export function MCPIntelligentAutomation() {
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([
    {
      id: '1',
      name: 'Auto-Scale on High Load',
      description: 'Automatically scale servers when CPU usage exceeds 80%',
      trigger: 'cpu_usage > 80%',
      action: 'scale_up',
      enabled: true,
      executionCount: 12,
      lastTriggered: new Date(Date.now() - 3600000)
    },
    {
      id: '2',
      name: 'Restart on Memory Leak',
      description: 'Restart server when memory usage grows continuously',
      trigger: 'memory_trend = increasing AND memory > 90%',
      action: 'restart_server',
      enabled: true,
      executionCount: 3,
      lastTriggered: new Date(Date.now() - 7200000)
    },
    {
      id: '3',
      name: 'Security Alert Response',
      description: 'Isolate server on security threat detection',
      trigger: 'security_threat_detected',
      action: 'isolate_server',
      enabled: true,
      executionCount: 0
    }
  ]);

  const [workflows, setWorkflows] = useState<IntelligentWorkflow[]>([
    {
      id: '1',
      name: 'Performance Optimization Pipeline',
      steps: [
        { id: '1', name: 'Analyze Performance', type: 'ai_decision', status: 'completed', aiConfidence: 0.92 },
        { id: '2', name: 'Generate Recommendations', type: 'ai_decision', status: 'completed', aiConfidence: 0.88 },
        { id: '3', name: 'Apply Optimizations', type: 'action', status: 'running' },
        { id: '4', name: 'Validate Results', type: 'condition', status: 'pending' }
      ],
      status: 'active',
      progress: 65,
      aiOptimized: true
    },
    {
      id: '2',
      name: 'Security Hardening Workflow',
      steps: [
        { id: '1', name: 'Scan for Vulnerabilities', type: 'action', status: 'completed' },
        { id: '2', name: 'AI Risk Assessment', type: 'ai_decision', status: 'completed', aiConfidence: 0.95 },
        { id: '3', name: 'Apply Security Patches', type: 'action', status: 'completed' },
        { id: '4', name: 'Verify Security Posture', type: 'condition', status: 'completed' }
      ],
      status: 'completed',
      progress: 100,
      aiOptimized: true
    }
  ]);

  const [aiSettings, setAiSettings] = useState({
    autoOptimization: true,
    predictiveScaling: true,
    anomalyDetection: true,
    intelligentRouting: true,
    adaptiveCaching: true,
    selfHealing: true
  });

  const toggleRule = (ruleId: string) => {
    setAutomationRules(prev => 
      prev.map(rule => 
        rule.id === ruleId 
          ? { ...rule, enabled: !rule.enabled }
          : rule
      )
    );
  };

  const getStepIcon = (type: string, status: string) => {
    if (status === 'completed') return CheckCircle;
    if (status === 'failed') return AlertTriangle;
    if (status === 'running') return Activity;
    
    switch (type) {
      case 'ai_decision': return Brain;
      case 'action': return Zap;
      case 'condition': return Target;
      default: return Clock;
    }
  };

  const getStepColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-500';
      case 'running': return 'text-blue-500';
      case 'failed': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            AI Automation Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="auto-opt">Auto Optimization</Label>
              <Switch
                id="auto-opt"
                checked={aiSettings.autoOptimization}
                onCheckedChange={(checked) => 
                  setAiSettings(prev => ({ ...prev, autoOptimization: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="pred-scale">Predictive Scaling</Label>
              <Switch
                id="pred-scale"
                checked={aiSettings.predictiveScaling}
                onCheckedChange={(checked) => 
                  setAiSettings(prev => ({ ...prev, predictiveScaling: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="anomaly">Anomaly Detection</Label>
              <Switch
                id="anomaly"
                checked={aiSettings.anomalyDetection}
                onCheckedChange={(checked) => 
                  setAiSettings(prev => ({ ...prev, anomalyDetection: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="routing">Intelligent Routing</Label>
              <Switch
                id="routing"
                checked={aiSettings.intelligentRouting}
                onCheckedChange={(checked) => 
                  setAiSettings(prev => ({ ...prev, intelligentRouting: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="caching">Adaptive Caching</Label>
              <Switch
                id="caching"
                checked={aiSettings.adaptiveCaching}
                onCheckedChange={(checked) => 
                  setAiSettings(prev => ({ ...prev, adaptiveCaching: checked }))
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <Label htmlFor="healing">Self-Healing</Label>
              <Switch
                id="healing"
                checked={aiSettings.selfHealing}
                onCheckedChange={(checked) => 
                  setAiSettings(prev => ({ ...prev, selfHealing: checked }))
                }
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="rules" className="space-y-4">
        <TabsList>
          <TabsTrigger value="rules">Automation Rules</TabsTrigger>
          <TabsTrigger value="workflows">AI Workflows</TabsTrigger>
          <TabsTrigger value="analytics">Smart Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2">
                  <Bot className="w-5 h-5" />
                  Automation Rules
                </CardTitle>
                <Button>
                  <Settings className="w-4 h-4 mr-2" />
                  Add Rule
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {automationRules.map((rule) => (
                  <div key={rule.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{rule.name}</h4>
                        <p className="text-sm text-muted-foreground">{rule.description}</p>
                      </div>
                      <Switch
                        checked={rule.enabled}
                        onCheckedChange={() => toggleRule(rule.id)}
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Trigger:</span>
                        <div className="font-mono bg-muted p-2 rounded mt-1">
                          {rule.trigger}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Action:</span>
                        <div className="font-mono bg-muted p-2 rounded mt-1">
                          {rule.action}
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Statistics:</span>
                        <div className="mt-1">
                          <div>Executed: {rule.executionCount} times</div>
                          {rule.lastTriggered && (
                            <div>Last: {rule.lastTriggered.toLocaleTimeString()}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Workflow className="w-5 h-5" />
                Intelligent Workflows
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {workflows.map((workflow) => (
                  <div key={workflow.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium flex items-center gap-2">
                          {workflow.name}
                          {workflow.aiOptimized && (
                            <Badge variant="secondary">
                              <Brain className="w-3 h-3 mr-1" />
                              AI Optimized
                            </Badge>
                          )}
                        </h4>
                        <div className="flex items-center gap-4 mt-2">
                          <Badge variant={workflow.status === 'completed' ? 'default' : 'secondary'}>
                            {workflow.status}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {workflow.progress}% complete
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">{workflow.progress}%</div>
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-blue-500 transition-all duration-300"
                            style={{ width: `${workflow.progress}%` }}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {workflow.steps.map((step, index) => {
                        const Icon = getStepIcon(step.type, step.status);
                        
                        return (
                          <div key={step.id} className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                              <Icon className={`w-4 h-4 ${getStepColor(step.status)}`} />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{step.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {step.type.replace('_', ' ')}
                                </Badge>
                                {step.aiConfidence && (
                                  <Badge variant="secondary" className="text-xs">
                                    {Math.round(step.aiConfidence * 100)}% confidence
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <Badge variant={step.status === 'completed' ? 'default' : 'secondary'}>
                              {step.status}
                            </Badge>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>AI Performance Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Automation Efficiency</span>
                    <span className="font-bold text-green-600">94%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Cost Reduction</span>
                    <span className="font-bold text-blue-600">32%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Incident Prevention</span>
                    <span className="font-bold text-purple-600">87%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Response Time Improvement</span>
                    <span className="font-bold text-orange-600">56%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Predictive Alerts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                    <AlertTriangle className="w-4 h-4 text-yellow-600" />
                    <span className="text-sm">High load predicted in 2 hours</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                    <TrendingUp className="w-4 h-4 text-blue-600" />
                    <span className="text-sm">Memory optimization opportunity detected</span>
                  </div>
                  <div className="flex items-center gap-2 p-2 bg-green-50 dark:bg-green-900/20 rounded">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-sm">All systems operating optimally</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}