import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useMotionValue, useTransform } from 'framer-motion';
import {
  Zap,
  Eye,
  Layers,
  Cpu,
  Activity,
  Waves,
  Star,
  Hexagon,
  Triangle,
  Square,
  Circle,
  Diamond,
  Sparkles,
  Globe,
  Network,
  Brain,
  Atom,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';

interface HolographicInterfaceProps {
  enabled: boolean;
  intensity: number;
  className?: string;
}

// 3D Holographic Grid
const HolographicGrid: React.FC<{ enabled: boolean; intensity: number }> = ({ enabled, intensity }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  useEffect(() => {
    if (!enabled || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    let time = 0;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Create perspective grid
      const gridSize = 50;
      const perspective = 400;
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      ctx.strokeStyle = `rgba(0, 255, 255, ${0.3 * intensity / 100})`;
      ctx.lineWidth = 1;

      // Draw horizontal lines
      for (let i = -10; i <= 10; i++) {
        const y = i * gridSize;
        const z = Math.sin(time * 0.01 + i * 0.1) * 50;
        
        const startX = -500;
        const endX = 500;
        
        const projectedY1 = centerY + (y * perspective) / (perspective + z);
        const projectedY2 = centerY + (y * perspective) / (perspective + z);
        
        ctx.beginPath();
        ctx.moveTo(centerX + startX, projectedY1);
        ctx.lineTo(centerX + endX, projectedY2);
        ctx.stroke();
      }

      // Draw vertical lines
      for (let i = -10; i <= 10; i++) {
        const x = i * gridSize;
        const z1 = Math.sin(time * 0.01 + i * 0.1) * 50;
        const z2 = Math.sin(time * 0.01 + i * 0.1 + 5) * 50;
        
        const projectedX1 = centerX + (x * perspective) / (perspective + z1);
        const projectedX2 = centerX + (x * perspective) / (perspective + z2);
        const projectedY1 = centerY - 250;
        const projectedY2 = centerY + 250;
        
        ctx.beginPath();
        ctx.moveTo(projectedX1, projectedY1);
        ctx.lineTo(projectedX2, projectedY2);
        ctx.stroke();
      }

      time += 1;
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [enabled, intensity]);

  if (!enabled) return null;

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none z-0"
      style={{ opacity: 0.4 }}
    />
  );
};

// Floating Data Nodes
const DataNodes: React.FC<{ enabled: boolean; count: number }> = ({ enabled, count }) => {
  const [nodes, setNodes] = useState<Array<{
    id: number;
    x: number;
    y: number;
    z: number;
    vx: number;
    vy: number;
    vz: number;
    size: number;
    color: string;
    type: 'data' | 'process' | 'connection';
  }>>([]);

  useEffect(() => {
    if (!enabled) {
      setNodes([]);
      return;
    }

    const newNodes = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      z: Math.random() * 100,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      vz: (Math.random() - 0.5) * 0.2,
      size: Math.random() * 20 + 10,
      color: ['#00ffff', '#ff00ff', '#ffff00', '#00ff00'][Math.floor(Math.random() * 4)],
      type: ['data', 'process', 'connection'][Math.floor(Math.random() * 3)] as 'data' | 'process' | 'connection',
    }));

    setNodes(newNodes);

    const interval = setInterval(() => {
      setNodes(prev => prev.map(node => ({
        ...node,
        x: (node.x + node.vx + window.innerWidth) % window.innerWidth,
        y: (node.y + node.vy + window.innerHeight) % window.innerHeight,
        z: (node.z + node.vz + 100) % 100,
      })));
    }, 16);

    return () => clearInterval(interval);
  }, [enabled, count]);

  if (!enabled) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-10">
      {nodes.map((node) => {
        const scale = 1 + node.z / 100;
        const opacity = 0.7 - node.z / 200;
        
        return (
          <motion.div
            key={node.id}
            className="absolute"
            style={{
              left: node.x,
              top: node.y,
              transform: `scale(${scale})`,
              opacity,
            }}
            animate={{
              rotate: 360,
              scale: [scale, scale * 1.1, scale],
            }}
            transition={{
              rotate: { duration: 10, repeat: Infinity, ease: "linear" },
              scale: { duration: 2, repeat: Infinity, ease: "easeInOut" },
            }}
          >
            <div
              className="w-4 h-4 border-2 rounded-full"
              style={{
                borderColor: node.color,
                backgroundColor: `${node.color}20`,
                boxShadow: `0 0 20px ${node.color}`,
              }}
            >
              {node.type === 'data' && <Circle className="w-2 h-2 m-1" style={{ color: node.color }} />}
              {node.type === 'process' && <Cpu className="w-2 h-2 m-1" style={{ color: node.color }} />}
              {node.type === 'connection' && <Network className="w-2 h-2 m-1" style={{ color: node.color }} />}
            </div>
          </motion.div>
        );
      })}
    </div>
  );
};

// Holographic Text Effect
const HolographicText: React.FC<{ text: string; enabled: boolean }> = ({ text, enabled }) => {
  if (!enabled) return <span>{text}</span>;

  return (
    <motion.span
      className="relative inline-block"
      animate={{
        textShadow: [
          '0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff',
          '0 0 5px #ff00ff, 0 0 10px #ff00ff, 0 0 15px #ff00ff',
          '0 0 5px #ffff00, 0 0 10px #ffff00, 0 0 15px #ffff00',
          '0 0 5px #00ffff, 0 0 10px #00ffff, 0 0 15px #00ffff',
        ],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      {text}
      <motion.span
        className="absolute inset-0 opacity-50"
        animate={{
          x: [0, 1, -1, 0],
          opacity: [0.5, 0.8, 0.3, 0.5],
        }}
        transition={{
          duration: 0.1,
          repeat: Infinity,
          repeatDelay: Math.random() * 2 + 1,
        }}
        style={{
          color: '#00ffff',
          filter: 'blur(0.5px)',
        }}
      >
        {text}
      </motion.span>
    </motion.span>
  );
};

// Energy Pulse Rings
const EnergyRings: React.FC<{ enabled: boolean; centerX: number; centerY: number }> = ({ 
  enabled, 
  centerX, 
  centerY 
}) => {
  if (!enabled) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-5">
      {Array.from({ length: 5 }).map((_, i) => (
        <motion.div
          key={i}
          className="absolute border-2 border-cyan-400 rounded-full"
          style={{
            left: centerX - 50,
            top: centerY - 50,
            width: 100,
            height: 100,
          }}
          animate={{
            scale: [1, 3, 1],
            opacity: [0.8, 0, 0.8],
            borderColor: ['#00ffff', '#ff00ff', '#ffff00', '#00ffff'],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: i * 0.6,
            ease: "easeOut",
          }}
        />
      ))}
    </div>
  );
};

// Quantum Field Effect
const QuantumField: React.FC<{ enabled: boolean }> = ({ enabled }) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    phase: number;
    frequency: number;
    amplitude: number;
  }>>([]);

  useEffect(() => {
    if (!enabled) {
      setParticles([]);
      return;
    }

    const newParticles = Array.from({ length: 100 }, (_, i) => ({
      id: i,
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      phase: Math.random() * Math.PI * 2,
      frequency: Math.random() * 0.02 + 0.01,
      amplitude: Math.random() * 10 + 5,
    }));

    setParticles(newParticles);
  }, [enabled]);

  if (!enabled) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute w-1 h-1 bg-cyan-400 rounded-full"
          style={{
            left: particle.x,
            top: particle.y,
            boxShadow: '0 0 10px #00ffff',
          }}
          animate={{
            x: [
              particle.x,
              particle.x + Math.sin(particle.phase) * particle.amplitude,
              particle.x,
            ],
            y: [
              particle.y,
              particle.y + Math.cos(particle.phase) * particle.amplitude,
              particle.y,
            ],
            opacity: [0.3, 1, 0.3],
          }}
          transition={{
            duration: 1 / particle.frequency,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};

// Main Holographic Interface Component
export const HolographicInterface: React.FC<HolographicInterfaceProps> = ({
  enabled,
  intensity,
  className,
}) => {
  const [settings, setSettings] = useState({
    grid: true,
    dataNodes: true,
    energyRings: false,
    quantumField: false,
    nodeCount: 20,
  });

  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePos({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  if (!enabled) return null;

  return (
    <div className={`fixed inset-0 pointer-events-none ${className}`}>
      {/* Background Effects */}
      <HolographicGrid enabled={settings.grid} intensity={intensity} />
      <DataNodes enabled={settings.dataNodes} count={settings.nodeCount} />
      <EnergyRings enabled={settings.energyRings} centerX={mousePos.x} centerY={mousePos.y} />
      <QuantumField enabled={settings.quantumField} />

      {/* Control Panel */}
      <motion.div
        className="fixed top-20 right-4 z-50 pointer-events-auto"
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
      >
        <Card className="p-3 bg-background/90 backdrop-blur-md border border-cyan-400/30">
          <CardContent className="space-y-3 p-0">
            <div className="flex items-center gap-2">
              <Atom className="h-4 w-4 text-cyan-400" />
              <HolographicText text="Holographic Interface" enabled={true} />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs">Grid</Label>
                <Switch
                  checked={settings.grid}
                  onCheckedChange={(grid) => setSettings(prev => ({ ...prev, grid }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Data Nodes</Label>
                <Switch
                  checked={settings.dataNodes}
                  onCheckedChange={(dataNodes) => setSettings(prev => ({ ...prev, dataNodes }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Energy Rings</Label>
                <Switch
                  checked={settings.energyRings}
                  onCheckedChange={(energyRings) => setSettings(prev => ({ ...prev, energyRings }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label className="text-xs">Quantum Field</Label>
                <Switch
                  checked={settings.quantumField}
                  onCheckedChange={(quantumField) => setSettings(prev => ({ ...prev, quantumField }))}
                />
              </div>

              {settings.dataNodes && (
                <div>
                  <Label className="text-xs">Node Count: {settings.nodeCount}</Label>
                  <Slider
                    value={[settings.nodeCount]}
                    onValueChange={([nodeCount]) => setSettings(prev => ({ ...prev, nodeCount }))}
                    max={50}
                    min={5}
                    step={5}
                    className="mt-1"
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Holographic Overlay */}
      <div className="fixed inset-0 bg-gradient-to-br from-cyan-500/5 via-transparent to-purple-500/5 pointer-events-none" />
    </div>
  );
};