import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Zap, CheckCircle, AlertTriangle, Info } from 'lucide-react';

interface PerformanceIssue {
  id: string;
  type: 'animation' | 'memory' | 'render' | 'network';
  severity: 'low' | 'medium' | 'high';
  description: string;
  fix: string;
  autoFixable: boolean;
}

interface PerformanceOptimizerProps {
  onClose: () => void;
}

export const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = ({ onClose }) => {
  const [issues, setIssues] = useState<PerformanceIssue[]>([]);
  const [scanning, setScanning] = useState(true);
  const [fixedIssues, setFixedIssues] = useState<string[]>([]);

  useEffect(() => {
    // Simulate performance scanning
    const timer = setTimeout(() => {
      setIssues([
        {
          id: 'hover-flicker',
          type: 'animation',
          severity: 'high',
          description: 'Hover animations causing layout shifts and flickering',
          fix: 'Replace scale transforms with translate3d for hardware acceleration',
          autoFixable: true
        },
        {
          id: 'shimmer-performance',
          type: 'animation',
          severity: 'medium',
          description: 'Shimmer effects triggering unnecessary repaints',
          fix: 'Use optimized shimmer with transform3d and will-change',
          autoFixable: true
        },
        {
          id: 'framer-conflicts',
          type: 'animation',
          severity: 'medium',
          description: 'Framer Motion conflicts with CSS transitions',
          fix: 'Standardize on hardware-accelerated animations',
          autoFixable: true
        }
      ]);
      setScanning(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const handleAutoFix = (issueId: string) => {
    setFixedIssues(prev => [...prev, issueId]);
    // In a real implementation, this would apply the actual fixes
  };

  const severityColors = {
    low: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-red-100 text-red-800'
  };

  const typeIcons = {
    animation: <Zap className="w-4 h-4" />,
    memory: <AlertTriangle className="w-4 h-4" />,
    render: <Info className="w-4 h-4" />,
    network: <CheckCircle className="w-4 h-4" />
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
      >
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold flex items-center gap-2">
                <Zap className="w-6 h-6 text-blue-500" />
                Performance Optimizer
              </h2>
              <p className="text-muted-foreground mt-1">
                Automatically detect and fix performance issues
              </p>
            </div>
            <Button variant="ghost" onClick={onClose}>×</Button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(80vh-200px)]">
          {scanning ? (
            <div className="text-center py-8">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="inline-block"
              >
                <Zap className="w-8 h-8 text-blue-500" />
              </motion.div>
              <p className="mt-4 text-muted-foreground">Scanning for performance issues...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">
                  Found {issues.length} performance issues
                </h3>
                <Button
                  onClick={() => issues.forEach(issue => issue.autoFixable && handleAutoFix(issue.id))}
                  disabled={issues.every(issue => fixedIssues.includes(issue.id))}
                >
                  Auto-fix All
                </Button>
              </div>

              {issues.map((issue) => (
                <Card key={issue.id} className="hover-lift">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {typeIcons[issue.type]}
                        <CardTitle className="text-base">{issue.description}</CardTitle>
                      </div>
                      <Badge className={severityColors[issue.severity]}>
                        {issue.severity}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">
                      <strong>Fix:</strong> {issue.fix}
                    </p>
                    
                    {fixedIssues.includes(issue.id) ? (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="w-4 h-4" />
                        <span className="text-sm">Fixed!</span>
                      </div>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => handleAutoFix(issue.id)}
                        disabled={!issue.autoFixable}
                      >
                        {issue.autoFixable ? 'Auto-fix' : 'Manual fix required'}
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};