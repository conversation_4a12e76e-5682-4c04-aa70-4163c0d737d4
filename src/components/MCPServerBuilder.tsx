import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Code,
  Database,
  FileText,
  Network,
  Settings,
  Plus,
  Trash2,
  Copy,
  Download,
  Play
} from 'lucide-react';

interface MCPServerConfig {
  name: string;
  type: 'filesystem' | 'git' | 'database' | 'api' | 'custom';
  description: string;
  capabilities: string[];
  config: Record<string, any>;
  authentication?: {
    type: 'none' | 'basic' | 'bearer' | 'oauth';
    credentials?: Record<string, string>;
  };
  endpoints: Array<{
    name: string;
    method: string;
    path: string;
    description: string;
  }>;
  validation: {
    required: string[];
    optional: string[];
  };
}

export function MCPServerBuilder() {
  const [serverConfig, setServerConfig] = useState<MCPServerConfig>({
    name: '',
    type: 'custom',
    description: '',
    capabilities: [],
    config: {},
    authentication: { type: 'none' },
    endpoints: [],
    validation: { required: [], optional: [] }
  });

  const [newCapability, setNewCapability] = useState('');
  const [newEndpoint, setNewEndpoint] = useState({
    name: '',
    method: 'GET',
    path: '',
    description: ''
  });

  const serverTypes = [
    { value: 'filesystem', label: 'Filesystem', icon: FileText },
    { value: 'git', label: 'Git Repository', icon: Code },
    { value: 'database', label: 'Database', icon: Database },
    { value: 'api', label: 'REST API', icon: Network },
    { value: 'custom', label: 'Custom', icon: Settings }
  ];

  const authTypes = [
    { value: 'none', label: 'No Authentication' },
    { value: 'basic', label: 'Basic Auth' },
    { value: 'bearer', label: 'Bearer Token' },
    { value: 'oauth', label: 'OAuth 2.0' }
  ];

  const httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];

  const addCapability = () => {
    if (newCapability && !serverConfig.capabilities.includes(newCapability)) {
      setServerConfig(prev => ({
        ...prev,
        capabilities: [...prev.capabilities, newCapability]
      }));
      setNewCapability('');
    }
  };

  const removeCapability = (capability: string) => {
    setServerConfig(prev => ({
      ...prev,
      capabilities: prev.capabilities.filter(c => c !== capability)
    }));
  };

  const addEndpoint = () => {
    if (newEndpoint.name && newEndpoint.path) {
      setServerConfig(prev => ({
        ...prev,
        endpoints: [...prev.endpoints, { ...newEndpoint }]
      }));
      setNewEndpoint({ name: '', method: 'GET', path: '', description: '' });
    }
  };

  const removeEndpoint = (index: number) => {
    setServerConfig(prev => ({
      ...prev,
      endpoints: prev.endpoints.filter((_, i) => i !== index)
    }));
  };

  const generateServerCode = () => {
    const code = `
// Generated MCP Server: ${serverConfig.name}
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

class ${serverConfig.name.replace(/\s+/g, '')}Server {
  constructor() {
    this.server = new Server(
      {
        name: "${serverConfig.name}",
        version: "1.0.0",
        description: "${serverConfig.description}"
      },
      {
        capabilities: {
          ${serverConfig.capabilities.map(cap => `${cap}: {}`).join(',\n          ')}
        }
      }
    );

    this.setupHandlers();
  }

  setupHandlers() {
    ${serverConfig.endpoints.map(endpoint => `
    // ${endpoint.description}
    this.server.setRequestHandler("${endpoint.name}", async (request) => {
      // TODO: Implement ${endpoint.name} handler
      return {
        success: true,
        data: {}
      };
    });`).join('\n')}
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log("${serverConfig.name} MCP Server running");
  }
}

const server = new ${serverConfig.name.replace(/\s+/g, '')}Server();
server.start().catch(console.error);
`;

    return code;
  };

  const downloadServerCode = () => {
    const code = generateServerCode();
    const blob = new Blob([code], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${serverConfig.name.replace(/\s+/g, '-').toLowerCase()}-server.js`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getTypeIcon = (type: string) => {
    const typeData = serverTypes.find(t => t.value === type);
    return typeData?.icon || Settings;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">MCP Server Builder</h2>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigator.clipboard.writeText(generateServerCode())}>
            <Copy className="w-4 h-4 mr-2" />
            Copy Code
          </Button>
          <Button onClick={downloadServerCode}>
            <Download className="w-4 h-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      <Tabs defaultValue="basic" className="space-y-4">
        <TabsList>
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="auth">Authentication</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Server Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="name">Server Name</Label>
                <Input
                  id="name"
                  value={serverConfig.name}
                  onChange={(e) => setServerConfig(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="My Custom MCP Server"
                />
              </div>

              <div>
                <Label htmlFor="type">Server Type</Label>
                <Select
                  value={serverConfig.type}
                  onValueChange={(value) => setServerConfig(prev => ({ ...prev, type: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {serverTypes.map((type) => {
                      const Icon = type.icon;
                      return (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <Icon className="w-4 h-4" />
                            {type.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={serverConfig.description}
                  onChange={(e) => setServerConfig(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what your MCP server does..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="capabilities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Server Capabilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newCapability}
                  onChange={(e) => setNewCapability(e.target.value)}
                  placeholder="e.g., read_file, write_file, query_database"
                  onKeyPress={(e) => e.key === 'Enter' && addCapability()}
                />
                <Button onClick={addCapability}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {serverConfig.capabilities.map((capability) => (
                  <Badge key={capability} variant="secondary" className="flex items-center gap-1">
                    {capability}
                    <button
                      onClick={() => removeCapability(capability)}
                      className="ml-1 hover:text-red-500"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>

              {serverConfig.capabilities.length === 0 && (
                <p className="text-sm text-muted-foreground">
                  Add capabilities that your server will support
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                <Input
                  value={newEndpoint.name}
                  onChange={(e) => setNewEndpoint(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Endpoint name"
                />
                <Select
                  value={newEndpoint.method}
                  onValueChange={(value) => setNewEndpoint(prev => ({ ...prev, method: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {httpMethods.map((method) => (
                      <SelectItem key={method} value={method}>
                        {method}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  value={newEndpoint.path}
                  onChange={(e) => setNewEndpoint(prev => ({ ...prev, path: e.target.value }))}
                  placeholder="/api/endpoint"
                />
                <Button onClick={addEndpoint}>
                  <Plus className="w-4 h-4" />
                </Button>
              </div>

              <Input
                value={newEndpoint.description}
                onChange={(e) => setNewEndpoint(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Endpoint description"
              />

              <div className="space-y-2">
                {serverConfig.endpoints.map((endpoint, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{endpoint.method}</Badge>
                        <span className="font-medium">{endpoint.name}</span>
                        <span className="text-sm text-muted-foreground">{endpoint.path}</span>
                      </div>
                      {endpoint.description && (
                        <p className="text-sm text-muted-foreground mt-1">{endpoint.description}</p>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => removeEndpoint(index)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="auth" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Authentication Type</Label>
                <Select
                  value={serverConfig.authentication?.type || 'none'}
                  onValueChange={(value) => setServerConfig(prev => ({
                    ...prev,
                    authentication: { ...prev.authentication, type: value as any }
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {authTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {serverConfig.authentication?.type !== 'none' && (
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    Configure authentication settings for your server
                  </p>
                  {/* Add authentication configuration fields based on type */}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="w-5 h-5" />
                Generated Server Code
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-lg overflow-x-auto text-sm">
                <code>{generateServerCode()}</code>
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}