import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useSpring, useTransform } from 'framer-motion';
import {
  <PERSON><PERSON><PERSON>,
  Zap,
  Brain,
  Eye,
  Palette,
  Wand2,
  Layers,
  Cpu,
  Activity,
  Waves,
  Star,
  Hexagon,
} from 'lucide-react';

// Particle System for Background
export const ParticleBackground: React.FC<{ intensity?: number; color?: string }> = ({
  intensity = 50,
  color = '#3b82f6'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
    }> = [];
    
    // Initialize particles
    for (let i = 0; i < intensity; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 2 + 1,
        opacity: Math.random() * 0.5 + 0.1,
      });
    }
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach((particle, i) => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = `${color}${Math.floor(particle.opacity * 255).toString(16).padStart(2, '0')}`;
        ctx.fill();
        
        // Connect nearby particles
        particles.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.strokeStyle = `${color}${Math.floor((1 - distance / 100) * 50).toString(16).padStart(2, '0')}`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        });
      });
      
      requestAnimationFrame(animate);
    };
    
    animate();
  }, [intensity, color]);
  
  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none"
      width={window.innerWidth}
      height={window.innerHeight}
    />
  );
};

// Animated Gradient Background
export const AnimatedGradientBackground: React.FC<{ theme?: 'aurora' | 'sunset' | 'ocean' | 'forest' }> = ({
  theme = 'aurora'
}) => {
  const gradients = {
    aurora: 'from-purple-400 via-pink-500 to-red-500',
    sunset: 'from-orange-400 via-red-500 to-pink-500',
    ocean: 'from-blue-400 via-cyan-500 to-teal-500',
    forest: 'from-green-400 via-emerald-500 to-teal-500',
  };
  
  return (
    <motion.div
      className={`absolute inset-0 bg-gradient-to-br ${gradients[theme]} opacity-10`}
      animate={{
        background: [
          `linear-gradient(45deg, ${gradients[theme]})`,
          `linear-gradient(90deg, ${gradients[theme]})`,
          `linear-gradient(135deg, ${gradients[theme]})`,
          `linear-gradient(180deg, ${gradients[theme]})`,
          `linear-gradient(45deg, ${gradients[theme]})`,
        ],
      }}
      transition={{
        duration: 10,
        repeat: Infinity,
        ease: "linear",
      }}
    />
  );
};

// Morphing Shapes
export const MorphingShape: React.FC<{ size?: number; color?: string }> = ({
  size = 100,
  color = '#3b82f6'
}) => {
  return (
    <motion.div
      className="absolute"
      style={{ width: size, height: size }}
      animate={{
        borderRadius: [
          "20% 80% 80% 20%",
          "80% 20% 20% 80%",
          "50% 50% 50% 50%",
          "20% 80% 80% 20%",
        ],
        rotate: [0, 90, 180, 270, 360],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      style={{
        background: `linear-gradient(45deg, ${color}20, ${color}40)`,
        backdropFilter: 'blur(10px)',
      }}
    />
  );
};

// Floating Elements
export const FloatingElements: React.FC = () => {
  const elements = Array.from({ length: 6 }, (_, i) => ({
    id: i,
    icon: [Sparkles, Zap, Brain, Eye, Palette, Wand2][i],
    delay: i * 0.5,
  }));
  
  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {elements.map(({ id, icon: Icon, delay }) => (
        <motion.div
          key={id}
          className="absolute"
          initial={{ y: "100vh", x: Math.random() * window.innerWidth }}
          animate={{
            y: "-100px",
            x: Math.random() * window.innerWidth,
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            delay,
            ease: "linear",
          }}
        >
          <motion.div
            animate={{
              rotate: 360,
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            className="text-primary/20"
          >
            <Icon size={24 + Math.random() * 16} />
          </motion.div>
        </motion.div>
      ))}
    </div>
  );
};

// Neural Network Visualization
export const NeuralNetworkViz: React.FC<{ nodes?: number; connections?: number }> = ({
  nodes = 20,
  connections = 30
}) => {
  const [networkNodes, setNetworkNodes] = useState<Array<{
    id: number;
    x: number;
    y: number;
    active: boolean;
  }>>([]);
  
  useEffect(() => {
    const newNodes = Array.from({ length: nodes }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      active: false,
    }));
    setNetworkNodes(newNodes);
    
    // Animate nodes randomly
    const interval = setInterval(() => {
      setNetworkNodes(prev => prev.map(node => ({
        ...node,
        active: Math.random() > 0.7,
      })));
    }, 1000);
    
    return () => clearInterval(interval);
  }, [nodes]);
  
  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg className="w-full h-full opacity-30">
        {/* Connections */}
        {Array.from({ length: connections }, (_, i) => {
          const node1 = networkNodes[Math.floor(Math.random() * networkNodes.length)];
          const node2 = networkNodes[Math.floor(Math.random() * networkNodes.length)];
          if (!node1 || !node2) return null;
          
          return (
            <motion.line
              key={i}
              x1={`${node1.x}%`}
              y1={`${node1.y}%`}
              x2={`${node2.x}%`}
              y2={`${node2.y}%`}
              stroke="currentColor"
              strokeWidth="1"
              className="text-primary/20"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: i * 0.1 }}
            />
          );
        })}
        
        {/* Nodes */}
        {networkNodes.map(node => (
          <motion.circle
            key={node.id}
            cx={`${node.x}%`}
            cy={`${node.y}%`}
            r="3"
            fill="currentColor"
            className={node.active ? "text-primary" : "text-primary/40"}
            animate={{
              scale: node.active ? [1, 1.5, 1] : 1,
              opacity: node.active ? [0.4, 1, 0.4] : 0.4,
            }}
            transition={{ duration: 0.5 }}
          />
        ))}
      </svg>
    </div>
  );
};

// Holographic Card Effect
export const HolographicCard: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className = "" }) => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);
  
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;
    
    setMousePosition({ x, y });
  };
  
  return (
    <motion.div
      ref={cardRef}
      className={`relative overflow-hidden rounded-lg ${className}`}
      onMouseMove={handleMouseMove}
      whileHover={{ scale: 1.02 }}
      style={{
        background: `
          radial-gradient(
            circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%,
            rgba(59, 130, 246, 0.1) 0%,
            transparent 50%
          ),
          linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)
        `,
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
      }}
    >
      {/* Holographic shine effect */}
      <motion.div
        className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300"
        style={{
          background: `
            linear-gradient(
              ${Math.atan2(mousePosition.y - 0.5, mousePosition.x - 0.5) * 180 / Math.PI + 90}deg,
              transparent 30%,
              rgba(255,255,255,0.2) 50%,
              transparent 70%
            )
          `,
        }}
      />
      {children}
    </motion.div>
  );
};

// Pulse Wave Effect
export const PulseWave: React.FC<{ intensity?: number; color?: string }> = ({
  intensity = 3,
  color = '#3b82f6'
}) => {
  return (
    <div className="absolute inset-0 pointer-events-none flex items-center justify-center">
      {Array.from({ length: intensity }, (_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full border-2"
          style={{ borderColor: color }}
          initial={{ scale: 0, opacity: 1 }}
          animate={{
            scale: [0, 2, 4],
            opacity: [1, 0.5, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: i * 0.5,
            ease: "easeOut",
          }}
        />
      ))}
    </div>
  );
};

// Matrix Rain Effect
export const MatrixRain: React.FC<{ density?: number }> = ({ density = 20 }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const fontSize = 14;
    const columns = Math.floor(canvas.width / fontSize);
    const drops: number[] = Array(columns).fill(0);
    
    const draw = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      ctx.fillStyle = '#0f0';
      ctx.font = `${fontSize}px monospace`;
      
      drops.forEach((drop, i) => {
        const char = chars[Math.floor(Math.random() * chars.length)];
        ctx.fillText(char, i * fontSize, drop * fontSize);
        
        if (drop * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
        drops[i]++;
      });
    };
    
    const interval = setInterval(draw, 50);
    return () => clearInterval(interval);
  }, [density]);
  
  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 pointer-events-none opacity-20"
      width={window.innerWidth}
      height={window.innerHeight}
    />
  );
};

// Geometric Pattern Overlay
export const GeometricPattern: React.FC<{ pattern?: 'hexagons' | 'triangles' | 'circles' }> = ({
  pattern = 'hexagons'
}) => {
  const patterns = {
    hexagons: (
      <pattern id="hexagons" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
        <polygon
          points="30,5 50,20 50,40 30,55 10,40 10,20"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          opacity="0.1"
        />
      </pattern>
    ),
    triangles: (
      <pattern id="triangles" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
        <polygon
          points="20,5 35,30 5,30"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          opacity="0.1"
        />
      </pattern>
    ),
    circles: (
      <pattern id="circles" x="0" y="0" width="30" height="30" patternUnits="userSpaceOnUse">
        <circle
          cx="15"
          cy="15"
          r="10"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          opacity="0.1"
        />
      </pattern>
    ),
  };
  
  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg className="w-full h-full text-primary">
        <defs>{patterns[pattern]}</defs>
        <rect width="100%" height="100%" fill={`url(#${pattern})`} />
      </svg>
    </div>
  );
};