import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, Network, AlertCircle, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { api, type MCPServer } from "@/lib/api";
import { MCPServerList } from "./MCPServerList";
import { MCPAddServer } from "./MCPAddServer";
import { MCPImportExport } from "./MCPImportExport";
import { toast } from "sonner";

interface MCPManagerProps {
  onBack: () => void;
  className?: string;
}

export const MCPManager: React.FC<MCPManagerProps> = ({ onBack, className }) => {
  const [activeTab, setActiveTab] = useState("servers");
  const [servers, setServers] = useState<MCPServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadServers();
  }, []);

  const loadServers = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await api.mcpList();
      if (result.success && result.servers) {
        setServers(result.servers);
      } else {
        setError(result.message || "Failed to load servers");
      }
    } catch (err) {
      console.error("Failed to load MCP servers:", err);
      setError("Failed to load MCP servers");
    } finally {
      setLoading(false);
    }
  };

  const handleAddServer = async (name: string, jsonConfig: string, scope: string) => {
    try {
      const result = await api.mcpAddJson(name, jsonConfig, scope);
      if (result.success) {
        toast.success(`Server "${name}" added successfully`);
        await loadServers();
      } else {
        throw new Error(result.message || "Failed to add server");
      }
    } catch (error) {
      console.error("Failed to add server:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add server");
    }
  };

  const handleRemoveServer = async (name: string) => {
    try {
      const result = await api.mcpRemove(name);
      if (result.success) {
        toast.success(`Server "${name}" removed`);
        await loadServers();
      } else {
        throw new Error(result.message || "Failed to remove server");
      }
    } catch (error) {
      console.error("Failed to remove server:", error);
      toast.error(error instanceof Error ? error.message : "Failed to remove server");
    }
  };

  const handleImport = async (source: "claude-desktop" | "file", path?: string) => {
    try {
      let result;
      if (source === "claude-desktop") {
        result = await api.mcpAddFromClaudeDesktop();
      } else if (path) {
        // Import from file - would need additional API method
        toast.error("File import not yet implemented");
        return;
      }
      
      if (result?.success) {
        toast.success("Servers imported successfully");
        await loadServers();
      } else {
        throw new Error(result?.message || "Failed to import servers");
      }
    } catch (error) {
      console.error("Failed to import servers:", error);
      toast.error(error instanceof Error ? error.message : "Failed to import servers");
    }
  };

  const handleExport = async () => {
    try {
      const data = JSON.stringify(servers, null, 2);
      const blob = new Blob([data], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "mcp-servers.json";
      a.click();
      URL.revokeObjectURL(url);
      toast.success("Servers exported successfully");
    } catch (error) {
      console.error("Failed to export servers:", error);
      toast.error("Failed to export servers");
    }
  };

  return (
    <div className={`flex flex-col h-full bg-background text-foreground ${className || ""}`}>
      <div className="max-w-5xl mx-auto w-full flex flex-col h-full">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex items-center justify-between p-4 border-b border-border"
        >
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="icon" onClick={onBack} className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h2 className="text-lg font-semibold flex items-center gap-2">
                <Network className="h-5 w-5 text-blue-500" />
                MCP Servers
              </h2>
              <p className="text-xs text-muted-foreground">
                Manage Model Context Protocol servers
              </p>
            </div>
          </div>
        </motion.div>

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mx-4 mt-4 p-3 rounded-lg bg-destructive/10 border border-destructive/50 flex items-center gap-2 text-sm text-destructive"
          >
            <AlertCircle className="h-4 w-4" />
            {error}
          </motion.div>
        )}

        {/* Main Content */}
        {loading ? (
          <div className="flex-1 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex flex-col h-full">
              <TabsList className="mx-4 mt-4">
                <TabsTrigger value="servers">Servers</TabsTrigger>
                <TabsTrigger value="add">Add Server</TabsTrigger>
                <TabsTrigger value="import-export">Import/Export</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-y-auto">
                <TabsContent value="servers" className="h-full p-4">
                  <MCPServerList
                    servers={servers}
                    onServerRemoved={handleRemoveServer}
                    onRefresh={loadServers}
                  />
                </TabsContent>

                <TabsContent value="add" className="h-full p-4">
                  <MCPAddServer onServerAdded={handleAddServer} />
                </TabsContent>

                <TabsContent value="import-export" className="h-full p-4">
                  <MCPImportExport
                    onImport={handleImport}
                    onExport={handleExport}
                  />
                </TabsContent>
              </div>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
}