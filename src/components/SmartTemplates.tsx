import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { api } from '@/lib/api';
import { motion } from 'framer-motion';
import { 
  Code, 
  Database, 
  Plus,
  Edit,
  Trash2,
  Star,
  StarOff,
  Search,
  Filter,
  Sparkles,
  Package,
  Shield,
  Cpu,
  GitBranch,
  Terminal,
  Bug,
  BookOpen,
  Loader2,
  XCircle
} from 'lucide-react';

interface SmartTemplatesProps {
  onSelectTemplate: (template: Template) => void;
  onClose?: () => void;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  icon: string;
  tags: string[];
  content: string;
  variables: TemplateVariable[];
  examples: TemplateExample[];
  isFavorite: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
  author: string;
  isPublic: boolean;
}

interface TemplateVariable {
  name: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  default?: any;
  required: boolean;
  placeholder?: string;
}

interface TemplateExample {
  title: string;
  values: Record<string, any>;
  output: string;
}

type TemplateCategory = 
  | 'code-generation'
  | 'debugging'
  | 'documentation'
  | 'testing'
  | 'refactoring'
  | 'security'
  | 'performance'
  | 'architecture'
  | 'data-processing'
  | 'devops'
  | 'custom';

const categoryIcons: Record<TemplateCategory, any> = {
  'code-generation': Code,
  'debugging': Bug,
  'documentation': BookOpen,
  'testing': Shield,
  'refactoring': GitBranch,
  'security': Shield,
  'performance': Cpu,
  'architecture': Package,
  'data-processing': Database,
  'devops': Terminal,
  'custom': Sparkles
};

export function SmartTemplates({ onSelectTemplate, onClose }: SmartTemplatesProps) {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('library');
  
  // Form state for creating/editing templates
  const [formData, setFormData] = useState<Partial<Template>>({
    name: '',
    description: '',
    category: 'custom',
    content: '',
    tags: [],
    variables: [],
    examples: [],
    isPublic: false
  });

  useEffect(() => {
    loadTemplates();
  }, []);

  useEffect(() => {
    filterTemplates();
  }, [templates, selectedCategory, searchQuery]);

  const loadTemplates = async () => {
    setIsLoading(true);
    try {
      // Load built-in templates
      const builtInTemplates = getBuiltInTemplates();
      // Load user templates from API
      const userTemplates = await api.getSmartTemplates();
      setTemplates([...builtInTemplates, ...userTemplates]);
    } catch (error) {
      console.error('Failed to load templates:', error);
      setTemplates(getBuiltInTemplates());
    } finally {
      setIsLoading(false);
    }
  };

  const filterTemplates = () => {
    let filtered = templates;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(t => t.category === selectedCategory);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(query) ||
        t.description.toLowerCase().includes(query) ||
        t.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    setFilteredTemplates(filtered);
  };

  const handleCreateTemplate = async () => {
    try {
      const newTemplate = await api.createSmartTemplate(formData as Omit<Template, 'id'>);
      setTemplates([...templates, newTemplate]);
      setShowCreateDialog(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create template:', error);
    }
  };

  const handleUpdateTemplate = async () => {
    if (!selectedTemplate) return;
    
    try {
      const updatedTemplate = await api.updateSmartTemplate(selectedTemplate.id, formData);
      setTemplates(templates.map(t => t.id === selectedTemplate.id ? updatedTemplate : t));
      setShowEditDialog(false);
      resetForm();
    } catch (error) {
      console.error('Failed to update template:', error);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    try {
      await api.deleteSmartTemplate(templateId);
      setTemplates(templates.filter(t => t.id !== templateId));
    } catch (error) {
      console.error('Failed to delete template:', error);
    }
  };

  const handleToggleFavorite = async (templateId: string) => {
    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) return;
      
      const updated = await api.toggleTemplateFavorite(templateId, !template.isFavorite);
      setTemplates(templates.map(t => t.id === templateId ? updated : t));
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  const handleUseTemplate = (template: Template) => {
    // Track usage
    api.trackTemplateUsage(template.id);
    onSelectTemplate(template);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: 'custom',
      content: '',
      tags: [],
      variables: [],
      examples: [],
      isPublic: false
    });
    setSelectedTemplate(null);
  };

  const getBuiltInTemplates = (): Template[] => {
    return [
      {
        id: 'builtin-1',
        name: 'React Component Generator',
        description: 'Generate a complete React component with TypeScript, hooks, and styling',
        category: 'code-generation',
        icon: 'react',
        tags: ['react', 'typescript', 'component', 'frontend'],
        content: `Create a React component named {{componentName}} with the following requirements:
- TypeScript support
- Use functional component with hooks
- Include props interface
- Add {{styling}} styling
- Include {{features}} features
- Add proper error handling
- Include unit tests

Component purpose: {{purpose}}`,
        variables: [
          { name: 'componentName', description: 'Name of the component', type: 'string', required: true },
          { name: 'styling', description: 'Styling approach (CSS, Tailwind, styled-components)', type: 'string', default: 'Tailwind', required: false },
          { name: 'features', description: 'List of features to include', type: 'array', required: true },
          { name: 'purpose', description: 'What the component does', type: 'string', required: true }
        ],
        examples: [
          {
            title: 'User Profile Card',
            values: {
              componentName: 'UserProfileCard',
              styling: 'Tailwind CSS',
              features: ['avatar display', 'edit mode', 'social links'],
              purpose: 'Display user profile information with editing capabilities'
            },
            output: 'A complete UserProfileCard component with all specified features'
          }
        ],
        isFavorite: true,
        usageCount: 42,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        author: 'Claudia Team',
        isPublic: true
      },
      {
        id: 'builtin-2',
        name: 'API Endpoint Builder',
        description: 'Create RESTful API endpoints with validation, error handling, and documentation',
        category: 'code-generation',
        icon: 'api',
        tags: ['api', 'rest', 'backend', 'nodejs'],
        content: `Create a {{method}} API endpoint at {{path}} that:
- Handles {{dataType}} data
- Includes input validation for: {{validationRules}}
- Implements {{authentication}} authentication
- Returns {{responseFormat}} format
- Includes error handling for common scenarios
- Add OpenAPI/Swagger documentation

Business logic: {{businessLogic}}`,
        variables: [
          { name: 'method', description: 'HTTP method', type: 'string', required: true, default: 'POST' },
          { name: 'path', description: 'API endpoint path', type: 'string', required: true },
          { name: 'dataType', description: 'Type of data being handled', type: 'string', required: true },
          { name: 'validationRules', description: 'Validation requirements', type: 'array', required: true },
          { name: 'authentication', description: 'Auth method (JWT, API Key, OAuth)', type: 'string', default: 'JWT', required: false },
          { name: 'responseFormat', description: 'Response format', type: 'string', default: 'JSON', required: false },
          { name: 'businessLogic', description: 'Core business logic', type: 'string', required: true }
        ],
        examples: [],
        isFavorite: false,
        usageCount: 28,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        author: 'Claudia Team',
        isPublic: true
      },
      {
        id: 'builtin-3',
        name: 'Debug Assistant',
        description: 'Systematic debugging approach for complex issues',
        category: 'debugging',
        icon: 'bug',
        tags: ['debug', 'troubleshooting', 'analysis'],
        content: `Help me debug this issue:

Error/Problem: {{errorDescription}}
Expected behavior: {{expectedBehavior}}
Actual behavior: {{actualBehavior}}
Steps to reproduce: {{reproductionSteps}}
Environment: {{environment}}

Please:
1. Analyze the potential root causes
2. Suggest debugging steps
3. Provide code snippets to help diagnose
4. Recommend fixes with explanations`,
        variables: [
          { name: 'errorDescription', description: 'Describe the error or issue', type: 'string', required: true },
          { name: 'expectedBehavior', description: 'What should happen', type: 'string', required: true },
          { name: 'actualBehavior', description: 'What actually happens', type: 'string', required: true },
          { name: 'reproductionSteps', description: 'Steps to reproduce', type: 'array', required: true },
          { name: 'environment', description: 'Tech stack and environment', type: 'string', required: true }
        ],
        examples: [],
        isFavorite: true,
        usageCount: 67,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        author: 'Claudia Team',
        isPublic: true
      },
      {
        id: 'builtin-4',
        name: 'Performance Optimizer',
        description: 'Analyze and optimize code performance',
        category: 'performance',
        icon: 'zap',
        tags: ['performance', 'optimization', 'profiling'],
        content: `Analyze and optimize the performance of:

Code/Component: {{codeSection}}
Current performance metrics: {{currentMetrics}}
Target metrics: {{targetMetrics}}
Constraints: {{constraints}}

Please provide:
1. Performance bottleneck analysis
2. Optimization strategies
3. Refactored code with explanations
4. Benchmark comparisons
5. Trade-offs and recommendations`,
        variables: [
          { name: 'codeSection', description: 'Code or component to optimize', type: 'string', required: true },
          { name: 'currentMetrics', description: 'Current performance metrics', type: 'string', required: true },
          { name: 'targetMetrics', description: 'Target performance goals', type: 'string', required: true },
          { name: 'constraints', description: 'Any constraints or limitations', type: 'string', required: false }
        ],
        examples: [],
        isFavorite: false,
        usageCount: 35,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        author: 'Claudia Team',
        isPublic: true
      },
      {
        id: 'builtin-5',
        name: 'Test Suite Generator',
        description: 'Generate comprehensive test suites for your code',
        category: 'testing',
        icon: 'check',
        tags: ['testing', 'unit-tests', 'integration-tests', 'tdd'],
        content: `Generate a comprehensive test suite for:

Code to test: {{codeToTest}}
Testing framework: {{framework}}
Coverage target: {{coverageTarget}}%
Test types needed: {{testTypes}}

Include:
- Unit tests for all functions/methods
- Edge cases and error scenarios
- {{additionalRequirements}}
- Mock implementations where needed
- Clear test descriptions`,
        variables: [
          { name: 'codeToTest', description: 'Code that needs testing', type: 'string', required: true },
          { name: 'framework', description: 'Testing framework (Jest, Mocha, etc.)', type: 'string', default: 'Jest', required: false },
          { name: 'coverageTarget', description: 'Target coverage percentage', type: 'number', default: 80, required: false },
          { name: 'testTypes', description: 'Types of tests needed', type: 'array', default: ['unit', 'integration'], required: false },
          { name: 'additionalRequirements', description: 'Any additional requirements', type: 'string', required: false }
        ],
        examples: [],
        isFavorite: false,
        usageCount: 52,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        author: 'Claudia Team',
        isPublic: true
      }
    ];
  };

  const renderTemplateCard = (template: Template) => {
    const Icon = categoryIcons[template.category];
    
    return (
      <motion.div
        key={template.id}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.02 }}
        className="relative"
      >
        <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-2">
                <Icon className="w-5 h-5 text-primary" />
                <CardTitle className="text-base">{template.name}</CardTitle>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleToggleFavorite(template.id);
                  }}
                >
                  {template.isFavorite ? 
                    <Star className="w-4 h-4 fill-current text-yellow-500" /> : 
                    <StarOff className="w-4 h-4" />
                  }
                </Button>
                {!template.id.startsWith('builtin-') && (
                  <>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedTemplate(template);
                        setFormData(template);
                        setShowEditDialog(true);
                      }}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteTemplate(template.id);
                      }}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <CardDescription className="line-clamp-2">
              {template.description}
            </CardDescription>
            <div className="flex flex-wrap gap-1">
              {template.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {template.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{template.tags.length - 3}
                </Badge>
              )}
            </div>
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{template.variables.length} variables</span>
              <span>{template.usageCount} uses</span>
            </div>
            <Button
              className="w-full"
              size="sm"
              onClick={() => handleUseTemplate(template)}
            >
              Use Template
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Smart Templates</h2>
        {onClose && (
          <Button variant="ghost" size="icon" onClick={onClose}>
            <XCircle className="w-5 h-5" />
          </Button>
        )}
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="library">Template Library</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
          <TabsTrigger value="recent">Recently Used</TabsTrigger>
        </TabsList>
        
        <div className="mt-4 space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select value={selectedCategory} onValueChange={(v) => setSelectedCategory(v as any)}>
              <SelectTrigger className="w-[200px]">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="code-generation">Code Generation</SelectItem>
                <SelectItem value="debugging">Debugging</SelectItem>
                <SelectItem value="documentation">Documentation</SelectItem>
                <SelectItem value="testing">Testing</SelectItem>
                <SelectItem value="refactoring">Refactoring</SelectItem>
                <SelectItem value="security">Security</SelectItem>
                <SelectItem value="performance">Performance</SelectItem>
                <SelectItem value="architecture">Architecture</SelectItem>
                <SelectItem value="data-processing">Data Processing</SelectItem>
                <SelectItem value="devops">DevOps</SelectItem>
                <SelectItem value="custom">Custom</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create
            </Button>
          </div>
        </div>
        
        <TabsContent value="library" className="flex-1 overflow-auto mt-4">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="w-6 h-6 animate-spin" />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTemplates.map(renderTemplateCard)}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="favorites" className="flex-1 overflow-auto mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.filter(t => t.isFavorite).map(renderTemplateCard)}
          </div>
        </TabsContent>
        
        <TabsContent value="recent" className="flex-1 overflow-auto mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates
              .sort((a, b) => b.usageCount - a.usageCount)
              .slice(0, 9)
              .map(renderTemplateCard)}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Create/Edit Dialog */}
      <Dialog open={showCreateDialog || showEditDialog} onOpenChange={(open) => {
        if (!open) {
          setShowCreateDialog(false);
          setShowEditDialog(false);
          resetForm();
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {showCreateDialog ? 'Create New Template' : 'Edit Template'}
            </DialogTitle>
            <DialogDescription>
              Create reusable templates for common tasks
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="name">Template Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., React Component Generator"
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="What does this template do?"
                rows={2}
              />
            </div>
            
            <div>
              <Label htmlFor="category">Category</Label>
              <Select 
                value={formData.category} 
                onValueChange={(v) => setFormData({ ...formData, category: v as TemplateCategory })}
              >
                <SelectTrigger id="category">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="code-generation">Code Generation</SelectItem>
                  <SelectItem value="debugging">Debugging</SelectItem>
                  <SelectItem value="documentation">Documentation</SelectItem>
                  <SelectItem value="testing">Testing</SelectItem>
                  <SelectItem value="refactoring">Refactoring</SelectItem>
                  <SelectItem value="security">Security</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                  <SelectItem value="architecture">Architecture</SelectItem>
                  <SelectItem value="data-processing">Data Processing</SelectItem>
                  <SelectItem value="devops">DevOps</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="content">Template Content</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                placeholder="Use {{variableName}} for dynamic values"
                rows={6}
                className="font-mono"
              />
            </div>
            
            <div>
              <Label htmlFor="tags">Tags (comma-separated)</Label>
              <Input
                id="tags"
                value={formData.tags?.join(', ')}
                onChange={(e) => setFormData({ 
                  ...formData, 
                  tags: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                })}
                placeholder="react, component, typescript"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowCreateDialog(false);
              setShowEditDialog(false);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={showCreateDialog ? handleCreateTemplate : handleUpdateTemplate}>
              {showCreateDialog ? 'Create Template' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}