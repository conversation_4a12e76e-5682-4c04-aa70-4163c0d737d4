import { useEffect, lazy, Suspense } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Loader2, Bot, Code2, Network } from "lucide-react";
import { Card } from "@/components/ui/card";
import { OutputCacheProvider } from "@/lib/outputCache";
import { But<PERSON> } from "@/components/ui/button";
import { ProjectList } from "@/components/ProjectList";
import { SessionList } from "@/components/SessionList";
import { RunningClaudeSessions } from "@/components/RunningClaudeSessions";
import { Topbar } from "@/components/Topbar";
import { MarkdownEditor } from "@/components/MarkdownEditor";
import { ClaudeFileEditor } from "@/components/ClaudeFileEditor";
import { Settings } from "@/components/Settings";
import { CCAgents } from "@/components/CCAgents";
import { ClaudeCodeSession } from "@/components/ClaudeCodeSession";
import { UsageDashboard } from "@/components/UsageDashboard";
import { MCPManager } from "@/components/MCPManagerFixed";
import { NFOCredits } from "@/components/NFOCredits";
import { ClaudeBinaryDialog } from "@/components/ClaudeBinaryDialog";
// Toast functionality now handled by sonner
import { LoadingFallback } from "@/components/LoadingFallback";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { SkipLink } from "@/components/SkipLink";
import { useKeyboardNavigation } from "@/hooks/useKeyboardNavigation";
import { useFocusMode } from "@/hooks/useFocusMode";
import { useFeatureContext } from "@/hooks/useFeatureContext";
import { Toaster } from "sonner";
import { DynamicNavigationCards } from "@/components/DynamicNavigationCards";
import { QuickCustomization } from "@/components/QuickCustomization";
import { useUIStore } from "@/stores/uiStore";
import { useProjectStore } from "@/stores/projectStore";
import { useAppInitializer } from "@/hooks/useAppInitializer";
import { ParallelAgentsDemo } from "@/components/ParallelAgentsDemo";
import { EnhancedAgentDashboard } from "@/components/EnhancedAgentDashboard";
import { VisualEffectsManager } from "@/components/VisualEffectsManager";
import { AgentExecutionDemo } from "@/components/AgentExecutionDemo";
import { SmartCodeAssistant } from "@/components/SmartCodeAssistant";
import { SmartFeatureSelection } from "@/components/SmartFeatureSelection";
import { EnhancedWorkflowAutomation } from "@/components/EnhancedWorkflowAutomation";
import { EnhancedMCPManager } from "@/components/EnhancedMCPManager";
import { FeatureWrapper } from "@/components/FeatureWrapper";
import { BulkTaskManagerReal as BulkTaskManager } from "@/components/BulkTaskManagerReal";
import ParallelAgentsSystemIntegration from "@/components/parallel-agents/ParallelAgentsSystemIntegration";
import ContextEngineering from "@/components/ContextEngineering";

// Lazy load heavy components
const SmartTemplates = lazy(() => import("@/components/SmartTemplates").then(m => ({ default: m.SmartTemplates })));
const PerformanceProfiler = lazy(() => import("@/components/PerformanceProfiler").then(m => ({ default: m.PerformanceProfiler })));
const PluginManager = lazy(() => import("@/components/PluginManager").then(m => ({ default: m.PluginManager })));
const AgentSystem = lazy(() => import("@/components/AgentSystemRefactored").then(m => ({ default: m.AgentSystem })));

type View = 
  | "welcome" 
  | "projects" 
  | "agents" 
  | "editor" 
  | "settings" 
  | "claude-file-editor" 
  | "claude-code-session" 
  | "usage-dashboard" 
  | "mcp" 
  | "templates" 
  | "performance" 
  | "plugins" 
  | "agent-system" 
  | "parallel-agents" 
  | "enhanced-agents" 
  | "visual-effects" 
  | "agent-execution" 
  | "smart-assistant" 
  | "smart-features" 
  | "workflow-automation" 
  | "enhanced-mcp" 
  | "bulk-tasks" 
  | "enterprise-parallel-agents" 
  | "context-engineering";

/**
 * Main App component - Manages the Claude directory browser UI
 */
function App() {
  const initializationStatus = useAppInitializer();
  const {
    view,
    loading,
    error,
    showNFO,
    showClaudeBinaryDialog,
    activeClaudeSessionId,
    setView,
    setLoading,
    setShowNFO,
    setShowClaudeBinaryDialog,
    setClaudeStreaming
  } = useUIStore();
  
  const {
    projects,
    selectedProject,
    sessions,
    editingClaudeFile,
    selectedSession,
    fetchProjects,
    selectProject,
    clearSelectedProject,
    selectSession,
    editClaudeFile,
    clearEditingClaudeFile,
  } = useProjectStore();

  // Focus mode and feature context
  const { getVisibleFeatures } = useFocusMode();
  const { suggestedFeatures } = useFeatureContext(view, selectedProject?.path);

  // Keyboard shortcuts
  useKeyboardNavigation([
    {
      key: '1',
      ctrl: true,
      handler: () => setView('agents'),
      description: 'Go to CC Agents'
    },
    {
      key: '2',
      ctrl: true,
      handler: () => setView('projects'),
      description: 'Go to CC Projects'
    },
    {
      key: '3',
      ctrl: true,
      handler: () => setView('templates'),
      description: 'Go to Smart Templates'
    },
    {
      key: '4',
      ctrl: true,
      handler: () => setView('performance'),
      description: 'Go to Performance'
    },
    {
      key: 'h',
      ctrl: true,
      handler: () => setView('welcome'),
      description: 'Go to Home'
    },
    {
      key: 'Escape',
      handler: () => {
        if (showNFO) setShowNFO(false);
        if (showClaudeBinaryDialog) setShowClaudeBinaryDialog(false);
      },
      description: 'Close dialogs'
    }
  ]);

  // Load projects on mount when in projects view
  useEffect(() => {
    if (view === "projects") {
      fetchProjects();
    } else if (view === "welcome") {
      // Reset loading state for welcome view
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [view]);

  // Listen for Claude session selection events
  useEffect(() => {
    const handleSessionSelected = (event: CustomEvent) => {
      const { session } = event.detail;
      selectSession(session);
    };

    const handleClaudeNotFound = () => {
      setShowClaudeBinaryDialog(true);
    };

    window.addEventListener('claude-session-selected', handleSessionSelected as EventListener);
    window.addEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    return () => {
      window.removeEventListener('claude-session-selected', handleSessionSelected as EventListener);
      window.removeEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    };
  }, [selectSession, setShowClaudeBinaryDialog]);

  const handleNewSession = () => {
    selectSession(null);
  };

  const renderContent = () => {
    console.log("Rendering view:", view);
    switch (view) {
      case "welcome":
        return (
          <div className="min-h-full bg-gradient-to-br from-background via-background to-muted/20">
            <div className="container mx-auto px-6 py-12 max-w-7xl">
              {/* Enhanced Hero Section */}
              <motion.div
                initial={{ opacity: 0, y: -30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center mb-20"
              >
                <div className="relative">
                  <h1 className="text-6xl md:text-7xl font-bold tracking-tight bg-gradient-to-r from-primary via-purple-500 to-pink-500 bg-clip-text text-transparent mb-8">
                    Claudia Enhanced
                  </h1>
                  <p className="text-2xl md:text-3xl text-muted-foreground mb-10 max-w-4xl mx-auto leading-relaxed">
                    Your AI Development Command Center
                  </p>
                  <p className="text-xl text-muted-foreground/80 mb-16 max-w-5xl mx-auto">
                    Transform Claude Code into a visual, collaborative, and intelligent development experience
                  </p>
                  
                  {/* Quick Action Buttons */}
                  <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                    <motion.div
                      whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button 
                        size="lg" 
                        className="px-12 py-4 text-xl font-semibold bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90 transition-all duration-300 shadow-2xl hover:shadow-3xl rounded-xl"
                        onClick={() => setView("projects")}
                      >
                        <Plus className="mr-3 h-6 w-6" />
                        🚀 Start New Project
                      </Button>
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05, boxShadow: "0 20px 40px rgba(0,0,0,0.1)" }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button 
                        variant="outline" 
                        size="lg" 
                        className="px-12 py-4 text-xl font-semibold border-2 hover:bg-accent/50 transition-all duration-300 rounded-xl backdrop-blur-sm"
                        onClick={() => setView("agents")}
                      >
                        <Bot className="mr-3 h-6 w-6" />
                        🤖 Explore AI Agents
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </motion.div>

              {/* Quick Stats Dashboard */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="mb-20"
              >
                <h2 className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-gray-900 to-gray-600 dark:from-gray-100 dark:to-gray-400 bg-clip-text text-transparent">
                  Quick Overview
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <motion.div
                    whileHover={{ y: -5, boxShadow: "0 25px 50px rgba(0,0,0,0.1)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Card className="p-8 text-center border-2 hover:border-blue-200 dark:hover:border-blue-800 transition-all duration-300 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
                      <div className="flex items-center justify-center mb-6">
                        <div className="p-4 rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                          <Code2 className="w-8 h-8 text-white" />
                        </div>
                      </div>
                      <h3 className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                        {sessions.length}
                      </h3>
                      <p className="text-lg font-medium text-blue-700 dark:text-blue-300">Active Sessions</p>
                      <p className="text-sm text-blue-600/70 dark:text-blue-400/70 mt-2">Currently running</p>
                    </Card>
                  </motion.div>
                  <motion.div
                    whileHover={{ y: -5, boxShadow: "0 25px 50px rgba(0,0,0,0.1)" }}
                    transition={{ type: "spring", stiffness: 300, delay: 0.1 }}
                  >
                    <Card className="p-8 text-center border-2 hover:border-green-200 dark:hover:border-green-800 transition-all duration-300 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
                      <div className="flex items-center justify-center mb-6">
                        <div className="p-4 rounded-2xl bg-gradient-to-br from-green-500 to-green-600 shadow-lg">
                          <Bot className="w-8 h-8 text-white" />
                        </div>
                      </div>
                      <h3 className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">
                        12
                      </h3>
                      <p className="text-lg font-medium text-green-700 dark:text-green-300">Available Agents</p>
                      <p className="text-sm text-green-600/70 dark:text-green-400/70 mt-2">Ready to assist</p>
                    </Card>
                  </motion.div>
                  <motion.div
                    whileHover={{ y: -5, boxShadow: "0 25px 50px rgba(0,0,0,0.1)" }}
                    transition={{ type: "spring", stiffness: 300, delay: 0.2 }}
                  >
                    <Card className="p-8 text-center border-2 hover:border-purple-200 dark:hover:border-purple-800 transition-all duration-300 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
                      <div className="flex items-center justify-center mb-6">
                        <div className="p-4 rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg">
                          <Network className="w-8 h-8 text-white" />
                        </div>
                      </div>
                      <h3 className="text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                        8
                      </h3>
                      <p className="text-lg font-medium text-purple-700 dark:text-purple-300">MCP Servers</p>
                      <p className="text-sm text-purple-600/70 dark:text-purple-400/70 mt-2">Connected & active</p>
                    </Card>
                  </motion.div>
                </div>
              </motion.div>

              {/* Enhanced Navigation Cards */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <DynamicNavigationCards 
                  visibleFeatures={getVisibleFeatures()}
                  suggestedFeatures={suggestedFeatures}
                  onFeatureClick={(feature) => {
                    // Type assertion to bypass TypeScript issue
                    setView(feature as any);
                  }}
                />
              </motion.div>
            </div>
          </div>
        );

      case "agents":
        return (
          <div className="flex-1 overflow-hidden">
            <CCAgents onBack={() => setView("welcome")} />
          </div>
        );

      case "editor":
        return (
          <div className="flex-1 overflow-hidden">
            <MarkdownEditor onBack={() => setView("welcome")} />
          </div>
        );
      
      case "settings":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Settings onBack={() => setView("welcome")} />
          </div>
        );
      
      case "projects":
        return (
          <div className="flex h-full items-center justify-center p-4 overflow-y-auto">
            <div className="w-full max-w-2xl">
              {/* Header with back button */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="mb-6"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setView("welcome")}
                  className="mb-4"
                >
                  ← Back to Home
                </Button>
                <div className="text-center">
                  <h1 className="text-3xl font-bold tracking-tight">CC Projects</h1>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Browse your Claude Code sessions
                  </p>
                </div>
              </motion.div>

              {/* Error display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mb-4 rounded-lg border border-destructive/50 bg-destructive/10 p-3 text-xs text-destructive"
                >
                  {error}
                </motion.div>
              )}

              {/* Loading state */}
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              )}

              {/* Content */}
              {!loading && (
                <AnimatePresence mode="wait">
                  {selectedProject ? (
                    <motion.div
                      key="sessions"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <SessionList
                        sessions={sessions}
                        projectPath={selectedProject.path}
                        onBack={clearSelectedProject}
                        onEditClaudeFile={editClaudeFile}
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="projects"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      {/* New session button at the top */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Button
                          onClick={handleNewSession}
                          size="default"
                          className="w-full"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          New Claude Code session
                        </Button>
                      </motion.div>

                      {/* Running Claude Sessions */}
                      <RunningClaudeSessions />

                      {/* Project list */}
                      {projects.length > 0 ? (
                        <ProjectList
                          projects={projects}
                          onProjectClick={selectProject}
                        />
                      ) : (
                        <div className="py-8 text-center">
                          <p className="text-sm text-muted-foreground">
                            No projects found in ~/.claude/projects
                          </p>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              )}
            </div>
          </div>
        );
      
      case "claude-file-editor":
        return editingClaudeFile ? (
          <ClaudeFileEditor
            file={editingClaudeFile}
            onBack={clearEditingClaudeFile}
          />
        ) : null;
      
      case "claude-code-session":
        return (
          <ClaudeCodeSession
            session={selectedSession || undefined}
            onBack={() => {
              selectSession(null);
              setView("projects");
            }}
            onStreamingChange={setClaudeStreaming}
          />
        );
      
      case "usage-dashboard":
        return (
          <UsageDashboard onBack={() => setView("welcome")} />
        );
      
      case "mcp":
        return (
          <MCPManager onBack={() => setView("welcome")} />
        );
      
      case "templates":
        return (
          <Suspense fallback={<LoadingFallback />}>
            <SmartTemplates 
              onSelectTemplate={(template) => {
                void template; // Suppress unused parameter warning
                // Handle template selection
              }}
              onClose={() => setView("welcome")}
            />
          </Suspense>
        );
      
      case "performance":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Suspense fallback={<LoadingFallback />}>
              <PerformanceProfiler 
                sessionId="default" 
                className="flex-1"
              />
            </Suspense>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setView("welcome")}
              className="absolute top-4 left-4"
            >
              ← Back to Home
            </Button>
          </div>
        );
      
      case "plugins":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Suspense fallback={<LoadingFallback />}>
              <PluginManager onClose={() => setView("welcome")} />
            </Suspense>
          </div>
        );
      
      case "agent-system":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Suspense fallback={<LoadingFallback />}>
              <AgentSystem 
                sessionId={activeClaudeSessionId || "demo-session"} 
                projectPath={selectedProject?.path || "/demo/project"}
                onClose={() => setView("welcome")}
              />
            </Suspense>
          </div>
        );
      
      case "parallel-agents":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Parallel Agents">
                <ParallelAgentsDemo />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "enhanced-agents":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Enhanced Agent Dashboard">
                <EnhancedAgentDashboard />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "visual-effects":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Visual Effects Manager">
                <VisualEffectsManager />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "agent-execution":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Agent Execution Demo">
                <AgentExecutionDemo />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "smart-assistant":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Smart Code Assistant">
                <SmartCodeAssistant />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "smart-features":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Smart Feature Selection">
                <SmartFeatureSelection currentView={view} activeProject={selectedProject?.path} />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "workflow-automation":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Workflow Automation">
                <EnhancedWorkflowAutomation />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "enhanced-mcp":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <EnhancedMCPManager onBack={() => setView("welcome")} />
            </ErrorBoundary>
          </div>
        );
      
      case "bulk-tasks":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Bulk Task Manager">
                <BulkTaskManager />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "enterprise-parallel-agents":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <FeatureWrapper onBack={() => setView("welcome")} title="Enterprise Parallel Agents System">
                <ParallelAgentsSystemIntegration
                  config={{
                    autoStart: true,
                    enableMonitoring: true,
                    enableOptimization: true,
                    enableWorkflows: true,
                    enableAnalytics: true,
                    debugMode: false
                  }}
                  onSystemReady={() => {
                    console.log('🚀 Enterprise Parallel Agents System is ready!');
                  }}
                  onSystemError={(error) => {
                    console.error('❌ Enterprise Parallel Agents System error:', error);
                  }}
                />
              </FeatureWrapper>
            </ErrorBoundary>
          </div>
        );
      
      case "context-engineering":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <ErrorBoundary>
              <ContextEngineering onBack={() => setView("welcome")} />
            </ErrorBoundary>
          </div>
        );
      
      default:
        return null;
    }
  };

  if (initializationStatus === 'initializing') {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (initializationStatus === 'error') {
    return (
      <div className="flex h-screen items-center justify-center p-4 text-center">
        <div>
          <h1 className="text-2xl font-bold text-destructive">Application Error</h1>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Reload Application
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <ErrorBoundary>
      <OutputCacheProvider>
        <div className="h-screen bg-background flex flex-col">
          <SkipLink href="#main-content">Skip to main content</SkipLink>
          {/* Topbar */}
          <Topbar
            onClaudeClick={() => setView("editor")}
            onSettingsClick={() => setView("settings")}
            onUsageClick={() => setView("usage-dashboard")}
            onPerformanceClick={() => setView("performance")}
            onPluginsClick={() => setView("plugins")}
            onAgentSystemClick={() => setView("agent-system")}
            onMCPClick={() => setView("mcp")}
            onInfoClick={() => setShowNFO(true)}
          />
          
          {/* Main Content */}
          <main id="main-content" className="flex-1 overflow-y-auto" role="main" aria-label="Main content">
            <ErrorBoundary>
              {renderContent()}
            </ErrorBoundary>
          </main>
          
          {/* NFO Credits Modal */}
          {showNFO && <NFOCredits onClose={() => setShowNFO(false)} />}
          
          {/* Claude Binary Dialog */}
          <ClaudeBinaryDialog
            open={showClaudeBinaryDialog}
            onOpenChange={setShowClaudeBinaryDialog}
            onSuccess={() => {
              // Claude binary path saved successfully
              window.location.reload();
            }}
            onError={(message) => {
              // Handle error if needed
              console.error("Claude binary setup error:", message);
            }}
          />
          
          {/* Toast Container - using sonner for notifications */}
          
          {/* Sonner Toaster */}
          <Toaster
            position="bottom-right"
            richColors
            expand
            closeButton
          />
          
          {/* Quick Customization Panel */}
          <QuickCustomization defaultPosition="right" />
        </div>
      </OutputCacheProvider>
    </ErrorBoundary>
  );
}

export default App;
