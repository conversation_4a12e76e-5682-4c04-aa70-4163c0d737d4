import { create } from 'zustand';

export interface Notification {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration?: number; // in milliseconds, undefined means persist until manually dismissed
  action?: {
    label: string;
    onClick: () => void;
  };
  timestamp: number;
}

interface NotificationState {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => string;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  updateNotification: (id: string, updates: Partial<Notification>) => void;
}

// Generate unique IDs for notifications
let notificationIdCounter = 0;
function generateNotificationId(): string {
  return `notification-${Date.now()}-${++notificationIdCounter}`;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],

  addNotification: (notification) => {
    const id = generateNotificationId();
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: Date.now(),
    };

    set((state) => ({
      notifications: [...state.notifications, newNotification],
    }));

    // Auto-remove notification after duration (default 5 seconds)
    if (notification.duration !== undefined) {
      const duration = notification.duration || 5000;
      setTimeout(() => {
        get().removeNotification(id);
      }, duration);
    }

    return id;
  },

  removeNotification: (id) => {
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    }));
  },

  clearAllNotifications: () => {
    set({ notifications: [] });
  },

  updateNotification: (id, updates) => {
    set((state) => ({
      notifications: state.notifications.map((n) =>
        n.id === id ? { ...n, ...updates } : n
      ),
    }));
  },
}));

// Convenience functions for common notification types
export const notifications = {
  success: (message: string, options?: Partial<Omit<Notification, 'type' | 'message'>>) =>
    useNotificationStore.getState().addNotification({ type: 'success', message, duration: 4000, ...options }),
  
  error: (message: string, options?: Partial<Omit<Notification, 'type' | 'message'>>) =>
    useNotificationStore.getState().addNotification({ type: 'error', message, duration: 8000, ...options }),
  
  info: (message: string, options?: Partial<Omit<Notification, 'type' | 'message'>>) =>
    useNotificationStore.getState().addNotification({ type: 'info', message, duration: 5000, ...options }),
  
  warning: (message: string, options?: Partial<Omit<Notification, 'type' | 'message'>>) =>
    useNotificationStore.getState().addNotification({ type: 'warning', message, duration: 6000, ...options }),
};