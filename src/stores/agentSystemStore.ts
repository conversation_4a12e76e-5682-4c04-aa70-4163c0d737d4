import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { Agent, AgentTask, AgentConversation, AgentMetrics } from '@/components/AgentSystem/types';

interface AgentSystemState {
  // Current state
  selectedAgent: Agent | null;
  selectedType: string | null;
  activeTab: string;
  agents: Agent[];
  tasks: AgentTask[];
  conversation: AgentConversation | null;
  analytics: AgentMetrics | null;
  
  // Deployment form state
  deployForm: {
    name: string;
    specialization: string;
  };
  
  // Autosave state
  lastSaved: number;
  hasUnsavedChanges: boolean;
  
  // Actions
  setSelectedAgent: (agent: Agent | null) => void;
  setSelectedType: (type: string | null) => void;
  setActiveTab: (tab: string) => void;
  setAgents: (agents: Agent[]) => void;
  setTasks: (tasks: AgentTask[]) => void;
  setConversation: (conversation: AgentConversation | null) => void;
  setAnalytics: (analytics: AgentMetrics | null) => void;
  setDeployForm: (form: { name: string; specialization: string }) => void;
  updateDeployForm: (updates: Partial<{ name: string; specialization: string }>) => void;
  markAsSaved: () => void;
  resetState: () => void;
}

const initialState = {
  selectedAgent: null,
  selectedType: null,
  activeTab: 'catalog',
  agents: [],
  tasks: [],
  conversation: null,
  analytics: null,
  deployForm: {
    name: '',
    specialization: ''
  },
  lastSaved: Date.now(),
  hasUnsavedChanges: false
};

// Helper to mark changes
const markChanged = (changes: any) => ({
  ...changes,
  hasUnsavedChanges: true
});

export const useAgentSystemStore = create<AgentSystemState>()(
  persist(
    (set, get) => ({
      // Initial state
      ...initialState,
      
      // Actions with autosave marking
      setSelectedAgent: (agent) => set(markChanged({ selectedAgent: agent })),
      setSelectedType: (type) => set(markChanged({ selectedType: type })),
      setActiveTab: (tab) => set(markChanged({ activeTab: tab })),
      setAgents: (agents) => set(markChanged({ agents })),
      setTasks: (tasks) => set(markChanged({ tasks })),
      setConversation: (conversation) => set(markChanged({ conversation })),
      setAnalytics: (analytics) => set(markChanged({ analytics })),
      setDeployForm: (form) => set(markChanged({ deployForm: form })),
      updateDeployForm: (updates) => set((state) => markChanged({
        deployForm: { ...state.deployForm, ...updates }
      })),
      markAsSaved: () => set({ 
        lastSaved: Date.now(), 
        hasUnsavedChanges: false 
      }),
      resetState: () => set(initialState)
    }),
    {
      name: 'agent-system-storage',
      storage: createJSONStorage(() => localStorage),
      version: 1,
      partialize: (state) => ({
        // Persist everything except temporary UI state
        selectedAgent: state.selectedAgent,
        selectedType: state.selectedType,
        activeTab: state.activeTab,
        agents: state.agents,
        tasks: state.tasks,
        conversation: state.conversation,
        analytics: state.analytics,
        deployForm: state.deployForm,
        lastSaved: state.lastSaved,
        hasUnsavedChanges: state.hasUnsavedChanges
      })
    }
  )
);