import { useEffect, useRef } from 'react';
import { useAgentSystemStore } from '@/stores/agentSystemStore';
import { toast } from 'sonner';

export function useAgentSystemAutosave() {
  const { hasUnsavedChanges, markAsSaved, lastSaved } = useAgentSystemStore();

  // Save on window unload (browser close/refresh)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // Perform synchronous save
        markAsSaved();
        
        // Show warning if there are unsaved changes
        e.preventDefault();
        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, markAsSaved]);


  const performManualSave = () => {
    try {
      markAsSaved();
      // Show subtle notification
      toast.success('Agent system saved', {
        duration: 2000,
        position: 'bottom-left',
      });
    } catch (error) {
      console.error('Save failed:', error);
      toast.error('Failed to save', {
        duration: 3000,
        position: 'bottom-left',
      });
    }
  };

  // Return save status for UI indicators
  return {
    hasUnsavedChanges,
    lastSaved,
    timeSinceLastSave: Date.now() - lastSaved,
    performManualSave
  };
}