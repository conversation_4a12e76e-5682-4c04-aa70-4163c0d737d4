import { useState, useCallback, useRef, useEffect } from 'react';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

export interface AsyncOperationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  retryCount?: number;
  retryDelay?: number;
}

export interface UseAsyncOperationReturn<T> {
  state: AsyncState<T>;
  execute: (...args: any[]) => Promise<T>;
  reset: () => void;
  retry: () => void;
}

export function useAsyncOperation<T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: AsyncOperationOptions = {}
): UseAsyncOperationReturn<T> {
  const [state, setState] = useState<AsyncState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const lastArgsRef = useRef<any[]>([]);
  const retryCountRef = useRef(0);
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const { onSuccess, onError, retryCount = 0, retryDelay = 1000 } = options;

  const execute = useCallback(
    async (...args: any[]): Promise<T> => {
      if (!isMountedRef.current) return Promise.reject(new Error('Component unmounted'));

      lastArgsRef.current = args;
      retryCountRef.current = 0;

      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        const result = await asyncFunction(...args);
        
        if (isMountedRef.current) {
          setState({
            data: result,
            loading: false,
            error: null,
          });

          onSuccess?.(result);
        }
        
        return result;
      } catch (error) {
        const errorInstance = error instanceof Error ? error : new Error(String(error));
        
        if (isMountedRef.current) {
          setState({
            data: null,
            loading: false,
            error: errorInstance,
          });

          onError?.(errorInstance);
        }
        
        throw errorInstance;
      }
    },
    [asyncFunction, onSuccess, onError]
  );

  const retry = useCallback(async () => {
    if (retryCountRef.current >= retryCount) {
      return;
    }

    retryCountRef.current += 1;

    // Add delay before retry
    if (retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    try {
      await execute(...lastArgsRef.current);
    } catch (error) {
      // If retry fails and we have more retries left, try again
      if (retryCountRef.current < retryCount) {
        retry();
      }
    }
  }, [execute, retryCount, retryDelay]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
    retryCountRef.current = 0;
  }, []);

  return {
    state,
    execute,
    reset,
    retry,
  };
}

// Hook for handling async operations with automatic error boundaries
export function useAsyncWithErrorBoundary<T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: AsyncOperationOptions & { throwOnError?: boolean } = {}
) {
  const { throwOnError = false, ...asyncOptions } = options;
  
  const asyncOp = useAsyncOperation(asyncFunction, {
    ...asyncOptions,
    onError: (error) => {
      // Call the original onError callback
      asyncOptions.onError?.(error);
      
      // Optionally throw the error to trigger error boundary
      if (throwOnError) {
        throw error;
      }
    },
  });

  return asyncOp;
}