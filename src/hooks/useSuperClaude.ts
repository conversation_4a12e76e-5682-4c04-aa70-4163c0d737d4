// Hook for using SuperClaude settings in components

import { useState, useEffect } from 'react';
import { 
  SuperClaudeSettings, 
  defaultSuperClaudeSettings, 
  loadSettings,
  saveSettings,
  settingsToCommandArgs,
  estimateTokenUsage
} from '@/lib/superClaude';

export function useSuperClaude() {
  const [settings, setSettings] = useState<SuperClaudeSettings>(defaultSuperClaudeSettings);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings on mount
  useEffect(() => {
    const loaded = loadSettings();
    setSettings(loaded);
    setIsLoading(false);
  }, []);

  // Update settings
  const updateSettings = (newSettings: SuperClaudeSettings) => {
    setSettings(newSettings);
    saveSettings(newSettings);
  };

  // Get command line arguments
  const getCommandArgs = () => {
    return settingsToCommandArgs(settings);
  };

  // Get token usage estimation
  const getTokenUsage = () => {
    return estimateTokenUsage(settings);
  };

  // Check if a specific MCP server is enabled
  const isMcpServerEnabled = (server: keyof SuperClaudeSettings['mcpServers']) => {
    return settings.mcpServers[server];
  };

  // Check if a specific feature is enabled
  const isFeatureEnabled = (feature: string) => {
    switch (feature) {
      case 'introspection':
        return settings.coreModes.introspection;
      case 'ultraCompressed':
        return settings.coreModes.ultraCompressed;
      case 'tokenEconomy':
        return settings.coreModes.tokenEconomy;
      case 'planning':
        return settings.planning.enabled;
      case 'security':
        return settings.security.owaspCompliance || settings.security.vulnerabilityScanning;
      default:
        return false;
    }
  };

  return {
    settings,
    updateSettings,
    getCommandArgs,
    getTokenUsage,
    isMcpServerEnabled,
    isFeatureEnabled,
    isLoading,
  };
}

// Example usage in a component:
/*
function MyComponent() {
  const { 
    settings, 
    updateSettings, 
    getCommandArgs,
    getTokenUsage,
    isFeatureEnabled 
  } = useSuperClaude();

  const handleStartSession = async () => {
    const args = getCommandArgs();
    const tokenUsage = getTokenUsage();
    
    console.log('Starting session with args:', args);
    console.log('Estimated token usage:', tokenUsage);
    
    // Pass args to your API call
    await api.startClaudeSession({
      ...otherParams,
      superClaudeArgs: args,
    });
  };

  return (
    <div>
      {isFeatureEnabled('introspection') && (
        <div>Introspection mode is enabled!</div>
      )}
      <button onClick={handleStartSession}>
        Start Enhanced Session
      </button>
    </div>
  );
}
*/