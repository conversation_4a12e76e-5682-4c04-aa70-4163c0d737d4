import { useState, useEffect } from 'react';
import { useUIStore } from '@/stores/uiStore';
import { api } from '@/lib/api';

type InitializationStatus = 'initializing' | 'ready' | 'error';

export function useAppInitializer() {
  const [status, setStatus] = useState<InitializationStatus>('initializing');
  const { setLoading, setError } = useUIStore();

  useEffect(() => {
    const initialize = async () => {
      try {
        setLoading(true);
        console.log("Starting app initialization...");
        
        // Perform all startup operations here
        try {
          await api.checkClaudeVersion();
          console.log("Claude version check completed");
        } catch (err) {
          console.warn("Claude version check failed, continuing anyway:", err);
          // Don't fail initialization just because <PERSON> check failed
        }
        
        setStatus('ready');
        console.log("App initialization complete");
      } catch (err) {
        console.error("Application initialization failed:", err);
        setError("Failed to initialize the application. Please try restarting.");
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    initialize();
  }, [setLoading, setError]);

  return status;
}