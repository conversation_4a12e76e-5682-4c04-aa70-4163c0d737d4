import { useState, useEffect, useCallback } from 'react';
import { MCPServerManager, MCPServerConfig, MCPServerMetrics } from '@/lib/mcp/MCPServerManager';

export function useMCPServers() {
  const [servers, setServers] = useState<MCPServerConfig[]>([]);
  const [metrics, setMetrics] = useState<MCPServerMetrics[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [manager] = useState(() => MCPServerManager.getInstance());

  // Load servers on mount
  useEffect(() => {
    const loadServers = () => {
      try {
        const serverList = manager.getServers();
        setServers(serverList);
        setLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load servers');
        setLoading(false);
      }
    };

    loadServers();

    // Listen for server changes
    const handleServerChange = (updatedServers: MCPServerConfig[]) => {
      setServers(updatedServers);
    };

    manager.addListener(handleServerChange);

    return () => {
      manager.removeListener(handleServerChange);
    };
  }, [manager]);

  // Update metrics periodically
  useEffect(() => {
    const updateMetrics = () => {
      const allMetrics = manager.getAllMetrics();
      setMetrics(allMetrics);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 2000);

    return () => clearInterval(interval);
  }, [manager]);

  // Server operations
  const addServer = useCallback(async (config: Omit<MCPServerConfig, 'id' | 'status'>) => {
    try {
      setError(null);
      await manager.addServer(config);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add server');
      throw err;
    }
  }, [manager]);

  const removeServer = useCallback(async (serverId: string) => {
    try {
      setError(null);
      await manager.removeServer(serverId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove server');
      throw err;
    }
  }, [manager]);

  const startServer = useCallback(async (serverId: string) => {
    try {
      setError(null);
      await manager.startServer(serverId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start server');
      throw err;
    }
  }, [manager]);

  const stopServer = useCallback(async (serverId: string) => {
    try {
      setError(null);
      await manager.stopServer(serverId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to stop server');
      throw err;
    }
  }, [manager]);

  const restartServer = useCallback(async (serverId: string) => {
    try {
      setError(null);
      await manager.restartServer(serverId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to restart server');
      throw err;
    }
  }, [manager]);

  const testConnection = useCallback(async (serverId: string) => {
    try {
      return await manager.testConnection(serverId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to test connection');
      return false;
    }
  }, [manager]);

  // File operations
  const readFile = useCallback(async (serverId: string, path: string) => {
    try {
      setError(null);
      return await manager.readFile(serverId, path);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to read file');
      throw err;
    }
  }, [manager]);

  const writeFile = useCallback(async (serverId: string, path: string, content: string) => {
    try {
      setError(null);
      await manager.writeFile(serverId, path, content);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to write file');
      throw err;
    }
  }, [manager]);

  const listDirectory = useCallback(async (serverId: string, path: string) => {
    try {
      setError(null);
      return await manager.listDirectory(serverId, path);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to list directory');
      throw err;
    }
  }, [manager]);

  // Configuration management
  const exportConfiguration = useCallback(async () => {
    try {
      setError(null);
      return await manager.exportConfiguration();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export configuration');
      throw err;
    }
  }, [manager]);

  const importConfiguration = useCallback(async (configJson: string) => {
    try {
      setError(null);
      await manager.importConfiguration(configJson);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to import configuration');
      throw err;
    }
  }, [manager]);

  // Utility functions
  const getServerMetrics = useCallback((serverId: string) => {
    return metrics.find(m => m.id === serverId);
  }, [metrics]);

  const getRunningServers = useCallback(() => {
    return servers.filter(s => s.status === 'running');
  }, [servers]);

  const getServerTemplates = useCallback(() => {
    return manager.getServerTemplates();
  }, [manager]);

  return {
    servers,
    metrics,
    loading,
    error,
    addServer,
    removeServer,
    startServer,
    stopServer,
    restartServer,
    testConnection,
    readFile,
    writeFile,
    listDirectory,
    exportConfiguration,
    importConfiguration,
    getServerMetrics,
    getRunningServers,
    getServerTemplates,
    clearError: () => setError(null)
  };
}