import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  meta?: boolean;
  handler: () => void;
  description?: string;
}

export function useKeyboardNavigation(shortcuts: KeyboardShortcut[]) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    for (const shortcut of shortcuts) {
      const isCtrlMatch = shortcut.ctrl ? event.ctrlKey : !event.ctrlKey;
      const isShiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey;
      const isAltMatch = shortcut.alt ? event.altKey : !event.altKey;
      const isMetaMatch = shortcut.meta ? event.metaKey : !event.metaKey;
      
      if (
        event.key === shortcut.key &&
        isCtrlMatch &&
        isShiftMatch &&
        isAltMatch &&
        isMetaMatch
      ) {
        event.preventDefault();
        shortcut.handler();
        break;
      }
    }
  }, [shortcuts]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);
}

// Common keyboard shortcuts
export const commonShortcuts: KeyboardShortcut[] = [
  {
    key: 'Escape',
    handler: () => {
      // Close any open modals or dialogs
      const closeButton = document.querySelector('[data-close-button]') as HTMLElement;
      closeButton?.click();
    },
    description: 'Close dialog'
  },
  {
    key: '/',
    ctrl: true,
    handler: () => {
      // Focus search input
      const searchInput = document.querySelector('[data-search-input]') as HTMLElement;
      searchInput?.focus();
    },
    description: 'Focus search'
  },
  {
    key: 'Tab',
    handler: () => {
      // Let browser handle tab navigation, but ensure focus indicators are visible
      document.body.classList.add('keyboard-navigation');
    },
    description: 'Navigate with keyboard'
  }
];

// Focus trap hook for modals
export function useFocusTrap(isActive: boolean, containerRef: React.RefObject<HTMLElement>) {
  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'a[href], button:not([disabled]), textarea:not([disabled]), input:not([disabled]), select:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    // Focus first element
    firstElement?.focus();

    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  }, [isActive, containerRef]);
}

// Announce to screen readers
export function useAnnounce() {
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('role', 'status');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }, []);

  return announce;
}