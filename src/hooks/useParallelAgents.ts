import { useState, useEffect, useCallback } from 'react';
import { ParallelAgentsManager } from '@/lib/ai-agents/ParallelAgentsManager';
import { Task, ExecutionContext } from '@/lib/ai-agents/types';

export interface UseParallelAgentsReturn {
  isProcessing: boolean;
  metrics: any;
  clusters: any[];
  executionPlans: any[];
  startParallelExecution: (tasks: Task[]) => Promise<string>;
  pauseExecution: (planId: string) => Promise<void>;
  resumeExecution: (planId: string) => Promise<void>;
  cancelExecution: (planId: string) => Promise<void>;
  getExecutionStatus: (planId: string) => any;
  autoScaleAgents: (targetUtilization?: number) => Promise<void>;
  rebalanceWorkload: () => Promise<void>;
}

export function useParallelAgents(): UseParallelAgentsReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [metrics, setMetrics] = useState({});
  const [clusters, setClusters] = useState([]);
  const [executionPlans, setExecutionPlans] = useState([]);
  const [manager] = useState(() => ParallelAgentsManager.getInstance());

  // Update metrics periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const currentMetrics = manager.getParallelExecutionMetrics();
      setMetrics(currentMetrics);
      setClusters(currentMetrics.clusters || []);
    }, 2000);

    return () => clearInterval(interval);
  }, [manager]);

  const startParallelExecution = useCallback(async (tasks: Task[]): Promise<string> => {
    setIsProcessing(true);
    try {
      const planId = await manager.startParallelExecution(tasks);
      return planId;
    } finally {
      setIsProcessing(false);
    }
  }, [manager]);

  const pauseExecution = useCallback(async (planId: string): Promise<void> => {
    await manager.pauseExecution(planId);
  }, [manager]);

  const resumeExecution = useCallback(async (planId: string): Promise<void> => {
    await manager.resumeExecution(planId);
  }, [manager]);

  const cancelExecution = useCallback(async (planId: string): Promise<void> => {
    await manager.cancelExecution(planId);
  }, [manager]);

  const getExecutionStatus = useCallback((planId: string) => {
    return manager.getExecutionStatus(planId);
  }, [manager]);

  const autoScaleAgents = useCallback(async (targetUtilization: number = 0.75): Promise<void> => {
    await manager.autoScaleAgents(0.8, targetUtilization);
  }, [manager]);

  const rebalanceWorkload = useCallback(async (): Promise<void> => {
    await manager.rebalanceWorkload();
  }, [manager]);

  return {
    isProcessing,
    metrics,
    clusters,
    executionPlans,
    startParallelExecution,
    pauseExecution,
    resumeExecution,
    cancelExecution,
    getExecutionStatus,
    autoScaleAgents,
    rebalanceWorkload
  };
}