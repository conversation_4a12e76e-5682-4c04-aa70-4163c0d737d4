/**
 * Optimized animations to prevent flickering
 * Uses transform3d for hardware acceleration and will-change for performance
 */

/* Smooth transitions with hardware acceleration */
.smooth-transform {
  will-change: transform;
  transform: translate3d(0, 0, 0);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.smooth-transform:hover {
  transform: translate3d(0, -2px, 0) scale3d(1.02, 1.02, 1);
}

/* Non-flickering hover effects */
.hover-lift {
  will-change: transform, box-shadow;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate3d(0, 0, 0);
}

.hover-lift:hover {
  transform: translate3d(0, -1px, 0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.hover-lift:active {
  transform: translate3d(0, 0, 0);
  transition-duration: 0.1s;
}

/* Optimized shimmer that doesn't cause layout shifts */
.shimmer-optimized {
  position: relative;
  overflow: hidden;
}

.shimmer-optimized::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: translate3d(0, 0, 0);
  transition: none;
  pointer-events: none;
  z-index: 1;
}

.shimmer-optimized:hover::before {
  animation: shimmer-slide 0.6s ease-out;
}

@keyframes shimmer-slide {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Prevent animation conflicts */
.no-motion-conflict {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

/* Smooth fade animations */
.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Staggered animations for lists */
.stagger-item {
  opacity: 0;
  transform: translate3d(0, 20px, 0);
  animation: staggerIn 0.4s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0ms; }
.stagger-item:nth-child(2) { animation-delay: 50ms; }
.stagger-item:nth-child(3) { animation-delay: 100ms; }
.stagger-item:nth-child(4) { animation-delay: 150ms; }
.stagger-item:nth-child(5) { animation-delay: 200ms; }

@keyframes staggerIn {
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .smooth-transform,
  .hover-lift,
  .shimmer-optimized::before,
  .fade-in,
  .stagger-item {
    animation: none !important;
    transition: none !important;
  }
}