// Global JSON safety wrapper to prevent crashes
const originalParse = JSON.parse;
const originalStringify = JSON.stringify;

// Override JSON.parse globally with safety checks
JSON.parse = function(text: string, reviver?: any) {
  try {
    if (!text || typeof text !== 'string') {
      console.warn('JSON.parse called with invalid input:', typeof text);
      return {};
    }
    
    const trimmed = text.trim();
    if (!trimmed) {
      console.warn('JSON.parse called with empty string');
      return {};
    }
    
    return originalParse.call(this, trimmed, reviver);
  } catch (error) {
    console.error('JSON.parse failed safely:', error, 'Input:', text?.substring(0, 100));
    return {};
  }
};

// Override JSON.stringify globally with safety checks
JSON.stringify = function(value: any, replacer?: any, space?: any) {
  try {
    return originalStringify.call(this, value, replacer, space);
  } catch (error) {
    console.error('JSON.stringify failed safely:', error);
    return '{}';
  }
};

export { originalParse, originalStringify };