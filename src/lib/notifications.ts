import { toast } from "sonner";

export interface NotificationOptions {
  title?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const notifications = {
  success: (message: string, options?: NotificationOptions) => {
    toast.success(message, {
      description: options?.title,
      duration: options?.duration ?? 4000,
      action: options?.action,
    });
  },

  error: (message: string, options?: NotificationOptions) => {
    toast.error(message, {
      description: options?.title,
      duration: options?.duration ?? 6000,
      action: options?.action,
    });
  },

  info: (message: string, options?: NotificationOptions) => {
    toast.info(message, {
      description: options?.title,
      duration: options?.duration ?? 4000,
      action: options?.action,
    });
  },

  warning: (message: string, options?: NotificationOptions) => {
    toast.warning(message, {
      description: options?.title,
      duration: options?.duration ?? 5000,
      action: options?.action,
    });
  },

  loading: (message: string, options?: NotificationOptions) => {
    return toast.loading(message, {
      description: options?.title,
    });
  },

  dismiss: (toastId?: string | number) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  },

  promise: <T>(
    promise: Promise<T>,
    messages: {
      loading: string;
      success: string | ((data: T) => string);
      error: string | ((error: any) => string);
    },
    options?: NotificationOptions
  ) => {
    return toast.promise(promise, {
      loading: messages.loading,
      success: messages.success,
      error: messages.error,
      duration: options?.duration,
    });
  },
};

// Helper function to format error messages
export function formatErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    // Check for specific error types
    if (error.message.includes("network")) {
      return "Network error. Please check your connection.";
    }
    if (error.message.includes("permission") || error.message.includes("denied")) {
      return "Permission denied. Please check your access rights.";
    }
    if (error.message.includes("not found")) {
      return "Resource not found. It may have been moved or deleted.";
    }
    if (error.message.includes("timeout")) {
      return "Operation timed out. Please try again.";
    }
    if (error.message.includes("invalid") || error.message.includes("validation")) {
      return "Invalid input. Please check your data and try again.";
    }
    
    // Return the original message if no specific pattern matches
    return error.message;
  }
  
  if (typeof error === "string") {
    return error;
  }
  
  return "An unexpected error occurred. Please try again.";
}

// Helper to handle API errors consistently
export function handleApiError(error: unknown, context: string): void {
  const message = formatErrorMessage(error);
  notifications.error(message, {
    title: `Failed to ${context}`,
    action: {
      label: "Retry",
      onClick: () => window.location.reload(),
    },
  });
}