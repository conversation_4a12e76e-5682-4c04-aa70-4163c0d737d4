import { invoke } from '@tauri-apps/api/core';

export interface VoicePermissionStatus {
  granted: boolean;
  error?: string;
}

export class TauriVoiceControl {
  private static instance: TauriVoiceControl;
  
  public static getInstance(): TauriVoiceControl {
    if (!TauriVoiceControl.instance) {
      TauriVoiceControl.instance = new TauriVoiceControl();
    }
    return TauriVoiceControl.instance;
  }

  async checkMicrophonePermission(): Promise<VoicePermissionStatus> {
    try {
      return await invoke('check_microphone_permission');
    } catch (error) {
      return {
        granted: false,
        error: error as string,
      };
    }
  }

  async requestMicrophonePermission(): Promise<VoicePermissionStatus> {
    try {
      return await invoke('request_microphone_permission');
    } catch (error) {
      return {
        granted: false,
        error: error as string,
      };
    }
  }

  async getVoiceControlStatus(): Promise<boolean> {
    try {
      return await invoke('get_voice_control_status');
    } catch (error) {
      console.error('Failed to get voice control status:', error);
      return false;
    }
  }

  async enableVoiceDebugging(): Promise<void> {
    try {
      await invoke('enable_voice_debugging');
    } catch (error) {
      console.error('Failed to enable voice debugging:', error);
    }
  }

  // Enhanced Web Speech API detection for Tauri
  isWebSpeechSupported(): boolean {
    // In Tauri, we need to check both the Web Speech API and Tauri environment
    const hasWebSpeech = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    const isTauri = '__TAURI__' in window;
    
    return hasWebSpeech && isTauri;
  }

  // Get platform-specific voice control information
  getPlatformInfo(): {
    platform: string;
    isTauri: boolean;
    hasWebSpeech: boolean;
    supportLevel: 'full' | 'partial' | 'none';
  } {
    const isTauri = '__TAURI__' in window;
    const hasWebSpeech = this.isWebSpeechSupported();
    const platform = navigator.platform;

    let supportLevel: 'full' | 'partial' | 'none' = 'none';
    
    if (isTauri && hasWebSpeech) {
      // Full support in Tauri with Web Speech API
      supportLevel = 'full';
    } else if (hasWebSpeech) {
      // Partial support (web only)
      supportLevel = 'partial';
    }

    return {
      platform,
      isTauri,
      hasWebSpeech,
      supportLevel,
    };
  }

  // Initialize voice control with Tauri-specific settings
  async initializeVoiceControl(): Promise<{
    success: boolean;
    message: string;
    platformInfo: ReturnType<TauriVoiceControl['getPlatformInfo']>;
  }> {
    const platformInfo = this.getPlatformInfo();
    
    if (!platformInfo.isTauri) {
      return {
        success: false,
        message: 'Voice control requires Tauri environment',
        platformInfo,
      };
    }

    if (!platformInfo.hasWebSpeech) {
      return {
        success: false,
        message: 'Web Speech API not available in this Tauri webview',
        platformInfo,
      };
    }

    try {
      const voiceStatus = await this.getVoiceControlStatus();
      const permissionStatus = await this.checkMicrophonePermission();

      if (!voiceStatus) {
        return {
          success: false,
          message: 'Voice control not available on this system',
          platformInfo,
        };
      }

      if (!permissionStatus.granted) {
        // Try to request permission
        const requestResult = await this.requestMicrophonePermission();
        if (!requestResult.granted) {
          return {
            success: false,
            message: `Microphone permission denied: ${requestResult.error || 'Unknown error'}`,
            platformInfo,
          };
        }
      }

      return {
        success: true,
        message: 'Voice control initialized successfully',
        platformInfo,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to initialize voice control: ${error}`,
        platformInfo,
      };
    }
  }
}