/**
 * Advanced Monitoring and Alerting System
 * 
 * Enterprise-grade monitoring and alerting for parallel agents with:
 * - Real-time system health monitoring
 * - Intelligent alerting with escalation policies
 * - Performance anomaly detection
 * - Resource usage tracking and optimization
 * - SLA monitoring and compliance reporting
 * - Custom metrics and dashboards
 * - Integration with external monitoring systems
 */

import { EventEmitter } from '../utils/EventEmitter';
import { AdvancedParallelOrchestrator } from './AdvancedParallelOrchestrator';
import { RealTimeAnalytics } from './RealTimeAnalytics';
import { PerformanceOptimizer } from './PerformanceOptimizer';
import { WorkflowAutomationEngine } from './WorkflowAutomation';

// Types
export interface MonitoringConfig {
  // Collection settings
  collection: {
    interval: number; // milliseconds
    retention: {
      metrics: number; // days
      logs: number; // days
      alerts: number; // days
    };
    batchSize: number;
  };
  
  // Alert settings
  alerting: {
    enabled: boolean;
    channels: AlertChannel[];
    escalationPolicy: EscalationPolicy;
    suppressionRules: SuppressionRule[];
  };
  
  // Thresholds
  thresholds: {
    cpu: { warning: number; critical: number };
    memory: { warning: number; critical: number };
    latency: { warning: number; critical: number };
    errorRate: { warning: number; critical: number };
    throughput: { warning: number; critical: number };
    successRate: { warning: number; critical: number };
  };
  
  // SLA settings
  sla: {
    enabled: boolean;
    targets: {
      availability: number; // percentage
      responseTime: number; // milliseconds
      throughput: number; // requests per second
      errorRate: number; // percentage
    };
    reportingInterval: number; // hours
  };
  
  // Integration settings
  integrations: {
    prometheus?: {
      enabled: boolean;
      endpoint: string;
      pushGateway?: string;
    };
    grafana?: {
      enabled: boolean;
      url: string;
      apiKey: string;
    };
    datadog?: {
      enabled: boolean;
      apiKey: string;
      site: string;
    };
    newrelic?: {
      enabled: boolean;
      licenseKey: string;
    };
  };
}

export interface AlertChannel {
  id: string;
  type: 'email' | 'slack' | 'webhook' | 'sms' | 'pagerduty';
  name: string;
  config: {
    // Email
    recipients?: string[];
    
    // Slack
    webhookUrl?: string;
    channel?: string;
    
    // Webhook
    url?: string;
    headers?: Record<string, string>;
    
    // SMS
    phoneNumbers?: string[];
    
    // PagerDuty
    integrationKey?: string;
  };
  enabled: boolean;
}

export interface EscalationPolicy {
  id: string;
  name: string;
  levels: EscalationLevel[];
}

export interface EscalationLevel {
  level: number;
  delay: number; // minutes
  channels: string[]; // Alert channel IDs
  conditions: {
    severity: AlertSeverity[];
    duration: number; // minutes
  };
}

export interface SuppressionRule {
  id: string;
  name: string;
  conditions: {
    alertType?: string;
    component?: string;
    severity?: AlertSeverity;
    timeWindow?: {
      start: string; // HH:MM
      end: string; // HH:MM
      timezone: string;
    };
  };
  duration: number; // minutes
  enabled: boolean;
}

export type AlertSeverity = 'info' | 'warning' | 'critical' | 'emergency';

export interface Alert {
  id: string;
  type: string;
  severity: AlertSeverity;
  title: string;
  description: string;
  component: string;
  
  // Timing
  timestamp: Date;
  resolvedAt?: Date;
  acknowledgedAt?: Date;
  
  // Context
  metadata: Record<string, any>;
  tags: string[];
  
  // Metrics
  value?: number;
  threshold?: number;
  
  // Status
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  
  // Escalation
  escalationLevel: number;
  notificationsSent: NotificationRecord[];
}

export interface NotificationRecord {
  channel: string;
  timestamp: Date;
  status: 'sent' | 'failed' | 'delivered';
  error?: string;
}

export interface MetricPoint {
  timestamp: Date;
  value: number;
  tags: Record<string, string>;
}

export interface Metric {
  name: string;
  type: 'counter' | 'gauge' | 'histogram' | 'summary';
  description: string;
  unit: string;
  points: MetricPoint[];
}

export interface HealthCheck {
  id: string;
  name: string;
  component: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  lastCheck: Date;
  responseTime: number;
  message?: string;
  details?: Record<string, any>;
}

export interface SLAReport {
  period: {
    start: Date;
    end: Date;
  };
  targets: {
    availability: { target: number; actual: number; status: 'met' | 'missed' };
    responseTime: { target: number; actual: number; status: 'met' | 'missed' };
    throughput: { target: number; actual: number; status: 'met' | 'missed' };
    errorRate: { target: number; actual: number; status: 'met' | 'missed' };
  };
  incidents: {
    count: number;
    totalDowntime: number; // minutes
    mttr: number; // mean time to recovery in minutes
  };
  compliance: {
    overall: number; // percentage
    breakdown: Record<string, number>;
  };
}

export interface MonitoringDashboard {
  id: string;
  name: string;
  description: string;
  widgets: DashboardWidget[];
  layout: {
    columns: number;
    rows: number;
  };
  refreshInterval: number; // seconds
  timeRange: {
    start: Date;
    end: Date;
  };
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'status' | 'alert-list';
  title: string;
  position: { x: number; y: number; width: number; height: number };
  config: {
    metrics?: string[];
    chartType?: 'line' | 'bar' | 'pie' | 'gauge';
    aggregation?: 'sum' | 'avg' | 'min' | 'max' | 'count';
    timeWindow?: number; // minutes
    filters?: Record<string, string>;
  };
}

/**
 * Advanced Monitoring and Alerting System
 */
export class MonitoringAndAlertingSystem extends EventEmitter {
  private isRunning: boolean = false;
  private config: MonitoringConfig;
  private metrics: Map<string, Metric> = new Map();
  private alerts: Map<string, Alert> = new Map();
  private healthChecks: Map<string, HealthCheck> = new Map();
  private dashboards: Map<string, MonitoringDashboard> = new Map();
  private collectionInterval?: NodeJS.Timeout;
  private alertProcessingInterval?: NodeJS.Timeout;
  
  // Dependencies
  private orchestrator: AdvancedParallelOrchestrator;
  private analytics: RealTimeAnalytics;
  private optimizer: PerformanceOptimizer;
  private workflowEngine: WorkflowAutomationEngine;
  
  constructor(
    config: MonitoringConfig,
    orchestrator: AdvancedParallelOrchestrator,
    analytics: RealTimeAnalytics,
    optimizer: PerformanceOptimizer,
    workflowEngine: WorkflowAutomationEngine
  ) {
    super();
    this.config = config;
    this.orchestrator = orchestrator;
    this.analytics = analytics;
    this.optimizer = optimizer;
    this.workflowEngine = workflowEngine;
    
    this.initializeMetrics();
    this.initializeHealthChecks();
    this.initializeDashboards();
  }
  
  /**
   * Start the monitoring system
   */
  async start(): Promise<void> {
    if (this.isRunning) return;
    
    this.isRunning = true;
    
    // Start metric collection
    this.collectionInterval = setInterval(
      () => this.collectMetrics(),
      this.config.collection.interval
    );
    
    // Start alert processing
    this.alertProcessingInterval = setInterval(
      () => this.processAlerts(),
      5000 // Process alerts every 5 seconds
    );
    
    // Setup event listeners
    this.setupEventListeners();
    
    // Initialize integrations
    await this.initializeIntegrations();
    
    this.emit('started');
    console.log('📊 Monitoring and Alerting System started');
  }
  
  /**
   * Stop the monitoring system
   */
  async stop(): Promise<void> {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    
    if (this.collectionInterval) {
      clearInterval(this.collectionInterval);
    }
    
    if (this.alertProcessingInterval) {
      clearInterval(this.alertProcessingInterval);
    }
    
    this.emit('stopped');
    console.log('⏹️ Monitoring and Alerting System stopped');
  }
  
  /**
   * Record a custom metric
   */
  recordMetric(
    name: string,
    value: number,
    tags: Record<string, string> = {},
    timestamp: Date = new Date()
  ): void {
    let metric = this.metrics.get(name);
    
    if (!metric) {
      metric = {
        name,
        type: 'gauge',
        description: `Custom metric: ${name}`,
        unit: 'count',
        points: []
      };
      this.metrics.set(name, metric);
    }
    
    metric.points.push({ timestamp, value, tags });
    
    // Keep only recent points
    const retentionMs = this.config.collection.retention.metrics * 24 * 60 * 60 * 1000;
    const cutoff = new Date(Date.now() - retentionMs);
    metric.points = metric.points.filter(p => p.timestamp > cutoff);
    
    this.emit('metricRecorded', { name, value, tags, timestamp });
  }
  
  /**
   * Create a custom alert
   */
  createAlert(
    type: string,
    severity: AlertSeverity,
    title: string,
    description: string,
    component: string,
    metadata: Record<string, any> = {}
  ): Alert {
    const alert: Alert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      title,
      description,
      component,
      timestamp: new Date(),
      metadata,
      tags: [component, type, severity],
      status: 'active',
      escalationLevel: 0,
      notificationsSent: []
    };
    
    this.alerts.set(alert.id, alert);
    this.emit('alertCreated', alert);
    
    return alert;
  }
  
  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string, acknowledgedBy: string): void {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.status !== 'active') {
      throw new Error(`Cannot acknowledge alert ${alertId}`);
    }
    
    alert.status = 'acknowledged';
    alert.acknowledgedAt = new Date();
    alert.metadata.acknowledgedBy = acknowledgedBy;
    
    this.emit('alertAcknowledged', alert);
  }
  
  /**
   * Resolve an alert
   */
  resolveAlert(alertId: string, resolvedBy: string, resolution?: string): void {
    const alert = this.alerts.get(alertId);
    if (!alert || alert.status === 'resolved') {
      throw new Error(`Cannot resolve alert ${alertId}`);
    }
    
    alert.status = 'resolved';
    alert.resolvedAt = new Date();
    alert.metadata.resolvedBy = resolvedBy;
    if (resolution) {
      alert.metadata.resolution = resolution;
    }
    
    this.emit('alertResolved', alert);
  }
  
  /**
   * Get active alerts
   */
  getActiveAlerts(filters: {
    severity?: AlertSeverity;
    component?: string;
    type?: string;
  } = {}): Alert[] {
    return Array.from(this.alerts.values())
      .filter(alert => {
        if (alert.status !== 'active') return false;
        if (filters.severity && alert.severity !== filters.severity) return false;
        if (filters.component && alert.component !== filters.component) return false;
        if (filters.type && alert.type !== filters.type) return false;
        return true;
      })
      .sort((a, b) => {
        const severityOrder = { emergency: 4, critical: 3, warning: 2, info: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });
  }
  
  /**
   * Get metrics
   */
  getMetrics(names?: string[]): Metric[] {
    const allMetrics = Array.from(this.metrics.values());
    return names ? allMetrics.filter(m => names.includes(m.name)) : allMetrics;
  }
  
  /**
   * Get health status
   */
  getHealthStatus(): {
    overall: 'healthy' | 'degraded' | 'unhealthy';
    components: HealthCheck[];
    summary: {
      healthy: number;
      degraded: number;
      unhealthy: number;
      unknown: number;
    };
  } {
    const components = Array.from(this.healthChecks.values());
    
    const summary = {
      healthy: components.filter(c => c.status === 'healthy').length,
      degraded: components.filter(c => c.status === 'degraded').length,
      unhealthy: components.filter(c => c.status === 'unhealthy').length,
      unknown: components.filter(c => c.status === 'unknown').length
    };
    
    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (summary.unhealthy > 0) {
      overall = 'unhealthy';
    } else if (summary.degraded > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }
    
    return { overall, components, summary };
  }
  
  /**
   * Generate SLA report
   */
  async generateSLAReport(period: { start: Date; end: Date }): Promise<SLAReport> {
    const metrics = await this.getMetricsForPeriod(period);
    
    // Calculate availability
    const uptimeMetric = metrics.find(m => m.name === 'system.uptime');
    const totalTime = period.end.getTime() - period.start.getTime();
    const uptime = uptimeMetric ? this.calculateUptime(uptimeMetric, period) : totalTime;
    const availability = (uptime / totalTime) * 100;
    
    // Calculate response time
    const latencyMetric = metrics.find(m => m.name === 'system.latency');
    const avgResponseTime = latencyMetric ? this.calculateAverage(latencyMetric) : 0;
    
    // Calculate throughput
    const throughputMetric = metrics.find(m => m.name === 'system.throughput');
    const avgThroughput = throughputMetric ? this.calculateAverage(throughputMetric) : 0;
    
    // Calculate error rate
    const errorMetric = metrics.find(m => m.name === 'system.error_rate');
    const avgErrorRate = errorMetric ? this.calculateAverage(errorMetric) : 0;
    
    // Get incidents
    const incidents = this.getIncidentsForPeriod(period);
    const totalDowntime = incidents.reduce((sum, incident) => sum + incident.duration, 0);
    const mttr = incidents.length > 0 ? totalDowntime / incidents.length : 0;
    
    return {
      period,
      targets: {
        availability: {
          target: this.config.sla.targets.availability,
          actual: availability,
          status: availability >= this.config.sla.targets.availability ? 'met' : 'missed'
        },
        responseTime: {
          target: this.config.sla.targets.responseTime,
          actual: avgResponseTime,
          status: avgResponseTime <= this.config.sla.targets.responseTime ? 'met' : 'missed'
        },
        throughput: {
          target: this.config.sla.targets.throughput,
          actual: avgThroughput,
          status: avgThroughput >= this.config.sla.targets.throughput ? 'met' : 'missed'
        },
        errorRate: {
          target: this.config.sla.targets.errorRate,
          actual: avgErrorRate,
          status: avgErrorRate <= this.config.sla.targets.errorRate ? 'met' : 'missed'
        }
      },
      incidents: {
        count: incidents.length,
        totalDowntime,
        mttr
      },
      compliance: {
        overall: this.calculateOverallCompliance({
          availability: availability >= this.config.sla.targets.availability,
          responseTime: avgResponseTime <= this.config.sla.targets.responseTime,
          throughput: avgThroughput >= this.config.sla.targets.throughput,
          errorRate: avgErrorRate <= this.config.sla.targets.errorRate
        }),
        breakdown: {
          availability: availability >= this.config.sla.targets.availability ? 100 : 0,
          responseTime: avgResponseTime <= this.config.sla.targets.responseTime ? 100 : 0,
          throughput: avgThroughput >= this.config.sla.targets.throughput ? 100 : 0,
          errorRate: avgErrorRate <= this.config.sla.targets.errorRate ? 100 : 0
        }
      }
    };
  }
  
  /**
   * Get dashboards
   */
  getDashboards(): MonitoringDashboard[] {
    return Array.from(this.dashboards.values());
  }
  
  /**
   * Create custom dashboard
   */
  createDashboard(dashboard: Omit<MonitoringDashboard, 'id'>): MonitoringDashboard {
    const newDashboard: MonitoringDashboard = {
      ...dashboard,
      id: `dashboard-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
    
    this.dashboards.set(newDashboard.id, newDashboard);
    this.emit('dashboardCreated', newDashboard);
    
    return newDashboard;
  }
  
  // Private methods
  
  private initializeMetrics(): void {
    const systemMetrics = [
      { name: 'system.cpu_usage', type: 'gauge', description: 'CPU usage percentage', unit: 'percent' },
      { name: 'system.memory_usage', type: 'gauge', description: 'Memory usage percentage', unit: 'percent' },
      { name: 'system.latency', type: 'histogram', description: 'Request latency', unit: 'milliseconds' },
      { name: 'system.throughput', type: 'gauge', description: 'Requests per second', unit: 'rps' },
      { name: 'system.error_rate', type: 'gauge', description: 'Error rate percentage', unit: 'percent' },
      { name: 'system.uptime', type: 'counter', description: 'System uptime', unit: 'seconds' },
      { name: 'agents.active_count', type: 'gauge', description: 'Number of active agents', unit: 'count' },
      { name: 'agents.task_queue_size', type: 'gauge', description: 'Task queue size', unit: 'count' },
      { name: 'workflows.active_count', type: 'gauge', description: 'Active workflows', unit: 'count' },
      { name: 'workflows.completion_rate', type: 'gauge', description: 'Workflow completion rate', unit: 'percent' }
    ];
    
    systemMetrics.forEach(metric => {
      this.metrics.set(metric.name, {
        ...metric,
        type: metric.type as any,
        points: []
      });
    });
  }
  
  private initializeHealthChecks(): void {
    const healthChecks = [
      { id: 'orchestrator', name: 'Parallel Orchestrator', component: 'orchestrator' },
      { id: 'analytics', name: 'Real-time Analytics', component: 'analytics' },
      { id: 'optimizer', name: 'Performance Optimizer', component: 'optimizer' },
      { id: 'workflow-engine', name: 'Workflow Engine', component: 'workflow' },
      { id: 'database', name: 'Database Connection', component: 'database' },
      { id: 'external-apis', name: 'External APIs', component: 'external' }
    ];
    
    healthChecks.forEach(check => {
      this.healthChecks.set(check.id, {
        ...check,
        status: 'unknown',
        lastCheck: new Date(),
        responseTime: 0
      });
    });
  }
  
  private initializeDashboards(): void {
    const systemDashboard: MonitoringDashboard = {
      id: 'system-overview',
      name: 'System Overview',
      description: 'High-level system metrics and health status',
      widgets: [
        {
          id: 'cpu-usage',
          type: 'metric',
          title: 'CPU Usage',
          position: { x: 0, y: 0, width: 3, height: 2 },
          config: {
            metrics: ['system.cpu_usage'],
            chartType: 'gauge',
            timeWindow: 5
          }
        },
        {
          id: 'memory-usage',
          type: 'metric',
          title: 'Memory Usage',
          position: { x: 3, y: 0, width: 3, height: 2 },
          config: {
            metrics: ['system.memory_usage'],
            chartType: 'gauge',
            timeWindow: 5
          }
        },
        {
          id: 'throughput',
          type: 'chart',
          title: 'Throughput',
          position: { x: 0, y: 2, width: 6, height: 3 },
          config: {
            metrics: ['system.throughput'],
            chartType: 'line',
            timeWindow: 60
          }
        },
        {
          id: 'active-alerts',
          type: 'alert-list',
          title: 'Active Alerts',
          position: { x: 6, y: 0, width: 6, height: 5 },
          config: {}
        }
      ],
      layout: { columns: 12, rows: 8 },
      refreshInterval: 30,
      timeRange: {
        start: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        end: new Date()
      }
    };
    
    this.dashboards.set(systemDashboard.id, systemDashboard);
  }
  
  private async collectMetrics(): Promise<void> {
    try {
      const timestamp = new Date();
      
      // Collect system metrics
      await this.collectSystemMetrics(timestamp);
      
      // Collect agent metrics
      await this.collectAgentMetrics(timestamp);
      
      // Collect workflow metrics
      await this.collectWorkflowMetrics(timestamp);
      
      // Update health checks
      await this.updateHealthChecks();
      
      // Check thresholds and create alerts
      this.checkThresholds();
      
    } catch (error) {
      console.error('Error collecting metrics:', error instanceof Error ? error.message : String(error));
      this.createAlert(
        'metric-collection-error',
        'warning',
        'Metric Collection Failed',
        `Failed to collect metrics: ${error instanceof Error ? error.message : String(error)}`,
        'monitoring'
      );
    }
  }
  
  private async collectSystemMetrics(timestamp: Date): Promise<void> {
    // Mock system metrics (in production, these would come from actual system monitoring)
    const cpuUsage = Math.random() * 100;
    const memoryUsage = Math.random() * 100;
    const latency = Math.random() * 1000;
    const throughput = Math.random() * 100;
    const errorRate = Math.random() * 5;
    
    this.recordMetric('system.cpu_usage', cpuUsage, {}, timestamp);
    this.recordMetric('system.memory_usage', memoryUsage, {}, timestamp);
    this.recordMetric('system.latency', latency, {}, timestamp);
    this.recordMetric('system.throughput', throughput, {}, timestamp);
    this.recordMetric('system.error_rate', errorRate, {}, timestamp);
    this.recordMetric('system.uptime', Date.now() / 1000, {}, timestamp);
  }
  
  private async collectAgentMetrics(timestamp: Date): Promise<void> {
    try {
      const metrics = await this.orchestrator.getMetrics();
      
      this.recordMetric('agents.active_count', metrics.totalAgents || 0, {}, timestamp);
      this.recordMetric('agents.task_queue_size', metrics.totalAgents || 0, {}, timestamp);
      this.recordMetric('agents.success_rate', metrics.successRate || 0, {}, timestamp);
      this.recordMetric('agents.avg_execution_time', 0, {}, timestamp);
    } catch (error) {
      console.warn('Failed to collect agent metrics:', error);
    }
  }
  
  private async collectWorkflowMetrics(timestamp: Date): Promise<void> {
    try {
      // Mock workflow metrics (would integrate with actual workflow engine)
      const activeWorkflows = Math.floor(Math.random() * 10);
      const completionRate = 85 + Math.random() * 15;
      
      this.recordMetric('workflows.active_count', activeWorkflows, {}, timestamp);
      this.recordMetric('workflows.completion_rate', completionRate, {}, timestamp);
    } catch (error) {
      console.warn('Failed to collect workflow metrics:', error);
    }
  }
  
  private async updateHealthChecks(): Promise<void> {
    const checks = Array.from(this.healthChecks.values());
    
    for (const check of checks) {
      const startTime = Date.now();
      
      try {
        let status: 'healthy' | 'degraded' | 'unhealthy';
        
        switch (check.component) {
          case 'orchestrator':
            status = this.orchestrator ? 'healthy' : 'unhealthy';
            break;
          case 'analytics':
            status = this.analytics ? 'healthy' : 'unhealthy';
            break;
          case 'optimizer':
            status = this.optimizer ? 'healthy' : 'unhealthy';
            break;
          case 'workflow':
            status = this.workflowEngine ? 'healthy' : 'unhealthy';
            break;
          default:
            status = 'healthy'; // Mock status
        }
        
        check.status = status;
        check.lastCheck = new Date();
        check.responseTime = Date.now() - startTime;
        check.message = status === 'healthy' ? 'Component is functioning normally' : 'Component has issues';
        
      } catch (error) {
        check.status = 'unhealthy';
        check.lastCheck = new Date();
        check.responseTime = Date.now() - startTime;
        check.message = error instanceof Error ? error.message : String(error);
        check.details = { error: error.toString() };
      }
    }
  }
  
  private checkThresholds(): void {
    const thresholds = this.config.thresholds;
    
    // Check CPU usage
    const cpuMetric = this.metrics.get('system.cpu_usage');
    if (cpuMetric && cpuMetric.points.length > 0) {
      const latestCpu = cpuMetric.points[cpuMetric.points.length - 1].value;
      
      if (latestCpu > thresholds.cpu.critical) {
        this.createAlert(
          'high-cpu-usage',
          'critical',
          'Critical CPU Usage',
          `CPU usage is ${latestCpu.toFixed(1)}%, exceeding critical threshold of ${thresholds.cpu.critical}%`,
          'system',
          { value: latestCpu, threshold: thresholds.cpu.critical }
        );
      } else if (latestCpu > thresholds.cpu.warning) {
        this.createAlert(
          'high-cpu-usage',
          'warning',
          'High CPU Usage',
          `CPU usage is ${latestCpu.toFixed(1)}%, exceeding warning threshold of ${thresholds.cpu.warning}%`,
          'system',
          { value: latestCpu, threshold: thresholds.cpu.warning }
        );
      }
    }
    
    // Check memory usage
    const memoryMetric = this.metrics.get('system.memory_usage');
    if (memoryMetric && memoryMetric.points.length > 0) {
      const latestMemory = memoryMetric.points[memoryMetric.points.length - 1].value;
      
      if (latestMemory > thresholds.memory.critical) {
        this.createAlert(
          'high-memory-usage',
          'critical',
          'Critical Memory Usage',
          `Memory usage is ${latestMemory.toFixed(1)}%, exceeding critical threshold of ${thresholds.memory.critical}%`,
          'system',
          { value: latestMemory, threshold: thresholds.memory.critical }
        );
      } else if (latestMemory > thresholds.memory.warning) {
        this.createAlert(
          'high-memory-usage',
          'warning',
          'High Memory Usage',
          `Memory usage is ${latestMemory.toFixed(1)}%, exceeding warning threshold of ${thresholds.memory.warning}%`,
          'system',
          { value: latestMemory, threshold: thresholds.memory.warning }
        );
      }
    }
    
    // Check error rate
    const errorMetric = this.metrics.get('system.error_rate');
    if (errorMetric && errorMetric.points.length > 0) {
      const latestErrorRate = errorMetric.points[errorMetric.points.length - 1].value;
      
      if (latestErrorRate > thresholds.errorRate.critical) {
        this.createAlert(
          'high-error-rate',
          'critical',
          'Critical Error Rate',
          `Error rate is ${latestErrorRate.toFixed(1)}%, exceeding critical threshold of ${thresholds.errorRate.critical}%`,
          'system',
          { value: latestErrorRate, threshold: thresholds.errorRate.critical }
        );
      } else if (latestErrorRate > thresholds.errorRate.warning) {
        this.createAlert(
          'high-error-rate',
          'warning',
          'High Error Rate',
          `Error rate is ${latestErrorRate.toFixed(1)}%, exceeding warning threshold of ${thresholds.errorRate.warning}%`,
          'system',
          { value: latestErrorRate, threshold: thresholds.errorRate.warning }
        );
      }
    }
  }
  
  private async processAlerts(): Promise<void> {
    const activeAlerts = this.getActiveAlerts();
    
    for (const alert of activeAlerts) {
      if (this.shouldSuppressAlert(alert)) {
        alert.status = 'suppressed';
        continue;
      }
      
      await this.processAlertEscalation(alert);
    }
  }
  
  private shouldSuppressAlert(alert: Alert): boolean {
    return this.config.alerting.suppressionRules.some(rule => {
      if (!rule.enabled) return false;
      
      const conditions = rule.conditions;
      
      if (conditions.alertType && conditions.alertType !== alert.type) return false;
      if (conditions.component && conditions.component !== alert.component) return false;
      if (conditions.severity && conditions.severity !== alert.severity) return false;
      
      if (conditions.timeWindow) {
        const now = new Date();
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        
        if (currentTime < conditions.timeWindow.start || currentTime > conditions.timeWindow.end) {
          return false;
        }
      }
      
      return true;
    });
  }
  
  private async processAlertEscalation(alert: Alert): Promise<void> {
    const policy = this.config.alerting.escalationPolicy;
    const alertAge = Date.now() - alert.timestamp.getTime();
    
    for (const level of policy.levels) {
      if (alert.escalationLevel >= level.level) continue;
      
      const shouldEscalate = level.conditions.severity.includes(alert.severity) &&
                           alertAge >= level.delay * 60 * 1000;
      
      if (shouldEscalate) {
        alert.escalationLevel = level.level;
        
        for (const channelId of level.channels) {
          await this.sendNotification(alert, channelId);
        }
      }
    }
  }
  
  private async sendNotification(alert: Alert, channelId: string): Promise<void> {
    const channel = this.config.alerting.channels.find(c => c.id === channelId);
    if (!channel || !channel.enabled) return;
    
    const notification: NotificationRecord = {
      channel: channelId,
      timestamp: new Date(),
      status: 'sent'
    };
    
    try {
      switch (channel.type) {
        case 'email':
          await this.sendEmailNotification(alert, channel);
          break;
        case 'slack':
          await this.sendSlackNotification(alert, channel);
          break;
        case 'webhook':
          await this.sendWebhookNotification(alert, channel);
          break;
        default:
          console.log(`📢 Alert notification (${channel.type}): ${alert.title}`);
      }
      
      notification.status = 'delivered';
    } catch (error) {
      notification.status = 'failed';
      notification.error = error instanceof Error ? error.message : String(error);
      console.error(`Failed to send notification via ${channel.type}:`, error);
    }
    
    alert.notificationsSent.push(notification);
  }
  
  private async sendEmailNotification(alert: Alert, channel: AlertChannel): Promise<void> {
    // Mock email sending
    console.log(`📧 Email notification sent to ${channel.config.recipients?.join(', ')}: ${alert.title}`);
  }
  
  private async sendSlackNotification(alert: Alert, channel: AlertChannel): Promise<void> {
    // Mock Slack notification
    console.log(`💬 Slack notification sent to ${channel.config.channel}: ${alert.title}`);
  }
  
  private async sendWebhookNotification(alert: Alert, channel: AlertChannel): Promise<void> {
    // Mock webhook notification
    console.log(`🔗 Webhook notification sent to ${channel.config.url}: ${alert.title}`);
  }
  
  private setupEventListeners(): void {
    // Listen to orchestrator events
    this.orchestrator.on('taskCompleted', (task) => {
      this.recordMetric('tasks.completed', 1, { type: task.type });
    });
    
    this.orchestrator.on('taskFailed', (task, error) => {
      this.recordMetric('tasks.failed', 1, { type: task.type });
      this.createAlert(
        'task-failure',
        'warning',
        'Task Failed',
        `Task ${task.id} failed: ${error instanceof Error ? error.message : String(error)}`,
        'orchestrator',
        { taskId: task.id, taskType: task.type }
      );
    });
    
    // Listen to workflow events
    this.workflowEngine.on('workflowFailed', (execution) => {
      this.createAlert(
        'workflow-failure',
        'critical',
        'Workflow Failed',
        `Workflow ${execution.workflowId} failed: ${execution.error instanceof Error ? execution.error.message : String(execution.error)}`,
        'workflow',
        { workflowId: execution.workflowId, executionId: execution.id }
      );
    });
  }
  
  private async initializeIntegrations(): Promise<void> {
    const integrations = this.config.integrations;
    
    if (integrations.prometheus?.enabled) {
      console.log('🔗 Prometheus integration initialized');
    }
    
    if (integrations.grafana?.enabled) {
      console.log('📊 Grafana integration initialized');
    }
    
    if (integrations.datadog?.enabled) {
      console.log('🐕 Datadog integration initialized');
    }
    
    if (integrations.newrelic?.enabled) {
      console.log('📈 New Relic integration initialized');
    }
  }
  
  private async getMetricsForPeriod(period: { start: Date; end: Date }): Promise<Metric[]> {
    return Array.from(this.metrics.values()).map(metric => ({
      ...metric,
      points: metric.points.filter(p => 
        p.timestamp >= period.start && p.timestamp <= period.end
      )
    }));
  }
  
  private calculateUptime(metric: Metric, period: { start: Date; end: Date }): number {
    // Simplified uptime calculation
    const totalTime = period.end.getTime() - period.start.getTime();
    return totalTime; // Mock: assume 100% uptime
  }
  
  private calculateAverage(metric: Metric): number {
    if (metric.points.length === 0) return 0;
    const sum = metric.points.reduce((acc, point) => acc + point.value, 0);
    return sum / metric.points.length;
  }
  
  private getIncidentsForPeriod(period: { start: Date; end: Date }): Array<{ duration: number }> {
    // Mock incidents data
    return [
      { duration: 5 }, // 5 minutes
      { duration: 12 } // 12 minutes
    ];
  }
  
  private calculateOverallCompliance(targets: Record<string, boolean>): number {
    const metTargets = Object.values(targets).filter(Boolean).length;
    const totalTargets = Object.values(targets).length;
    return totalTargets > 0 ? (metTargets / totalTargets) * 100 : 0;
  }
}

// Default configuration
export const defaultMonitoringConfig: MonitoringConfig = {
  collection: {
    interval: 30000, // 30 seconds
    retention: {
      metrics: 30, // 30 days
      logs: 7, // 7 days
      alerts: 90 // 90 days
    },
    batchSize: 100
  },
  alerting: {
    enabled: true,
    channels: [
      {
        id: 'default-console',
        type: 'webhook',
        name: 'Console Logging',
        config: {},
        enabled: true
      }
    ],
    escalationPolicy: {
      id: 'default-policy',
      name: 'Default Escalation Policy',
      levels: [
        {
          level: 1,
          delay: 0, // Immediate
          channels: ['default-console'],
          conditions: {
            severity: ['warning', 'critical', 'emergency'],
            duration: 0
          }
        },
        {
          level: 2,
          delay: 15, // 15 minutes
          channels: ['default-console'],
          conditions: {
            severity: ['critical', 'emergency'],
            duration: 15
          }
        }
      ]
    },
    suppressionRules: []
  },
  thresholds: {
    cpu: { warning: 70, critical: 90 },
    memory: { warning: 80, critical: 95 },
    latency: { warning: 1000, critical: 5000 },
    errorRate: { warning: 5, critical: 10 },
    throughput: { warning: 10, critical: 5 },
    successRate: { warning: 95, critical: 90 }
  },
  sla: {
    enabled: true,
    targets: {
      availability: 99.9, // 99.9%
      responseTime: 500, // 500ms
      throughput: 100, // 100 rps
      errorRate: 1 // 1%
    },
    reportingInterval: 24 // 24 hours
  },
  integrations: {}
};

export default MonitoringAndAlertingSystem;