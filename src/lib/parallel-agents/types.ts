// Core Types for Enterprise Parallel Agents System

export interface TaskMetrics {
  queued: number;
  processing: number;
  completed: number;
  failed: number;
  retrying: number;
  active?: number;
}

export interface AgentInfo {
  id: string;
  name: string;
  status: 'idle' | 'busy' | 'overloaded' | 'failed';
  currentTasks: number;
  maxCapacity: number;
  performance: {
    successRate: number;
    averageResponseTime: number;
    totalTasksCompleted: number;
  };
  capabilities: string[];
  clusterId: string;
}

export interface ClusterInfo {
  id: string;
  name: string;
  type: string;
  agents: AgentInfo[];
  utilization: number;
  performance: {
    throughput: number;
    successRate: number;
    averageResponseTime: number;
  };
  status: 'active' | 'inactive' | 'scaling';
}

export interface OrchestrationMetrics {
  totalClusters: number;
  totalAgents: number;
  activeAgents: number;
  queuedTasks: number;
  runningTasks: number;
  completedTasks: number;
  totalTasks: number;
  averageUtilization: number;
  overallSuccessRate: number;
  averageResponseTime: number;
  averageExecutionTime: number;
  throughput: {
    current: number;
    average: number;
    target: number;
  };
}

export interface DistributionMetrics {
  totalDistributed: number;
  averageDistributionTime: number;
  distributionSuccessRate: number;
  tasksByType: Record<string, number>;
  tasksByPriority: Record<string, number>;
  agentUtilization: Record<string, number>;
  loadBalancingEfficiency: number;
}

export interface PerformanceSnapshot {
  timestamp: number;
  system: {
    totalClusters: number;
    totalAgents: number;
    activeAgents: number;
    averageUtilization: number;
    overallSuccessRate: number;
    averageResponseTime: number;
  };
  clusters: ClusterInfo[];
  activeExecutions: number;
  throughput: number;
  responseTime: number;
  errorRate: number;
  resourceUsage: {
    cpu: number;
    memory: number;
    network: number;
  };
  resources: {
    cpu: number;
    memory: number;
    network: number;
  };
  agents: AgentInfo[];
  tasks: TaskMetrics;
}

export interface OptimizationRecommendation {
  id: string;
  type: 'scaling' | 'rebalancing' | 'configuration' | 'resource';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  category: string;
  expectedImprovement: {
    metric: string;
    improvement: number;
    unit: string;
  };
  implementation: {
    steps: string[];
    estimatedTime: number;
    complexity: 'low' | 'medium' | 'high';
  };
  impact: {
    performance: number;
    cost: number;
    reliability: number;
  };
}

export interface CostAnalysis {
  totalCost: number;
  costPerTask: number;
  costEfficiencyScore: number;
  benchmarkComparison: number;
  breakdown: {
    compute: number;
    storage: number;
    network: number;
    management: number;
  };
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  metadata: {
    version: string;
    author: string;
    created: number;
    updated: number;
    tags: string[];
  };
  category?: string;
  definition?: Partial<WorkflowDefinition>;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: 'task' | 'parallel' | 'sequential' | 'condition' | 'loop' | 'bulk' | 'wait' | 'webhook';
  description?: string;
  config: Record<string, any>;
  dependencies: string[];
  outputs: Record<string, any>;
  
  // Task configuration
  taskConfig?: {
    type: TaskType;
    priority: TaskPriority;
    agentType: string;
    inputs: Record<string, any>;
    timeout?: number;
    retryAttempts?: number;
  };
  
  // Parallel/Sequential configuration
  steps?: WorkflowStep[];
  
  // Condition configuration
  condition?: {
    expression: string;
    trueStep?: WorkflowStep;
    falseStep?: WorkflowStep;
  };
  
  // Loop configuration
  loop?: {
    condition: string;
    maxIterations: number;
    step: WorkflowStep;
  };
  
  // Bulk operation configuration
  bulkConfig?: {
    type: BulkOperationType;
    targets: string[];
    batchSize: number;
    maxConcurrency: number;
  };
  
  // Wait configuration
  waitConfig?: {
    type?: 'time' | 'condition' | 'event';
    value?: any;
    duration?: number;
    condition?: string;
  };
  
  // Webhook configuration
  webhookConfig?: {
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
  };
  
  // Error handling
  onError?: {
    action: 'fail' | 'retry' | 'skip' | 'fallback';
    fallbackStep?: WorkflowStep;
    maxRetries?: number;
  };
  
  // Output mapping
  outputMapping?: Record<string, string>;
}

export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  steps: WorkflowStep[];
  triggers: WorkflowTrigger[];
  configuration: {
    timeout: number;
    retryPolicy: {
      maxRetries: number;
      backoffMultiplier: number;
    };
    parallelism: {
      maxConcurrentSteps: number;
      resourceLimits: {
        cpu: number;
        memory: number;
      };
    };
  };
  inputSchema: Record<string, any>;
  outputSchema: Record<string, any>;
  inputs?: Record<string, {
    type: string;
    required?: boolean;
    description?: string;
  }>;
}

export interface WorkflowTrigger {
  id: string;
  type: 'schedule' | 'webhook' | 'file' | 'event';
  config: Record<string, any>;
  enabled: boolean;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  endTime?: number;
  duration?: number;
  progress: {
    currentStep?: string;
    completedSteps: string[];
    totalSteps: number;
    percentage: number;
    failedSteps?: number;
  };
  outputs: Record<string, any>;
  error?: string;
  variables: Record<string, any>;
  stepResults: Map<string, StepExecutionResult>;
}

export interface StepExecutionResult {
  stepId: string;
  status: 'completed' | 'failed' | 'skipped' | 'running';
  startTime: number;
  endTime: number;
  duration?: number;
  outputs: Record<string, any>;
  error?: string | { message: string; details: any };
  metadata?: Record<string, any>;
}

export interface WorkflowMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  averageStepsPerExecution: number;
  resourceUtilization: {
    cpu: number;
    memory: number;
  };
  stepMetrics: Record<string, {
    executions: number;
    averageTime: number;
    successRate: number;
    averageDuration?: number;
    errorRate?: number;
  }>;
}

// Task-related types
export type TaskType = 'analysis' | 'generation' | 'testing' | 'security' | 'optimization' | 'custom';
export type TaskPriority = 'low' | 'medium' | 'high' | 'critical';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type BulkOperationType = 'code-review' | 'refactor' | 'test-generation' | 'documentation' | 'security-scan' | 'optimization';

export interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical';
  components: {
    orchestrator: 'healthy' | 'warning' | 'critical';
    distributor: 'healthy' | 'warning' | 'critical';
    analytics: 'healthy' | 'warning' | 'critical';
    optimizer: 'healthy' | 'warning' | 'critical';
    workflows: 'healthy' | 'warning' | 'critical';
    monitoring: 'healthy' | 'warning' | 'critical';
  };
  uptime: number;
  lastCheck: number;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'info' | 'warning' | 'error' | 'critical';
  enabled: boolean;
  actions: string[];
}

export interface Alert {
  id: string;
  ruleId: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  acknowledged: boolean;
  resolved: boolean;
  metadata: Record<string, any>;
}

export interface SystemAlert extends Alert {
  source: string;
  category: 'performance' | 'security' | 'availability' | 'capacity';
}

export interface SystemRecommendation {
  id: string;
  title: string;
  description: string;
  category: 'performance' | 'cost' | 'reliability' | 'security';
  priority: 'low' | 'medium' | 'high' | 'critical';
  impact: string;
  effort: string;
  timestamp: Date;
}

export interface HealthCheck {
  component: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  responseTime?: number;
  details?: Record<string, any>;
}

export interface SLAReport {
  period: string;
  availability: number;
  responseTime: number;
  throughput: number;
  errorRate: number;
  targets: {
    availability: number;
    responseTime: number;
    throughput: number;
    errorRate: number;
  };
}

export interface MonitoringDashboard {
  id: string;
  name: string;
  widgets: Array<{
    id: string;
    type: string;
    config: Record<string, any>;
  }>;
}

export interface OptimizationStrategy {
  id: string;
  name: string;
  description: string;
  parameters: Record<string, any>;
}



// Configuration Interfaces
export interface OrchestrationConfig {
  maxConcurrentTasks: number;
  defaultTimeout: number;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
    maxBackoffTime: number;
  };
  scaling: {
    enabled: boolean;
    minAgents: number;
    maxAgents: number;
    scaleUpThreshold: number;
    scaleDownThreshold: number;
  };
}

export interface DistributionConfig {
  algorithm: 'round-robin' | 'least-loaded' | 'capability-based' | 'ml-optimized';
  loadBalancing: {
    enabled: boolean;
    rebalanceInterval: number;
    utilizationThreshold: number;
  };
  prioritization: {
    enabled: boolean;
    weights: Record<string, number>;
  };
}

export interface AnalyticsConfig {
  metricsRetention: number;
  samplingRate: number;
  realTimeUpdates: boolean;
  predictiveAnalytics: {
    enabled: boolean;
    modelUpdateInterval: number;
    predictionHorizon: number;
  };
}

// Default Configurations
export const defaultOrchestrationConfig: OrchestrationConfig = {
  maxConcurrentTasks: 1000,
  defaultTimeout: 30000,
  retryPolicy: {
    maxRetries: 3,
    backoffMultiplier: 2,
    maxBackoffTime: 60000
  },
  scaling: {
    enabled: true,
    minAgents: 10,
    maxAgents: 1000,
    scaleUpThreshold: 0.8,
    scaleDownThreshold: 0.3
  }
};

export const defaultDistributionConfig: DistributionConfig = {
  algorithm: 'ml-optimized',
  loadBalancing: {
    enabled: true,
    rebalanceInterval: 5000,
    utilizationThreshold: 0.8
  },
  prioritization: {
    enabled: true,
    weights: {
      high: 3,
      medium: 2,
      low: 1
    }
  }
};

export const defaultAnalyticsConfig: AnalyticsConfig = {
  metricsRetention: 86400000, // 24 hours
  samplingRate: 1.0,
  realTimeUpdates: true,
  predictiveAnalytics: {
    enabled: true,
    modelUpdateInterval: 300000, // 5 minutes
    predictionHorizon: 3600000 // 1 hour
  }
};

// Remove duplicate exports to avoid conflicts