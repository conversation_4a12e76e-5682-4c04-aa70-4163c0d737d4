/**
 * Intelligent Task Distribution System
 * 
 * This module implements advanced task distribution algorithms with:
 * - Machine learning-based agent selection
 * - Predictive load balancing
 * - Dynamic priority adjustment
 * - Performance-based routing
 * - Adaptive batching strategies
 */

import { EventEmitter } from '../utils/EventEmitter';
import { IntelligentTask, AgentCapability, TaskPriority } from './AdvancedParallelOrchestrator';

// Simple PriorityQueue implementation
class PriorityQueue<T> {
  private items: Array<{ item: T; priority: number }> = [];

  enqueue(item: T, priority: number = 0) {
    this.items.push({ item, priority });
    this.items.sort((a, b) => b.priority - a.priority);
  }

  dequeue(): T | undefined {
    return this.items.shift()?.item;
  }

  peek(): T | undefined {
    return this.items[0]?.item;
  }

  get size(): number {
    return this.items.length;
  }

  isEmpty(): boolean {
    return this.items.length === 0;
  }

  clear() {
    this.items = [];
  }
}

// Distribution Configuration
interface DistributionConfig {
  maxConcurrentTasks: number;
  taskTimeout: number;
  retryAttempts: number;
  loadBalancing: {
    strategy: 'round-robin' | 'least-loaded' | 'capability-based';
    rebalanceInterval: number;
  };
  mlModel: {
    enabled: boolean;
    updateInterval: number;
    trainingDataSize: number;
  };
}

const defaultDistributionConfig: DistributionConfig = {
  maxConcurrentTasks: 100,
  taskTimeout: 30000,
  retryAttempts: 3,
  loadBalancing: {
    strategy: 'capability-based',
    rebalanceInterval: 5000
  },
  mlModel: {
    enabled: true,
    updateInterval: 60000,
    trainingDataSize: 1000
  }
};

// Simple ML Model stub
class MLDistributionModel {
  async initialize(): Promise<void> {
    // Stub implementation for initialization
    console.log('Initializing ML model');
    return Promise.resolve();
  }

  predict(features: any): number {
    // Simple heuristic-based prediction
    return Math.random() * 0.5 + 0.5; // Return score between 0.5-1.0
  }

  train(data: any[]) {
    // Stub implementation
    console.log('Training ML model with', data.length, 'samples');
  }

  updateWeights(weights: Map<string, number>) {
    // Stub implementation
  }
}

// Simple Load Balancer
class LoadBalancer {
  constructor(private config: any) {}

  selectAgent(agents: AgentInfo[], task: IntelligentTask): AgentInfo | null {
    const availableAgents = agents.filter(a => a.status === 'idle' || a.status === 'busy');
    if (availableAgents.length === 0) return null;

    // Simple least-loaded strategy
    return availableAgents.reduce((best, current) => 
      current.currentLoad < best.currentLoad ? current : best
    );
  }
}

// Additional missing types
interface TaskRequest {
  task: IntelligentTask;
  timestamp: number;
  priority: number;
}

interface DistributionRecord {
  taskId: string;
  agentId: string;
  timestamp: number;
  success: boolean;
  duration: number;
}

interface LoadPrediction {
  agentId: string;
  predictedLoad: number;
  confidence: number;
  timestamp: number;
}

// Task Distribution Types
export interface DistributionStrategy {
  name: string;
  description: string;
  algorithm: (tasks: IntelligentTask[], agents: AgentInfo[]) => TaskAssignment[];
  metrics: DistributionMetrics;
}

export interface AgentInfo {
  id: string;
  type: string;
  status: 'idle' | 'busy' | 'overloaded' | 'failed';
  capabilities: AgentCapability;
  currentLoad: number; // 0-1
  queueLength: number;
  averageResponseTime: number;
  successRate: number;
  lastActivity: Date;
  healthScore: number; // 0-1
  specializations: string[];
  performance: {
    tasksCompleted: number;
    averageExecutionTime: number;
    errorRate: number;
    throughput: number; // tasks per minute
  };
}

export interface TaskAssignment {
  taskId: string;
  agentId: string;
  priority: number;
  estimatedStartTime: Date;
  estimatedCompletionTime: Date;
  confidence: number; // 0-1
  reasoning: string;
}

export interface DistributionMetrics {
  totalDistributed: number;
  averageDistributionTime: number;
  distributionSuccessRate: number;
  tasksByType: Record<string, number>;
  tasksByPriority: Record<string, number>;
  agentUtilization: Record<string, number>;
  loadBalancingEfficiency: number;
  totalAssignments?: number;
  averageAssignmentTime?: number;
  loadBalanceScore?: number; // 0-1, higher is better
  utilizationEfficiency?: number; // 0-1
  predictionAccuracy?: number; // 0-1
  bottleneckCount?: number;
}

export interface LoadPrediction {
  agentId: string;
  predictedLoad: number;
  timeHorizon: number; // minutes
  confidence: number;
  factors: string[];
}

export interface OptimizationResult {
  strategy: string;
  improvement: number; // percentage
  metrics: DistributionMetrics;
  recommendations: string[];
}

/**
 * Intelligent Task Distributor
 * 
 * Implements advanced algorithms for optimal task-to-agent assignment
 */
export class IntelligentTaskDistributor extends EventEmitter {
  private config: DistributionConfig;
  private agents: Map<string, AgentInfo> = new Map();
  private taskQueue: PriorityQueue<TaskRequest> = new PriorityQueue();
  private distributionHistory: DistributionRecord[] = [];
  private performanceMetrics: DistributionMetrics;
  private mlModel: MLDistributionModel;
  private loadBalancer: LoadBalancer;
  private isRunning: boolean = false;
  private distributionInterval?: NodeJS.Timeout;
  private taskHistory: Map<string, IntelligentTask[]> = new Map();
  private performanceHistory: Map<string, number[]> = new Map();
  private distributionStrategies: Map<string, DistributionStrategy> = new Map();
  private currentStrategy = 'ml-optimized';
  private learningEnabled = true;
  
  // ML Model State (simplified representation)
  private modelWeights: Map<string, number> = new Map();
  private featureImportance: Map<string, number> = new Map();
  private predictionCache: Map<string, LoadPrediction> = new Map();
  
  constructor(config: Partial<DistributionConfig> = {}) {
    super();
    this.config = { ...defaultDistributionConfig, ...config };
    this.performanceMetrics = this.initializeMetrics();
    this.mlModel = new MLDistributionModel();
    this.loadBalancer = new LoadBalancer(this.config.loadBalancing);

    try {
      this.setupEventListeners();
      this.initializeStrategies();
      this.initializeMLModel();
    } catch (error) {
      console.error('Error initializing IntelligentTaskDistributor:', error);
    }
  }

  /**
   * Start the task distributor
   */
  async start(): Promise<void> {
    if (this.isRunning) return;

    try {
      this.isRunning = true;
      await this.mlModel.initialize();

      // Start distribution processing
      this.distributionInterval = setInterval(() => {
        this.processTaskQueue();
      }, 100); // Process every 100ms

      this.emit('started');
    } catch (error) {
      console.error('Error starting IntelligentTaskDistributor:', error);
      this.isRunning = false;
      throw error;
    }
  }

  /**
   * Stop the task distributor
   */
  async stop(): Promise<void> {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    
    if (this.distributionInterval) {
      clearInterval(this.distributionInterval);
      this.distributionInterval = undefined;
    }
    
    this.emit('stopped');
  }

  /**
   * Get current metrics
   */
  getMetrics(): DistributionMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * Get all registered agents
   */
  getAgents(): AgentInfo[] {
    return Array.from(this.agents.values());
  }
  
  /**
   * Register an agent with the distributor
   */
  registerAgent(agent: AgentInfo): void {
    this.agents.set(agent.id, agent);
    this.taskHistory.set(agent.id, []);
    this.performanceHistory.set(agent.id, []);
    
    this.emit('agentRegistered', { agentId: agent.id });
  }
  
  /**
   * Update agent information
   */
  updateAgent(agentId: string, updates: Partial<AgentInfo>): void {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }
    
    Object.assign(agent, updates);
    this.agents.set(agentId, agent);
    
    this.emit('agentUpdated', { agentId, updates });
  }
  
  /**
   * Distribute tasks to optimal agents
   */
  async distributeTasks(tasks: IntelligentTask[]): Promise<TaskAssignment[]> {
    const startTime = Date.now();
    
    // Get current strategy
    const strategy = this.distributionStrategies.get(this.currentStrategy);
    if (!strategy) {
      throw new Error(`Strategy ${this.currentStrategy} not found`);
    }
    
    // Get available agents
    const availableAgents = Array.from(this.agents.values())
      .filter(agent => agent.status !== 'failed');
    
    if (availableAgents.length === 0) {
      throw new Error('No available agents for task distribution');
    }
    
    // Apply distribution algorithm
    const assignments = strategy.algorithm(tasks, availableAgents);
    
    // Update metrics
    const distributionTime = Date.now() - startTime;
    strategy.metrics.totalAssignments += assignments.length;
    strategy.metrics.averageAssignmentTime = 
      (strategy.metrics.averageAssignmentTime + distributionTime) / 2;
    
    // Learn from assignments if ML is enabled
    if (this.learningEnabled) {
      await this.learnFromAssignments(tasks, assignments);
    }
    
    this.emit('tasksDistributed', {
      taskCount: tasks.length,
      assignments: assignments.length,
      strategy: this.currentStrategy,
      distributionTime
    });
    
    return assignments;
  }
  
  /**
   * Find the optimal agent for a specific task
   */
  async findOptimalAgent(task: IntelligentTask): Promise<TaskAssignment | null> {
    const assignments = await this.distributeTasks([task]);
    return assignments.length > 0 ? assignments[0] : null;
  }
  
  /**
   * Predict future load for agents
   */
  async predictLoad(timeHorizonMinutes = 30): Promise<LoadPrediction[]> {
    const predictions: LoadPrediction[] = [];
    
    for (const [agentId, agent] of this.agents) {
      const prediction = await this.predictAgentLoad(agent, timeHorizonMinutes);
      predictions.push(prediction);
      
      // Cache prediction
      this.predictionCache.set(`${agentId}-${timeHorizonMinutes}`, prediction);
    }
    
    return predictions;
  }
  
  /**
   * Optimize distribution strategy
   */
  async optimizeStrategy(): Promise<OptimizationResult> {
    const currentMetrics = this.getCurrentMetrics();
    const strategies = Array.from(this.distributionStrategies.keys());
    
    let bestStrategy = this.currentStrategy;
    let bestScore = this.calculateStrategyScore(currentMetrics);
    
    // Test different strategies
    for (const strategyName of strategies) {
      if (strategyName === this.currentStrategy) continue;
      
      const testMetrics = await this.simulateStrategy(strategyName);
      const score = this.calculateStrategyScore(testMetrics);
      
      if (score > bestScore) {
        bestStrategy = strategyName;
        bestScore = score;
      }
    }
    
    const improvement = ((bestScore - this.calculateStrategyScore(currentMetrics)) / this.calculateStrategyScore(currentMetrics)) * 100;
    
    if (bestStrategy !== this.currentStrategy) {
      this.currentStrategy = bestStrategy;
      this.emit('strategyOptimized', {
        oldStrategy: this.currentStrategy,
        newStrategy: bestStrategy,
        improvement
      });
    }
    
    return {
      strategy: bestStrategy,
      improvement,
      metrics: this.distributionStrategies.get(bestStrategy)!.metrics,
      recommendations: this.generateRecommendations()
    };
  }
  
  /**
   * Get current distribution metrics
   */
  getCurrentMetrics(): DistributionMetrics {
    const strategy = this.distributionStrategies.get(this.currentStrategy);
    return strategy ? strategy.metrics : this.createEmptyMetrics();
  }
  
  /**
   * Get agent performance analytics
   */
  getAgentAnalytics(agentId?: string): any {
    if (agentId) {
      const agent = this.agents.get(agentId);
      if (!agent) return null;
      
      return {
        agent,
        taskHistory: this.taskHistory.get(agentId) || [],
        performanceHistory: this.performanceHistory.get(agentId) || [],
        predictions: this.predictionCache.get(`${agentId}-30`)
      };
    }
    
    // Return analytics for all agents
    const analytics: any = {};
    for (const [id, agent] of this.agents) {
      analytics[id] = this.getAgentAnalytics(id);
    }
    return analytics;
  }
  
  /**
   * Set distribution strategy
   */
  setStrategy(strategyName: string): void {
    if (!this.distributionStrategies.has(strategyName)) {
      throw new Error(`Strategy ${strategyName} not found`);
    }
    
    const oldStrategy = this.currentStrategy;
    this.currentStrategy = strategyName;
    
    this.emit('strategyChanged', {
      oldStrategy,
      newStrategy: strategyName
    });
  }
  
  /**
   * Enable or disable machine learning
   */
  setLearningEnabled(enabled: boolean): void {
    this.learningEnabled = enabled;
    this.emit('learningToggled', { enabled });
  }
  
  // Private Methods
  
  private initializeStrategies(): void {
    // Round Robin Strategy
    this.distributionStrategies.set('round-robin', {
      name: 'Round Robin',
      description: 'Distributes tasks evenly across all agents',
      algorithm: this.roundRobinAlgorithm.bind(this),
      metrics: this.createEmptyMetrics()
    });
    
    // Load-Based Strategy
    this.distributionStrategies.set('load-based', {
      name: 'Load Based',
      description: 'Assigns tasks to agents with lowest current load',
      algorithm: this.loadBasedAlgorithm.bind(this),
      metrics: this.createEmptyMetrics()
    });
    
    // Capability-Based Strategy
    this.distributionStrategies.set('capability-based', {
      name: 'Capability Based',
      description: 'Matches tasks to agents based on capabilities',
      algorithm: this.capabilityBasedAlgorithm.bind(this),
      metrics: this.createEmptyMetrics()
    });
    
    // ML-Optimized Strategy
    this.distributionStrategies.set('ml-optimized', {
      name: 'ML Optimized',
      description: 'Uses machine learning for optimal task assignment',
      algorithm: this.mlOptimizedAlgorithm.bind(this),
      metrics: this.createEmptyMetrics()
    });
    
    // Hybrid Strategy
    this.distributionStrategies.set('hybrid', {
      name: 'Hybrid',
      description: 'Combines multiple strategies for optimal performance',
      algorithm: this.hybridAlgorithm.bind(this),
      metrics: this.createEmptyMetrics()
    });
  }
  
  private initializeMLModel(): void {
    // Initialize feature weights
    this.modelWeights.set('agent_load', 0.3);
    this.modelWeights.set('capability_match', 0.25);
    this.modelWeights.set('response_time', 0.2);
    this.modelWeights.set('success_rate', 0.15);
    this.modelWeights.set('queue_length', 0.1);
    
    // Initialize feature importance
    this.featureImportance.set('task_complexity', 0.3);
    this.featureImportance.set('agent_specialization', 0.25);
    this.featureImportance.set('historical_performance', 0.2);
    this.featureImportance.set('current_load', 0.15);
    this.featureImportance.set('priority_level', 0.1);
  }
  
  // Distribution Algorithms
  
  private roundRobinAlgorithm(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    const assignments: TaskAssignment[] = [];
    let agentIndex = 0;
    
    for (const task of tasks) {
      const agent = agents[agentIndex % agents.length];
      
      assignments.push({
        taskId: task.id,
        agentId: agent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(Date.now() + agent.queueLength * 1000),
        estimatedCompletionTime: new Date(Date.now() + (agent.queueLength + task.metadata.estimatedDuration) * 1000),
        confidence: 0.7,
        reasoning: 'Round robin distribution'
      });
      
      agentIndex++;
    }
    
    return assignments;
  }
  
  private loadBasedAlgorithm(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    const assignments: TaskAssignment[] = [];
    
    // Sort agents by current load (ascending)
    const sortedAgents = [...agents].sort((a, b) => a.currentLoad - b.currentLoad);
    
    for (const task of tasks) {
      // Find agent with lowest load that can handle the task
      const suitableAgent = sortedAgents.find(agent => 
        this.canAgentHandleTask(agent, task)
      ) || sortedAgents[0];
      
      assignments.push({
        taskId: task.id,
        agentId: suitableAgent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(Date.now() + suitableAgent.queueLength * 1000),
        estimatedCompletionTime: new Date(Date.now() + (suitableAgent.queueLength + task.metadata.estimatedDuration) * 1000),
        confidence: 0.8,
        reasoning: `Assigned to agent with lowest load (${suitableAgent.currentLoad.toFixed(2)})`
      });
      
      // Update agent load for next iteration
      suitableAgent.currentLoad += 0.1;
      suitableAgent.queueLength++;
    }
    
    return assignments;
  }
  
  private capabilityBasedAlgorithm(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    const assignments: TaskAssignment[] = [];
    
    for (const task of tasks) {
      // Score agents based on capability match
      const agentScores = agents.map(agent => ({
        agent,
        score: this.calculateCapabilityScore(agent, task)
      }));
      
      // Sort by score (descending)
      agentScores.sort((a, b) => b.score - a.score);
      
      const bestAgent = agentScores[0].agent;
      const confidence = agentScores[0].score;
      
      assignments.push({
        taskId: task.id,
        agentId: bestAgent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(Date.now() + bestAgent.queueLength * 1000),
        estimatedCompletionTime: new Date(Date.now() + (bestAgent.queueLength + task.metadata.estimatedDuration) * 1000),
        confidence,
        reasoning: `Best capability match (score: ${confidence.toFixed(2)})`
      });
    }
    
    return assignments;
  }
  
  private mlOptimizedAlgorithm(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    const assignments: TaskAssignment[] = [];
    
    for (const task of tasks) {
      // Calculate ML-based scores for each agent
      const agentScores = agents.map(agent => ({
        agent,
        score: this.calculateMLScore(agent, task)
      }));
      
      // Sort by ML score (descending)
      agentScores.sort((a, b) => b.score - a.score);
      
      const bestAgent = agentScores[0].agent;
      const confidence = agentScores[0].score;
      
      assignments.push({
        taskId: task.id,
        agentId: bestAgent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(Date.now() + bestAgent.queueLength * 1000),
        estimatedCompletionTime: new Date(Date.now() + (bestAgent.queueLength + task.metadata.estimatedDuration) * 1000),
        confidence,
        reasoning: `ML-optimized assignment (score: ${confidence.toFixed(2)})`
      });
    }
    
    return assignments;
  }
  
  private hybridAlgorithm(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    const assignments: TaskAssignment[] = [];
    
    for (const task of tasks) {
      // Combine multiple scoring methods
      const agentScores = agents.map(agent => {
        const capabilityScore = this.calculateCapabilityScore(agent, task);
        const loadScore = 1 - agent.currentLoad; // Invert load (lower is better)
        const mlScore = this.calculateMLScore(agent, task);
        
        // Weighted combination
        const hybridScore = 
          capabilityScore * 0.4 +
          loadScore * 0.3 +
          mlScore * 0.3;
        
        return {
          agent,
          score: hybridScore,
          breakdown: { capabilityScore, loadScore, mlScore }
        };
      });
      
      // Sort by hybrid score (descending)
      agentScores.sort((a, b) => b.score - a.score);
      
      const bestAgent = agentScores[0].agent;
      const confidence = agentScores[0].score;
      const breakdown = agentScores[0].breakdown;
      
      assignments.push({
        taskId: task.id,
        agentId: bestAgent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(Date.now() + bestAgent.queueLength * 1000),
        estimatedCompletionTime: new Date(Date.now() + (bestAgent.queueLength + task.metadata.estimatedDuration) * 1000),
        confidence,
        reasoning: `Hybrid assignment (capability: ${breakdown.capabilityScore.toFixed(2)}, load: ${breakdown.loadScore.toFixed(2)}, ML: ${breakdown.mlScore.toFixed(2)})`
      });
    }
    
    return assignments;
  }
  
  // Utility Methods
  
  private calculatePriority(task: IntelligentTask): number {
    const priorityWeights = {
      critical: 1.0,
      high: 0.8,
      medium: 0.6,
      low: 0.4
    };
    
    return priorityWeights[task.priority.level] * task.priority.weight;
  }
  
  private canAgentHandleTask(agent: AgentInfo, task: IntelligentTask): boolean {
    // Check if agent can handle the task based on capabilities and current load
    if (agent.status === 'failed') return false;
    if (agent.currentLoad >= 1.0) return false;

    // Check if agent has required capabilities
    const capabilityScore = this.calculateCapabilityScore(agent, task);
    return capabilityScore > 0.5; // Require at least 50% capability match
  }
  
  private calculateCapabilityScore(agent: AgentInfo, task: IntelligentTask): number {
    // Handle cases where properties might not exist
    const agentCapabilities = agent.specializations || [];
    const requiredCapabilities = task.requiredCapabilities || [];

    if (requiredCapabilities.length === 0) return 1.0;

    let score = 0;
    for (const capability of requiredCapabilities) {
      if (agentCapabilities.includes(capability)) {
        // Use a default proficiency if capabilities object doesn't exist
        const proficiency = (agent.capabilities && typeof agent.capabilities === 'object' && 'proficiency' in agent.capabilities)
          ? (agent.capabilities as any).proficiency
          : 1.0;
        score += proficiency;
      }
    }

    return score / requiredCapabilities.length;
  }
  
  private calculateMLScore(agent: AgentInfo, task: IntelligentTask): number {
    // Extract features
    const features = {
      agent_load: agent.currentLoad,
      capability_match: this.calculateCapabilityScore(agent, task),
      response_time: agent.averageResponseTime / 10000, // Normalize
      success_rate: agent.successRate,
      queue_length: agent.queueLength / 10, // Normalize
      task_complexity: task.estimatedComplexity,
      priority_level: this.calculatePriority(task)
    };
    
    // Calculate weighted score
    let score = 0;
    for (const [feature, value] of Object.entries(features)) {
      const weight = this.modelWeights.get(feature) || 0;
      score += weight * value;
    }
    
    return Math.max(0, Math.min(1, score)); // Clamp to [0, 1]
  }
  
  private async predictAgentLoad(agent: AgentInfo, timeHorizonMinutes: number): Promise<LoadPrediction> {
    // Simple prediction based on current trends
    const currentLoad = agent.currentLoad;
    const queueLength = agent.queueLength;
    const avgTaskTime = agent.performance.averageExecutionTime;
    
    // Predict load based on queue and historical patterns
    const predictedLoad = Math.min(1, currentLoad + (queueLength * avgTaskTime) / (timeHorizonMinutes * 60 * 1000));
    
    return {
      agentId: agent.id,
      predictedLoad,
      timeHorizon: timeHorizonMinutes,
      confidence: 0.8, // Simplified confidence
      factors: ['current_load', 'queue_length', 'historical_performance']
    };
  }
  
  private async learnFromAssignments(tasks: IntelligentTask[], assignments: TaskAssignment[]): Promise<void> {
    try {
      // Update model weights based on assignment outcomes
      // This is a simplified learning mechanism

      for (const assignment of assignments) {
        const task = tasks.find(t => t.id === assignment.taskId);
        const agent = this.agents.get(assignment.agentId);

        if (task && agent) {
          // Record assignment for future learning
          const taskHistory = this.taskHistory.get(assignment.agentId) || [];
          taskHistory.push(task);
          this.taskHistory.set(assignment.agentId, taskHistory);

          // Update feature importance based on assignment confidence
          this.featureImportance.set('capability_match',
            (this.featureImportance.get('capability_match') || 0) + assignment.confidence * 0.1
          );

          this.featureImportance.set('agent_load',
            (this.featureImportance.get('agent_load') || 0) + assignment.confidence * 0.05
          );
        }
      }

      // Train the model with the assignments data
      const trainingData = assignments.map((assignment, index) => ({
        task: tasks[index],
        assignment,
        outcome: assignment.confidence
      }));

      this.mlModel.train(trainingData);
    } catch (error) {
      console.error('Error learning from assignments:', error);
    }
  }
  
  private calculateStrategyScore(metrics: DistributionMetrics): number {
    // Weighted score based on multiple metrics
    return (
      metrics.loadBalanceScore * 0.3 +
      metrics.utilizationEfficiency * 0.3 +
      metrics.predictionAccuracy * 0.2 +
      (1 - metrics.bottleneckCount / 10) * 0.2 // Normalize bottlenecks
    );
  }
  
  private async simulateStrategy(strategyName: string): Promise<DistributionMetrics> {
    // Simulate strategy performance with historical data
    // This is a simplified simulation
    return this.createEmptyMetrics();
  }
  
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const metrics = this.getCurrentMetrics();
    
    if (metrics.loadBalanceScore < 0.7) {
      recommendations.push('Consider rebalancing agent workloads');
    }
    
    if (metrics.utilizationEfficiency < 0.8) {
      recommendations.push('Optimize task batching to improve utilization');
    }
    
    if (metrics.bottleneckCount > 3) {
      recommendations.push('Scale up agents to reduce bottlenecks');
    }
    
    return recommendations;
  }
  
  private createEmptyMetrics(): DistributionMetrics {
    return {
      totalAssignments: 0,
      averageAssignmentTime: 0,
      loadBalanceScore: 0,
      utilizationEfficiency: 0,
      predictionAccuracy: 0,
      bottleneckCount: 0
    };
  }

  private initializeMetrics(): DistributionMetrics {
    return {
      totalDistributed: 0,
      averageDistributionTime: 0,
      distributionSuccessRate: 1.0,
      tasksByType: {},
      tasksByPriority: {},
      agentUtilization: {},
      loadBalancingEfficiency: 1.0
    };
  }

  private setupEventListeners(): void {
    // Setup internal event listeners
    this.on('error', (error) => {
      console.error('IntelligentTaskDistributor error:', error);
    });
  }

  private processTaskQueue(): void {
    // Process pending tasks in queue
    try {
      while (!this.taskQueue.isEmpty()) {
        const taskRequest = this.taskQueue.dequeue();
        if (taskRequest) {
          // Process the task request
          console.log('Processing task:', taskRequest.task.id);
        }
      }
    } catch (error) {
      console.error('Error processing task queue:', error);
    }
  }

  private initializeStrategies(): void {
    // Initialize distribution strategies
    this.distributionStrategies.set('round-robin', {
      name: 'Round Robin',
      description: 'Distributes tasks evenly across agents',
      algorithm: this.roundRobinDistribution.bind(this),
      metrics: this.createEmptyMetrics()
    });

    this.distributionStrategies.set('least-loaded', {
      name: 'Least Loaded',
      description: 'Assigns tasks to agents with lowest current load',
      algorithm: this.leastLoadedDistribution.bind(this),
      metrics: this.createEmptyMetrics()
    });

    this.distributionStrategies.set('ml-optimized', {
      name: 'ML Optimized',
      description: 'Uses machine learning for optimal task assignment',
      algorithm: this.mlOptimizedDistribution.bind(this),
      metrics: this.createEmptyMetrics()
    });
  }

  private initializeMLModel(): void {
    // Initialize ML model weights
    this.modelWeights.set('agent_load', 0.3);
    this.modelWeights.set('capability_match', 0.25);
    this.modelWeights.set('response_time', 0.2);
    this.modelWeights.set('success_rate', 0.15);
    this.modelWeights.set('queue_length', 0.1);
  }

  private roundRobinDistribution(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    // Simple round-robin distribution
    const assignments: TaskAssignment[] = [];

    if (agents.length === 0) return assignments;

    for (let i = 0; i < tasks.length; i++) {
      const agentIndex = i % agents.length;
      const agent = agents[agentIndex];

      assignments.push({
        taskId: tasks[i].id,
        agentId: agent.id,
        priority: this.calculatePriority(tasks[i]),
        estimatedStartTime: new Date(),
        estimatedCompletionTime: new Date(Date.now() + 60000), // Placeholder: 1 minute
        confidence: 0.7,
        reasoning: 'Round-robin assignment'
      });
    }

    return assignments;
  }

  private leastLoadedDistribution(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    // Assign tasks to agents with the lowest current load
    const assignments: TaskAssignment[] = [];

    if (agents.length === 0) return assignments;

    // Sort agents by current load (ascending)
    const sortedAgents = [...agents].sort((a, b) =>
      (a.currentLoad || 0) - (b.currentLoad || 0)
    );

    for (const task of tasks) {
      // Get the least loaded agent
      const agent = sortedAgents[0];

      assignments.push({
        taskId: task.id,
        agentId: agent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(),
        estimatedCompletionTime: new Date(Date.now() + 60000), // Placeholder: 1 minute
        confidence: 0.8,
        reasoning: 'Least-loaded assignment'
      });

      // Update agent load and re-sort for next assignment
      agent.currentLoad = (agent.currentLoad || 0) + 0.1;
      sortedAgents.sort((a, b) => (a.currentLoad || 0) - (b.currentLoad || 0));
    }

    return assignments;
  }

  private mlOptimizedDistribution(tasks: IntelligentTask[], agents: AgentInfo[]): TaskAssignment[] {
    // Use ML model to optimize task distribution
    const assignments: TaskAssignment[] = [];

    if (agents.length === 0) return assignments;

    for (const task of tasks) {
      // Calculate scores for each agent
      const agentScores = agents.map(agent => ({
        agent,
        score: this.calculateMLScore(agent, task)
      }));

      // Sort by score (descending)
      agentScores.sort((a, b) => b.score - a.score);

      // Select the best agent
      const bestAgent = agentScores[0].agent;

      assignments.push({
        taskId: task.id,
        agentId: bestAgent.id,
        priority: this.calculatePriority(task),
        estimatedStartTime: new Date(),
        estimatedCompletionTime: new Date(Date.now() + 60000), // Placeholder: 1 minute
        confidence: agentScores[0].score,
        reasoning: 'ML-optimized assignment'
      });
    }

    return assignments;
  }

  private getCurrentMetrics(): DistributionMetrics {
    return { ...this.performanceMetrics };
  }
}

// Export singleton instance with error handling
let intelligentDistributorInstance: IntelligentTaskDistributor | null = null;

try {
  intelligentDistributorInstance = new IntelligentTaskDistributor();
} catch (error) {
  console.error('Failed to initialize IntelligentTaskDistributor:', error);
}

export const intelligentDistributor = intelligentDistributorInstance;

// Re-export types
export type { DistributionConfig, DistributionMetrics, AgentInfo } from './types';