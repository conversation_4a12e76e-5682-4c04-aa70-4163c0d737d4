import { invoke } from '@tauri-apps/api/core';

export interface MCPServerConfig {
  id: string;
  name: string;
  type: 'filesystem' | 'git' | 'database' | 'api' | 'custom';
  command: string;
  args: string[];
  env?: Record<string, string>;
  cwd?: string;
  capabilities: string[];
  status: 'stopped' | 'starting' | 'running' | 'error';
  pid?: number;
  port?: number;
  config: Record<string, any>;
}

export interface MCPServerMetrics {
  id: string;
  uptime: number;
  requests: {
    total: number;
    successful: number;
    failed: number;
    avgResponseTime: number;
  };
  resources: {
    cpu: number;
    memory: number;
    handles: number;
  };
  lastError?: string;
}

export class MCPServerManager {
  private static instance: MCPServerManager;
  private servers: Map<string, MCPServerConfig> = new Map();
  private metrics: Map<string, MCPServerMetrics> = new Map();
  private listeners: Set<(servers: MCPServerConfig[]) => void> = new Set();

  public static getInstance(): MCPServerManager {
    if (!MCPServerManager.instance) {
      MCPServerManager.instance = new MCPServerManager();
    }
    return MCPServerManager.instance;
  }

  constructor() {
    this.loadServers();
    this.startMetricsCollection();
  }

  // Server Management
  public async addServer(config: Omit<MCPServerConfig, 'id' | 'status'>): Promise<MCPServerConfig> {
    const server: MCPServerConfig = {
      ...config,
      id: this.generateId(),
      status: 'stopped'
    };

    this.servers.set(server.id, server);
    await this.saveServers();
    this.notifyListeners();
    
    return server;
  }

  public async removeServer(serverId: string): Promise<void> {
    const server = this.servers.get(serverId);
    if (server && server.status === 'running') {
      await this.stopServer(serverId);
    }
    
    this.servers.delete(serverId);
    this.metrics.delete(serverId);
    await this.saveServers();
    this.notifyListeners();
  }

  public async startServer(serverId: string): Promise<void> {
    const server = this.servers.get(serverId);
    if (!server) throw new Error(`Server ${serverId} not found`);

    try {
      server.status = 'starting';
      this.notifyListeners();

      // For now, we'll simulate starting since the backend doesn't have start_mcp_server
      // In a real implementation, this would start the actual MCP server process
      const result = { pid: Math.floor(Math.random() * 10000), port: undefined };

      server.pid = result.pid;
      server.port = result.port;
      server.status = 'running';

      // Initialize metrics
      this.metrics.set(serverId, {
        id: serverId,
        uptime: 0,
        requests: { total: 0, successful: 0, failed: 0, avgResponseTime: 0 },
        resources: { cpu: 0, memory: 0, handles: 0 }
      });

      await this.saveServers();
      this.notifyListeners();
    } catch (error) {
      server.status = 'error';
      this.notifyListeners();
      throw error;
    }
  }

  public async stopServer(serverId: string): Promise<void> {
    const server = this.servers.get(serverId);
    if (!server) throw new Error(`Server ${serverId} not found`);

    try {
      // Simulate stopping the server
      // In a real implementation, this would stop the actual MCP server process
      
      server.status = 'stopped';
      server.pid = undefined;
      server.port = undefined;
      
      this.metrics.delete(serverId);
      await this.saveServers();
      this.notifyListeners();
    } catch (error) {
      server.status = 'error';
      this.notifyListeners();
      throw error;
    }
  }

  public async restartServer(serverId: string): Promise<void> {
    await this.stopServer(serverId);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await this.startServer(serverId);
  }

  // Server Communication
  public async sendRequest(serverId: string, method: string, params: any): Promise<any> {
    const server = this.servers.get(serverId);
    if (!server || server.status !== 'running') {
      throw new Error(`Server ${serverId} is not running`);
    }

    const startTime = Date.now();
    
    try {
      // Simulate MCP request
      // In a real implementation, this would send the request to the MCP server
      const result = { success: true, data: {} };

      // Update metrics
      const metrics = this.metrics.get(serverId);
      if (metrics) {
        const responseTime = Date.now() - startTime;
        metrics.requests.total++;
        metrics.requests.successful++;
        metrics.requests.avgResponseTime = 
          (metrics.requests.avgResponseTime + responseTime) / 2;
      }

      return result;
    } catch (error) {
      // Update error metrics
      const metrics = this.metrics.get(serverId);
      if (metrics) {
        metrics.requests.total++;
        metrics.requests.failed++;
        metrics.lastError = error instanceof Error ? error.message : 'Unknown error';
      }
      throw error;
    }
  }

  // File System Operations
  public async readFile(serverId: string, path: string): Promise<string> {
    return this.sendRequest(serverId, 'fs/read', { path });
  }

  public async writeFile(serverId: string, path: string, content: string): Promise<void> {
    return this.sendRequest(serverId, 'fs/write', { path, content });
  }

  public async listDirectory(serverId: string, path: string): Promise<string[]> {
    return this.sendRequest(serverId, 'fs/list', { path });
  }

  // Git Operations
  public async gitStatus(serverId: string): Promise<any> {
    return this.sendRequest(serverId, 'git/status', {});
  }

  public async gitCommit(serverId: string, message: string): Promise<void> {
    return this.sendRequest(serverId, 'git/commit', { message });
  }

  // Database Operations
  public async dbQuery(serverId: string, query: string, params?: any[]): Promise<any> {
    return this.sendRequest(serverId, 'db/query', { query, params });
  }

  // Server Discovery and Templates
  public getServerTemplates(): Array<Omit<MCPServerConfig, 'id' | 'status'>> {
    return [
      {
        name: 'Filesystem Server',
        type: 'filesystem',
        command: 'npx',
        args: ['@modelcontextprotocol/server-filesystem', '/workspace'],
        capabilities: ['read_file', 'write_file', 'list_directory', 'create_directory'],
        config: { basePath: '/workspace', readonly: false }
      },
      {
        name: 'Git Server',
        type: 'git',
        command: 'npx',
        args: ['@modelcontextprotocol/server-git'],
        capabilities: ['git_status', 'git_commit', 'git_push', 'git_pull', 'git_diff'],
        config: { repository: '.', branch: 'main' }
      },
      {
        name: 'SQLite Database',
        type: 'database',
        command: 'npx',
        args: ['@modelcontextprotocol/server-sqlite', 'database.db'],
        capabilities: ['query', 'insert', 'update', 'delete', 'schema'],
        config: { database: 'database.db', readonly: false }
      },
      {
        name: 'HTTP API Server',
        type: 'api',
        command: 'node',
        args: ['custom-api-server.js'],
        capabilities: ['http_get', 'http_post', 'http_put', 'http_delete'],
        config: { baseUrl: 'http://localhost:3000', timeout: 5000 }
      }
    ];
  }

  // Metrics and Monitoring
  public getServerMetrics(serverId: string): MCPServerMetrics | undefined {
    return this.metrics.get(serverId);
  }

  public getAllMetrics(): MCPServerMetrics[] {
    return Array.from(this.metrics.values());
  }

  private async startMetricsCollection(): Promise<void> {
    setInterval(async () => {
      for (const [serverId, server] of this.servers) {
        if (server.status === 'running' && server.pid) {
          try {
            // Simulate process info
            const processInfo = {
              cpu: Math.random() * 100,
              memory: Math.random() * 1000,
              handles: Math.floor(Math.random() * 100)
            };

            const metrics = this.metrics.get(serverId);
            if (metrics) {
              metrics.uptime += 5; // 5 second intervals
              metrics.resources = processInfo;
            }
          } catch (error) {
            console.warn(`Failed to get metrics for server ${serverId}:`, error);
          }
        }
      }
    }, 5000);
  }

  // Configuration Management
  public async exportConfiguration(): Promise<string> {
    const config = {
      servers: Array.from(this.servers.values()),
      exportedAt: new Date().toISOString(),
      version: '1.0.0'
    };
    return JSON.stringify(config, null, 2);
  }

  public async importConfiguration(configJson: string): Promise<void> {
    try {
      const config = JSON.parse(configJson);
      
      if (!config.servers || !Array.isArray(config.servers)) {
        throw new Error('Invalid configuration format');
      }

      // Stop all running servers
      for (const [serverId, server] of this.servers) {
        if (server.status === 'running') {
          await this.stopServer(serverId);
        }
      }

      // Clear existing servers
      this.servers.clear();
      this.metrics.clear();

      // Import new servers
      for (const serverConfig of config.servers) {
        const server: MCPServerConfig = {
          ...serverConfig,
          id: this.generateId(),
          status: 'stopped'
        };
        this.servers.set(server.id, server);
      }

      await this.saveServers();
      this.notifyListeners();
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error}`);
    }
  }

  // Event Listeners
  public addListener(listener: (servers: MCPServerConfig[]) => void): void {
    this.listeners.add(listener);
  }

  public removeListener(listener: (servers: MCPServerConfig[]) => void): void {
    this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    const servers = Array.from(this.servers.values());
    this.listeners.forEach(listener => listener(servers));
  }

  // Data Persistence
  private async loadServers(): Promise<void> {
    try {
      // Use the existing MCP list command
      const result = await invoke<any>('mcp_list');
      if (result.success && result.servers) {
        // Convert the API format to our internal format
        for (const server of result.servers) {
          const mcpServer: MCPServerConfig = {
            id: server.name,
            name: server.name,
            type: this.inferServerType(server),
            command: server.command || '',
            args: server.args || [],
            env: server.env || {},
            capabilities: [],
            status: 'stopped',
            config: {}
          };
          this.servers.set(mcpServer.id, mcpServer);
        }
      }
    } catch (error) {
      console.warn('Failed to load MCP servers:', error);
    }
  }

  private inferServerType(server: any): MCPServerConfig['type'] {
    const name = server.name.toLowerCase();
    if (name.includes('filesystem') || name.includes('file')) return 'filesystem';
    if (name.includes('git')) return 'git';
    if (name.includes('database') || name.includes('db')) return 'database';
    if (name.includes('api')) return 'api';
    return 'custom';
  }

  private async saveServers(): Promise<void> {
    // MCP servers are saved through individual add/remove commands
    // No need for a separate save operation
  }

  // Utility Methods
  private generateId(): string {
    return `mcp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public getServers(): MCPServerConfig[] {
    return Array.from(this.servers.values());
  }

  public getServer(serverId: string): MCPServerConfig | undefined {
    return this.servers.get(serverId);
  }

  public getRunningServers(): MCPServerConfig[] {
    return Array.from(this.servers.values()).filter(s => s.status === 'running');
  }

  public async testConnection(serverId: string): Promise<boolean> {
    try {
      await this.sendRequest(serverId, 'ping', {});
      return true;
    } catch {
      return false;
    }
  }

  public async getServerCapabilities(serverId: string): Promise<string[]> {
    try {
      const result = await this.sendRequest(serverId, 'capabilities', {});
      return result.capabilities || [];
    } catch {
      const server = this.servers.get(serverId);
      return server?.capabilities || [];
    }
  }
}