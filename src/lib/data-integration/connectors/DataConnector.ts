import { dataEventBus, DataEventType } from '../EventBus';

export interface ConnectionConfig {
  name: string;
  type: string;
  connectionString?: string;
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  options?: Record<string, any>;
}

export interface QueryResult<T = any> {
  rows: T[];
  rowCount: number;
  fields?: Array<{ name: string; type: string }>;
  executionTime?: number;
}

export interface DataRow {
  [key: string]: any;
}

export interface HealthStatus {
  connected: boolean;
  latency?: number;
  lastCheck: Date;
  error?: string;
}

export interface DataConnectorOptions {
  maxRetries?: number;
  retryDelay?: number;
  connectionTimeout?: number;
  queryTimeout?: number;
  poolSize?: number;
}

export abstract class DataConnector {
  protected config: ConnectionConfig;
  protected options: DataConnectorOptions;
  protected connected: boolean = false;
  protected connectionPool: any[] = [];
  
  constructor(config: ConnectionConfig, options: DataConnectorOptions = {}) {
    this.config = config;
    this.options = {
      maxRetries: 3,
      retryDelay: 1000,
      connectionTimeout: 30000,
      queryTimeout: 60000,
      poolSize: 10,
      ...options
    };
  }
  
  abstract connect(): Promise<void>;
  abstract disconnect(): Promise<void>;
  abstract query<T = DataRow>(query: string, params?: any[]): Promise<QueryResult<T>>;
  abstract stream<T = DataRow>(query: string, params?: any[]): AsyncIterable<T>;
  
  async healthCheck(): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      if (!this.connected) {
        await this.connect();
      }
      
      // Subclasses should override this with specific health check query
      await this.performHealthCheck();
      
      const latency = Date.now() - startTime;
      
      return {
        connected: true,
        latency,
        lastCheck: new Date()
      };
    } catch (error) {
      return {
        connected: false,
        lastCheck: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  protected abstract performHealthCheck(): Promise<void>;
  
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.options.maxRetries!; attempt++) {
      try {
        dataEventBus.emit(DataEventType.DATA_OPERATION_STARTED, {
          connector: this.config.name,
          operation: operationName,
          attempt
        });
        
        const result = await operation();
        
        dataEventBus.emit(DataEventType.DATA_OPERATION_COMPLETED, {
          connector: this.config.name,
          operation: operationName,
          attempt
        });
        
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        dataEventBus.emit(DataEventType.DATA_OPERATION_FAILED, {
          connector: this.config.name,
          operation: operationName,
          attempt,
          error: lastError.message
        });
        
        if (attempt < this.options.maxRetries!) {
          dataEventBus.emit(DataEventType.DATA_OPERATION_RETRY, {
            connector: this.config.name,
            operation: operationName,
            attempt,
            nextAttempt: attempt + 1,
            delay: this.options.retryDelay
          });
          
          await this.delay(this.options.retryDelay! * attempt);
        }
      }
    }
    
    throw lastError || new Error('Operation failed after all retries');
  }
  
  protected delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  protected emitConnectionEvent(type: DataEventType, metadata?: any): void {
    dataEventBus.emit(type, {
      connector: this.config.name,
      type: this.config.type,
      ...metadata
    });
  }
}