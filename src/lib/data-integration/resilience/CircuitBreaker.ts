import { dataEventBus, DataEventType } from '../EventBus';

export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

export interface CircuitBreakerOptions {
  name: string;
  failureThreshold: number;
  successThreshold: number;
  timeout: number;
  resetTimeout: number;
  monitoringPeriod: number;
  volumeThreshold: number;
  slowCallDurationThreshold?: number;
  slowCallRateThreshold?: number;
}

export interface CircuitBreakerMetrics {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  slowCalls: number;
  state: CircuitState;
  lastStateChange: Date;
  nextAttempt?: Date;
}

export class CircuitBreakerError extends Error {
  constructor(message: string, public state: CircuitState) {
    super(message);
    this.name = 'CircuitBreakerError';
  }
}

export class CircuitBreaker {
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private successCount: number = 0;
  private lastFailureTime?: Date;
  private nextAttemptTime?: Date;
  private callMetrics: Array<{ timestamp: Date; duration: number; success: boolean }> = [];
  
  constructor(private options: CircuitBreakerOptions) {
    this.options = {
      failureThreshold: 5,
      successThreshold: 3,
      timeout: 60000,
      resetTimeout: 30000,
      monitoringPeriod: 60000,
      volumeThreshold: 10,
      slowCallDurationThreshold: 5000,
      slowCallRateThreshold: 0.5,
      ...options
    };
  }
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (!this.canExecute()) {
      throw new CircuitBreakerError(
        `Circuit breaker is ${this.state}. Next attempt at ${this.nextAttemptTime}`,
        this.state
      );
    }
    
    const startTime = Date.now();
    
    try {
      const result = await this.executeWithTimeout(operation);
      this.onSuccess(Date.now() - startTime);
      return result;
    } catch (error) {
      this.onFailure(Date.now() - startTime);
      throw error;
    }
  }
  
  private canExecute(): boolean {
    switch (this.state) {
      case CircuitState.CLOSED:
        return true;
      
      case CircuitState.OPEN:
        if (this.shouldAttemptReset()) {
          this.transitionTo(CircuitState.HALF_OPEN);
          return true;
        }
        return false;
      
      case CircuitState.HALF_OPEN:
        return true;
      
      default:
        return false;
    }
  }
  
  private async executeWithTimeout<T>(operation: () => Promise<T>): Promise<T> {
    return Promise.race([
      operation(),
      new Promise<T>((_, reject) => 
        setTimeout(() => reject(new Error('Operation timeout')), this.options.timeout)
      )
    ]);
  }
  
  private onSuccess(duration: number): void {
    this.recordCall(true, duration);
    
    switch (this.state) {
      case CircuitState.CLOSED:
        this.failureCount = 0;
        break;
      
      case CircuitState.HALF_OPEN:
        this.successCount++;
        if (this.successCount >= this.options.successThreshold) {
          this.transitionTo(CircuitState.CLOSED);
        }
        break;
    }
  }
  
  private onFailure(duration: number): void {
    this.recordCall(false, duration);
    this.lastFailureTime = new Date();
    
    switch (this.state) {
      case CircuitState.CLOSED:
        this.failureCount++;
        if (this.shouldOpen()) {
          this.transitionTo(CircuitState.OPEN);
        }
        break;
      
      case CircuitState.HALF_OPEN:
        this.transitionTo(CircuitState.OPEN);
        break;
    }
  }
  
  private shouldOpen(): boolean {
    // Check failure threshold
    if (this.failureCount >= this.options.failureThreshold) {
      return true;
    }
    
    // Check volume and error rate
    const recentCalls = this.getRecentCalls();
    if (recentCalls.length >= this.options.volumeThreshold) {
      const errorRate = recentCalls.filter(c => !c.success).length / recentCalls.length;
      if (errorRate > 0.5) {
        return true;
      }
      
      // Check slow call rate
      if (this.options.slowCallDurationThreshold && this.options.slowCallRateThreshold) {
        const slowCalls = recentCalls.filter(
          c => c.duration > this.options.slowCallDurationThreshold!
        ).length;
        const slowCallRate = slowCalls / recentCalls.length;
        if (slowCallRate > this.options.slowCallRateThreshold) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  private shouldAttemptReset(): boolean {
    if (!this.nextAttemptTime) {
      return true;
    }
    return new Date() >= this.nextAttemptTime;
  }
  
  private transitionTo(newState: CircuitState): void {
    const oldState = this.state;
    this.state = newState;
    
    switch (newState) {
      case CircuitState.CLOSED:
        this.failureCount = 0;
        this.successCount = 0;
        this.nextAttemptTime = undefined;
        break;
      
      case CircuitState.OPEN:
        this.nextAttemptTime = new Date(Date.now() + this.options.resetTimeout);
        this.successCount = 0;
        break;
      
      case CircuitState.HALF_OPEN:
        this.successCount = 0;
        this.failureCount = 0;
        break;
    }
    
    // Emit state change event
    dataEventBus.emit(DataEventType.CONNECTION_LOST, {
      circuitBreaker: this.options.name,
      oldState,
      newState,
      nextAttempt: this.nextAttemptTime
    });
  }
  
  private recordCall(success: boolean, duration: number): void {
    this.callMetrics.push({
      timestamp: new Date(),
      duration,
      success
    });
    
    // Clean up old metrics
    const cutoff = new Date(Date.now() - this.options.monitoringPeriod);
    this.callMetrics = this.callMetrics.filter(m => m.timestamp > cutoff);
  }
  
  private getRecentCalls() {
    const cutoff = new Date(Date.now() - this.options.monitoringPeriod);
    return this.callMetrics.filter(m => m.timestamp > cutoff);
  }
  
  getMetrics(): CircuitBreakerMetrics {
    const recentCalls = this.getRecentCalls();
    const slowCalls = this.options.slowCallDurationThreshold
      ? recentCalls.filter(c => c.duration > this.options.slowCallDurationThreshold!).length
      : 0;
    
    return {
      totalCalls: recentCalls.length,
      successfulCalls: recentCalls.filter(c => c.success).length,
      failedCalls: recentCalls.filter(c => !c.success).length,
      slowCalls,
      state: this.state,
      lastStateChange: new Date(),
      nextAttempt: this.nextAttemptTime
    };
  }
  
  reset(): void {
    this.transitionTo(CircuitState.CLOSED);
    this.callMetrics = [];
  }
}

// Circuit breaker factory
export class CircuitBreakerFactory {
  private static breakers: Map<string, CircuitBreaker> = new Map();
  
  static create(options: CircuitBreakerOptions): CircuitBreaker {
    if (!this.breakers.has(options.name)) {
      this.breakers.set(options.name, new CircuitBreaker(options));
    }
    return this.breakers.get(options.name)!;
  }
  
  static get(name: string): CircuitBreaker | undefined {
    return this.breakers.get(name);
  }
  
  static getAll(): Map<string, CircuitBreaker> {
    return new Map(this.breakers);
  }
  
  static reset(name: string): void {
    const breaker = this.breakers.get(name);
    if (breaker) {
      breaker.reset();
    }
  }
  
  static resetAll(): void {
    this.breakers.forEach(breaker => breaker.reset());
  }
}