import { useFeatureFlagsStore } from '@/stores/featureFlagsStore';

// Re-export types and defaults from the separate file to avoid circular dependencies
export { type FeatureFlag, type FeatureFlags, defaultFeatureFlags } from './featureFlagsTypes';

// Hook for React components
export function useFeatureFlags() {
  const { flags, updateFlags, isEnabled, resetToDefaults } = useFeatureFlagsStore();
  return {
    flags,
    updateFlags,
    isEnabled,
    resetToDefaults
  };
}

// Non-hook functions for use outside React components
export function getFeatureFlags() {
  return useFeatureFlagsStore.getState().flags;
}

export function updateFeatureFlags(updates: Partial<import('./featureFlagsTypes').FeatureFlags>) {
  useFeatureFlagsStore.getState().updateFlags(updates);
}

export function isFeatureEnabled(feature: keyof import('./featureFlagsTypes').FeatureFlags): boolean {
  return useFeatureFlagsStore.getState().isEnabled(feature);
}