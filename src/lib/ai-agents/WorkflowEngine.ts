import { Workflow, WorkflowTask, WorkflowTrigger, ExecutionContext, WorkflowStatus } from './types';
import { TaskMaster } from './TaskMaster';
import { AIAgentLibrary } from './AgentLibrary';

export class WorkflowEngine {
  private static instance: WorkflowEngine;
  private taskMaster: TaskMaster;
  private agentLibrary: AIAgentLibrary;
  private activeWorkflows: Map<string, Workflow> = new Map();
  private scheduledWorkflows: Map<string, NodeJS.Timeout> = new Map();

  public static getInstance(): WorkflowEngine {
    if (!WorkflowEngine.instance) {
      WorkflowEngine.instance = new WorkflowEngine();
    }
    return WorkflowEngine.instance;
  }

  constructor() {
    this.taskMaster = TaskMaster.getInstance();
    this.agentLibrary = AIAgentLibrary.getInstance();
  }

  // Predefined workflow templates
  createCodeQualityWorkflow(projectPath: string): Workflow {
    return {
      id: `code-quality-${Date.now()}`,
      name: 'Code Quality Assessment',
      description: 'Comprehensive code quality analysis workflow',
      version: '1.0.0',
      tasks: [
        {
          id: 'security-scan',
          agent_id: 'security-scanner-v1',
          position: { x: 100, y: 100 },
          dependencies: [],
          conditions: [{ type: 'always' }],
          retry_policy: { max_attempts: 3, delay: 1000, backoff_strategy: 'exponential' },
          timeout: 300
        },
        {
          id: 'code-review',
          agent_id: 'code-reviewer-v1',
          position: { x: 300, y: 100 },
          dependencies: ['security-scan'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 600
        },
        {
          id: 'refactoring',
          agent_id: 'refactorer-v1',
          position: { x: 500, y: 100 },
          dependencies: ['code-review'],
          conditions: [{ type: 'custom', expression: 'code_quality_score < 0.8' }],
          retry_policy: { max_attempts: 2, delay: 2000, backoff_strategy: 'fixed' },
          timeout: 900
        },
        {
          id: 'test-generation',
          agent_id: 'unit-tester-v1',
          position: { x: 300, y: 300 },
          dependencies: ['code-review'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 3, delay: 1000, backoff_strategy: 'exponential' },
          timeout: 400
        },
        {
          id: 'documentation',
          agent_id: 'documenter-v1',
          position: { x: 700, y: 200 },
          dependencies: ['refactoring', 'test-generation'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 300
        }
      ],
      triggers: [
        {
          type: 'manual',
          config: { project_path: projectPath },
          enabled: true
        }
      ],
      variables: {
        project_path: projectPath,
        quality_threshold: 0.8,
        generate_tests: true,
        update_docs: true
      },
      settings: {
        parallel_execution: true,
        failure_strategy: 'continue',
        notification_settings: [
          {
            type: 'ui',
            trigger: 'complete',
            recipients: ['user'],
            template: 'workflow_complete'
          }
        ],
        resource_limits: {
          max_concurrent_tasks: 3,
          max_execution_time: 3600,
          max_memory_usage: 1024,
          max_token_usage: 50000
        }
      },
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  createCIWorkflow(repositoryUrl: string): Workflow {
    return {
      id: `ci-workflow-${Date.now()}`,
      name: 'Continuous Integration',
      description: 'Automated CI pipeline with testing and deployment',
      version: '1.0.0',
      tasks: [
        {
          id: 'code-checkout',
          agent_id: 'git-agent-v1',
          position: { x: 100, y: 100 },
          dependencies: [],
          conditions: [{ type: 'always' }],
          retry_policy: { max_attempts: 3, delay: 1000, backoff_strategy: 'exponential' },
          timeout: 120
        },
        {
          id: 'security-check',
          agent_id: 'security-scanner-v1',
          position: { x: 300, y: 100 },
          dependencies: ['code-checkout'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 300
        },
        {
          id: 'unit-tests',
          agent_id: 'unit-tester-v1',
          position: { x: 300, y: 300 },
          dependencies: ['code-checkout'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 3, delay: 1000, backoff_strategy: 'exponential' },
          timeout: 600
        },
        {
          id: 'integration-tests',
          agent_id: 'integration-tester-v1',
          position: { x: 500, y: 300 },
          dependencies: ['unit-tests'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 2000, backoff_strategy: 'exponential' },
          timeout: 900
        },
        {
          id: 'build',
          agent_id: 'build-agent-v1',
          position: { x: 500, y: 100 },
          dependencies: ['security-check', 'unit-tests'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 600
        },
        {
          id: 'deploy-staging',
          agent_id: 'deployment-v1',
          position: { x: 700, y: 200 },
          dependencies: ['build', 'integration-tests'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 5000, backoff_strategy: 'fixed' },
          timeout: 1200
        }
      ],
      triggers: [
        {
          type: 'git_event',
          config: { 
            repository: repositoryUrl,
            events: ['push', 'pull_request'],
            branches: ['main', 'develop']
          },
          enabled: true
        }
      ],
      variables: {
        repository_url: repositoryUrl,
        target_environment: 'staging',
        notification_channel: 'slack'
      },
      settings: {
        parallel_execution: true,
        failure_strategy: 'stop',
        notification_settings: [
          {
            type: 'slack',
            trigger: 'error',
            recipients: ['dev-team'],
            template: 'ci_failure'
          },
          {
            type: 'slack',
            trigger: 'complete',
            recipients: ['dev-team'],
            template: 'ci_success'
          }
        ],
        resource_limits: {
          max_concurrent_tasks: 4,
          max_execution_time: 7200,
          max_memory_usage: 2048,
          max_token_usage: 30000
        }
      },
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  createDataProcessingWorkflow(dataSource: string): Workflow {
    return {
      id: `data-processing-${Date.now()}`,
      name: 'Data Processing Pipeline',
      description: 'Extract, transform, and analyze data workflow',
      version: '1.0.0',
      tasks: [
        {
          id: 'data-extraction',
          agent_id: 'data-extractor-v1',
          position: { x: 100, y: 100 },
          dependencies: [],
          conditions: [{ type: 'always' }],
          retry_policy: { max_attempts: 3, delay: 2000, backoff_strategy: 'exponential' },
          timeout: 600
        },
        {
          id: 'data-validation',
          agent_id: 'data-validator-v1',
          position: { x: 300, y: 100 },
          dependencies: ['data-extraction'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 300
        },
        {
          id: 'data-transformation',
          agent_id: 'data-transformer-v1',
          position: { x: 500, y: 100 },
          dependencies: ['data-validation'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 900
        },
        {
          id: 'data-analysis',
          agent_id: 'data-analyst-v1',
          position: { x: 700, y: 100 },
          dependencies: ['data-transformation'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 1200
        },
        {
          id: 'report-generation',
          agent_id: 'report-generator-v1',
          position: { x: 900, y: 100 },
          dependencies: ['data-analysis'],
          conditions: [{ type: 'success' }],
          retry_policy: { max_attempts: 2, delay: 1000, backoff_strategy: 'linear' },
          timeout: 300
        }
      ],
      triggers: [
        {
          type: 'schedule',
          config: { 
            cron: '0 2 * * *', // Daily at 2 AM
            timezone: 'UTC'
          },
          enabled: true
        }
      ],
      variables: {
        data_source: dataSource,
        output_format: 'json',
        analysis_type: 'comprehensive'
      },
      settings: {
        parallel_execution: false,
        failure_strategy: 'stop',
        notification_settings: [
          {
            type: 'email',
            trigger: 'complete',
            recipients: ['<EMAIL>'],
            template: 'data_processing_complete'
          }
        ],
        resource_limits: {
          max_concurrent_tasks: 1,
          max_execution_time: 10800,
          max_memory_usage: 4096,
          max_token_usage: 100000
        }
      },
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Workflow execution methods
  async executeWorkflow(workflow: Workflow, context: ExecutionContext): Promise<void> {
    workflow.status = 'active';
    this.activeWorkflows.set(workflow.id, workflow);
    
    try {
      await this.taskMaster.executeWorkflow(workflow, context);
      workflow.status = 'completed';
    } catch (error) {
      workflow.status = 'failed';
      throw error;
    } finally {
      this.activeWorkflows.delete(workflow.id);
    }
  }

  async pauseWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (workflow) {
      workflow.status = 'paused';
    }
  }

  async resumeWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (workflow && workflow.status === 'paused') {
      workflow.status = 'active';
    }
  }

  async cancelWorkflow(workflowId: string): Promise<void> {
    const workflow = this.activeWorkflows.get(workflowId);
    if (workflow) {
      workflow.status = 'failed';
      this.activeWorkflows.delete(workflowId);
    }
  }

  // Workflow scheduling
  scheduleWorkflow(workflow: Workflow): void {
    workflow.triggers.forEach(trigger => {
      if (trigger.enabled && trigger.type === 'schedule') {
        const cronExpression = trigger.config.cron;
        // In a real implementation, you'd use a proper cron scheduler
        console.log(`Scheduling workflow ${workflow.id} with cron: ${cronExpression}`);
      }
    });
  }

  // Workflow validation
  validateWorkflow(workflow: Workflow): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for circular dependencies
    if (this.hasCircularDependencies(workflow.tasks)) {
      errors.push('Circular dependencies detected in workflow tasks');
    }

    // Validate agent references
    workflow.tasks.forEach(task => {
      const agent = this.agentLibrary.getAgent(task.agent_id);
      if (!agent) {
        errors.push(`Agent ${task.agent_id} not found for task ${task.id}`);
      }
    });

    // Validate resource limits
    if (workflow.settings.resource_limits.max_concurrent_tasks <= 0) {
      errors.push('Max concurrent tasks must be greater than 0');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private hasCircularDependencies(tasks: WorkflowTask[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (taskId: string): boolean => {
      if (recursionStack.has(taskId)) {
        return true;
      }
      if (visited.has(taskId)) {
        return false;
      }

      visited.add(taskId);
      recursionStack.add(taskId);

      const task = tasks.find(t => t.id === taskId);
      if (task) {
        for (const depId of task.dependencies) {
          if (hasCycle(depId)) {
            return true;
          }
        }
      }

      recursionStack.delete(taskId);
      return false;
    };

    return tasks.some(task => hasCycle(task.id));
  }

  // Workflow templates management
  getWorkflowTemplates(): Array<{ id: string; name: string; description: string; category: string }> {
    return [
      {
        id: 'code-quality',
        name: 'Code Quality Assessment',
        description: 'Comprehensive code review, security scan, and refactoring workflow',
        category: 'Development'
      },
      {
        id: 'ci-pipeline',
        name: 'CI/CD Pipeline',
        description: 'Automated testing, building, and deployment workflow',
        category: 'DevOps'
      },
      {
        id: 'data-processing',
        name: 'Data Processing Pipeline',
        description: 'Extract, transform, and analyze data workflow',
        category: 'Data'
      },
      {
        id: 'security-audit',
        name: 'Security Audit',
        description: 'Comprehensive security scanning and vulnerability assessment',
        category: 'Security'
      },
      {
        id: 'documentation-update',
        name: 'Documentation Update',
        description: 'Automated documentation generation and maintenance',
        category: 'Documentation'
      }
    ];
  }

  // Public API
  getActiveWorkflows(): Workflow[] {
    return Array.from(this.activeWorkflows.values());
  }

  getWorkflowStatus(workflowId: string): WorkflowStatus | null {
    const workflow = this.activeWorkflows.get(workflowId);
    return workflow?.status || null;
  }
}