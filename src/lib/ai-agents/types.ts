// Core AI Agent Types and Interfaces

export interface BaseAgent {
  id: string;
  name: string;
  type: AgentType;
  description: string;
  capabilities: string[];
  status: AgentStatus;
  config: AgentConfig;
  metadata: AgentMetadata;
  created_at: string;
  updated_at: string;
}

export type AgentType = 
  | 'code-reviewer'
  | 'architect'
  | 'debugger'
  | 'optimizer'
  | 'documenter'
  | 'tester'
  | 'security-scanner'
  | 'refactorer'
  | 'api-designer'
  | 'data-analyst'
  | 'devops'
  | 'ui-designer'
  | 'task-master'
  | 'orchestrator'
  | 'researcher'
  | 'translator'
  | 'validator'
  | 'monitor'
  | 'custom';

export type AgentStatus = 
  | 'idle'
  | 'running'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'waiting'
  | 'error';

export interface AgentConfig {
  model: 'sonnet' | 'opus' | 'haiku';
  temperature: number;
  max_tokens: number;
  timeout: number;
  retry_count: number;
  parallel_execution: boolean;
  dependencies: string[];
  triggers: AgentTrigger[];
  outputs: AgentOutput[];
  custom_prompts: Record<string, string>;
  tools: string[];
  mcp_servers: string[];
}

export interface AgentMetadata {
  author: string;
  version: string;
  tags: string[];
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimated_duration: number;
  success_rate: number;
  usage_count: number;
  last_used: string;
  performance_metrics: PerformanceMetrics;
}

export interface AgentTrigger {
  type: 'file_change' | 'time_based' | 'event' | 'manual' | 'dependency_complete';
  condition: string;
  parameters: Record<string, any>;
}

export interface AgentOutput {
  type: 'file' | 'report' | 'data' | 'notification' | 'event';
  format: string;
  destination: string;
  template?: string;
}

export interface PerformanceMetrics {
  average_execution_time: number;
  success_rate: number;
  error_rate: number;
  resource_usage: ResourceUsage;
  quality_score: number;
}

export interface ResourceUsage {
  cpu_usage: number;
  memory_usage: number;
  token_usage: number;
  api_calls: number;
}

// Task Management Types
export interface Task {
  id: string;
  title: string;
  description: string;
  type: TaskType;
  priority: TaskPriority;
  status: TaskStatus;
  assigned_agents: string[];
  dependencies: string[];
  inputs: TaskInput[];
  outputs: TaskOutput[];
  progress: number;
  estimated_duration: number;
  actual_duration?: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  metadata: TaskMetadata;
}

export type TaskType = 
  | 'code_review'
  | 'refactoring'
  | 'testing'
  | 'documentation'
  | 'optimization'
  | 'security_scan'
  | 'deployment'
  | 'analysis'
  | 'research'
  | 'design'
  | 'bulk_operation'
  | 'workflow'
  | 'custom';

export type TaskPriority = 'low' | 'medium' | 'high' | 'critical' | 'urgent';
export type TaskStatus = 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';

export interface TaskInput {
  name: string;
  type: 'file' | 'directory' | 'text' | 'json' | 'url' | 'reference';
  value: any;
  required: boolean;
  validation?: string;
}

export interface TaskOutput {
  name: string;
  type: 'file' | 'report' | 'data' | 'metrics' | 'notification';
  value?: any;
  path?: string;
  format?: string;
}

export interface TaskMetadata {
  tags: string[];
  category: string;
  complexity: number;
  estimated_cost: number;
  actual_cost?: number;
  quality_gates: QualityGate[];
  approval_required: boolean;
  notifications: NotificationConfig[];
}

export interface QualityGate {
  name: string;
  condition: string;
  threshold: number;
  required: boolean;
}

export interface NotificationConfig {
  type: 'email' | 'slack' | 'webhook' | 'ui';
  trigger: 'start' | 'complete' | 'error' | 'milestone';
  recipients: string[];
  template?: string;
}

// Workflow and Orchestration Types
export interface Workflow {
  id: string;
  name: string;
  description: string;
  version: string;
  tasks: WorkflowTask[];
  triggers: WorkflowTrigger[];
  variables: Record<string, any>;
  settings: WorkflowSettings;
  status: WorkflowStatus;
  created_at: string;
  updated_at: string;
}

export interface WorkflowTask {
  id: string;
  agent_id: string;
  position: { x: number; y: number };
  dependencies: string[];
  conditions: TaskCondition[];
  retry_policy: RetryPolicy;
  timeout: number;
}

export interface WorkflowTrigger {
  type: 'manual' | 'schedule' | 'webhook' | 'file_change' | 'git_event';
  config: Record<string, any>;
  enabled: boolean;
}

export interface WorkflowSettings {
  parallel_execution: boolean;
  failure_strategy: 'stop' | 'continue' | 'retry';
  notification_settings: NotificationConfig[];
  resource_limits: ResourceLimits;
}

export interface ResourceLimits {
  max_concurrent_tasks: number;
  max_execution_time: number;
  max_memory_usage: number;
  max_token_usage: number;
}

export type WorkflowStatus = 'draft' | 'active' | 'paused' | 'completed' | 'failed' | 'archived';

export interface TaskCondition {
  type: 'success' | 'failure' | 'always' | 'custom';
  expression?: string;
}

export interface RetryPolicy {
  max_attempts: number;
  delay: number;
  backoff_strategy: 'linear' | 'exponential' | 'fixed';
}

// Bulk Operations Types
export interface BulkOperation {
  id: string;
  name: string;
  type: BulkOperationType;
  targets: BulkTarget[];
  agent_template: Partial<BaseAgent>;
  batch_size: number;
  parallel_limit: number;
  progress: BulkProgress;
  results: BulkResult[];
  status: BulkStatus;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export type BulkOperationType = 
  | 'code_review_batch'
  | 'refactor_batch'
  | 'test_generation'
  | 'documentation_batch'
  | 'security_scan_batch'
  | 'optimization_batch'
  | 'translation_batch'
  | 'validation_batch'
  | 'custom_batch';

export interface BulkTarget {
  id: string;
  type: 'file' | 'directory' | 'project' | 'repository' | 'url';
  path: string;
  metadata?: Record<string, any>;
  priority?: number;
}

export interface BulkProgress {
  total: number;
  completed: number;
  failed: number;
  skipped: number;
  in_progress: number;
  percentage: number;
  estimated_remaining: number;
}

export interface BulkResult {
  target_id: string;
  status: 'success' | 'failed' | 'skipped';
  output?: any;
  error?: string;
  duration: number;
  metrics?: Record<string, number>;
}

export type BulkStatus = 'pending' | 'running' | 'paused' | 'completed' | 'failed' | 'cancelled';

// Agent Library Types
export interface AgentLibrary {
  categories: AgentCategory[];
  featured_agents: string[];
  recent_agents: string[];
  popular_agents: string[];
  custom_agents: string[];
}

export interface AgentCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  agents: string[];
  subcategories?: AgentCategory[];
}

// Execution Context
export interface ExecutionContext {
  session_id: string;
  project_path: string;
  environment: 'development' | 'staging' | 'production';
  variables: Record<string, any>;
  secrets: Record<string, string>;
  permissions: Permission[];
  resource_limits: ResourceLimits;
  logging_level: 'debug' | 'info' | 'warn' | 'error';
}

export interface Permission {
  resource: string;
  actions: string[];
  conditions?: string[];
}