import { Task, Workflow, BulkOperation, ExecutionContext, TaskStatus, WorkflowStatus, BulkStatus } from './types';
import { AIAgentLibrary } from './AgentLibrary';

export class TaskMaster {
  private static instance: TaskMaster;
  private tasks: Map<string, Task> = new Map();
  private workflows: Map<string, Workflow> = new Map();
  private bulkOperations: Map<string, BulkOperation> = new Map();
  private executionQueue: string[] = [];
  private isProcessing = false;
  private maxConcurrentTasks = 5;

  public static getInstance(): TaskMaster {
    if (!TaskMaster.instance) {
      TaskMaster.instance = new TaskMaster();
    }
    return TaskMaster.instance;
  }

  // Task Management
  public async createTask(taskData: Partial<Task>): Promise<Task> {
    const task: Task = {
      id: this.generateId(),
      title: taskData.title || 'Untitled Task',
      description: taskData.description || '',
      type: taskData.type || 'custom',
      priority: taskData.priority || 'medium',
      status: 'pending',
      assigned_agents: taskData.assigned_agents || [],
      dependencies: taskData.dependencies || [],
      inputs: taskData.inputs || [],
      outputs: taskData.outputs || [],
      progress: 0,
      estimated_duration: taskData.estimated_duration || 300,
      created_at: new Date().toISOString(),
      metadata: {
        tags: taskData.metadata?.tags || [],
        category: taskData.metadata?.category || 'general',
        complexity: taskData.metadata?.complexity || 1,
        estimated_cost: taskData.metadata?.estimated_cost || 0,
        quality_gates: taskData.metadata?.quality_gates || [],
        approval_required: taskData.metadata?.approval_required || false,
        notifications: taskData.metadata?.notifications || []
      }
    };

    this.tasks.set(task.id, task);
    this.queueTask(task.id);
    return task;
  }

  public async executeTask(taskId: string, context: ExecutionContext): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) throw new Error(`Task ${taskId} not found`);

    try {
      task.status = 'running';
      task.started_at = new Date().toISOString();
      
      // Check dependencies
      await this.checkDependencies(task);
      
      // Execute assigned agents
      for (const agentId of task.assigned_agents) {
        await this.executeAgent(agentId, task, context);
      }
      
      // Validate quality gates
      await this.validateQualityGates(task);
      
      task.status = 'completed';
      task.completed_at = new Date().toISOString();
      task.progress = 100;
      
    } catch (error) {
      task.status = 'failed';
      task.completed_at = new Date().toISOString();
      throw error;
    }
  }

  // Workflow Management
  public async createWorkflow(workflowData: Partial<Workflow>): Promise<Workflow> {
    const workflow: Workflow = {
      id: this.generateId(),
      name: workflowData.name || 'Untitled Workflow',
      description: workflowData.description || '',
      version: workflowData.version || '1.0.0',
      tasks: workflowData.tasks || [],
      triggers: workflowData.triggers || [],
      variables: workflowData.variables || {},
      settings: {
        parallel_execution: workflowData.settings?.parallel_execution || false,
        failure_strategy: workflowData.settings?.failure_strategy || 'stop',
        notification_settings: workflowData.settings?.notification_settings || [],
        resource_limits: {
          max_concurrent_tasks: 3,
          max_execution_time: 3600,
          max_memory_usage: 1024,
          max_token_usage: 10000
        }
      },
      status: 'draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.workflows.set(workflow.id, workflow);
    return workflow;
  }

  public async executeWorkflow(workflowId: string, context: ExecutionContext): Promise<void> {
    const workflow = this.workflows.get(workflowId);
    if (!workflow) throw new Error(`Workflow ${workflowId} not found`);

    try {
      workflow.status = 'active';
      
      if (workflow.settings.parallel_execution) {
        await this.executeWorkflowParallel(workflow, context);
      } else {
        await this.executeWorkflowSequential(workflow, context);
      }
      
      workflow.status = 'completed';
    } catch (error) {
      workflow.status = 'failed';
      if (workflow.settings.failure_strategy === 'stop') {
        throw error;
      }
    }
  }

  // Bulk Operations
  public async createBulkOperation(operationData: Partial<BulkOperation>): Promise<BulkOperation> {
    const operation: BulkOperation = {
      id: this.generateId(),
      name: operationData.name || 'Bulk Operation',
      type: operationData.type || 'custom_batch',
      targets: operationData.targets || [],
      agent_template: operationData.agent_template || {},
      batch_size: operationData.batch_size || 10,
      parallel_limit: operationData.parallel_limit || 3,
      progress: {
        total: operationData.targets?.length || 0,
        completed: 0,
        failed: 0,
        skipped: 0,
        in_progress: 0,
        percentage: 0,
        estimated_remaining: 0
      },
      results: [],
      status: 'pending',
      created_at: new Date().toISOString()
    };

    this.bulkOperations.set(operation.id, operation);
    return operation;
  }

  public async executeBulkOperation(operationId: string, context: ExecutionContext): Promise<void> {
    const operation = this.bulkOperations.get(operationId);
    if (!operation) throw new Error(`Bulk operation ${operationId} not found`);

    try {
      operation.status = 'running';
      operation.started_at = new Date().toISOString();

      const batches = this.createBatches(operation.targets, operation.batch_size);
      
      for (const batch of batches) {
        await this.processBatch(batch, operation, context);
        this.updateBulkProgress(operation);
      }

      operation.status = 'completed';
      operation.completed_at = new Date().toISOString();
    } catch (error) {
      operation.status = 'failed';
      operation.completed_at = new Date().toISOString();
      throw error;
    }
  }

  // Smart Agent Recommendations
  public recommendAgentsForTask(task: Task): string[] {
    const library = AIAgentLibrary.getInstance();
    const recommendations: string[] = [];

    switch (task.type) {
      case 'code_review':
        recommendations.push('code-reviewer-v1', 'security-scanner-v1');
        break;
      case 'refactoring':
        recommendations.push('refactorer-v1', 'optimizer-v1');
        break;
      case 'testing':
        recommendations.push('unit-tester-v1', 'integration-tester-v1');
        break;
      case 'documentation':
        recommendations.push('documenter-v1', 'api-doc-v1');
        break;
      case 'security_scan':
        recommendations.push('security-scanner-v1', 'code-reviewer-v1');
        break;
      case 'optimization':
        recommendations.push('optimizer-v1', 'performance-tester-v1');
        break;
      case 'deployment':
        recommendations.push('deployment-v1', 'monitoring-v1');
        break;
      case 'analysis':
        recommendations.push('data-analyst-v1', 'researcher-v1');
        break;
      default:
        // For custom tasks, recommend based on complexity and requirements
        if (task.metadata.complexity > 3) {
          recommendations.push('architect-v1', 'task-master-v1');
        } else {
          recommendations.push('code-reviewer-v1');
        }
    }

    return recommendations.filter(id => library.getAgent(id) !== undefined);
  }

  // Workflow Templates
  public getWorkflowTemplates(): Partial<Workflow>[] {
    return [
      {
        name: 'Code Quality Pipeline',
        description: 'Comprehensive code quality assessment workflow',
        tasks: [
          {
            id: 'review',
            agent_id: 'code-reviewer-v1',
            position: { x: 100, y: 100 },
            dependencies: [],
            conditions: [{ type: 'always' }],
            retry_policy: { max_attempts: 3, delay: 5, backoff_strategy: 'exponential' },
            timeout: 300
          },
          {
            id: 'security',
            agent_id: 'security-scanner-v1',
            position: { x: 300, y: 100 },
            dependencies: ['review'],
            conditions: [{ type: 'success' }],
            retry_policy: { max_attempts: 2, delay: 10, backoff_strategy: 'linear' },
            timeout: 600
          },
          {
            id: 'optimize',
            agent_id: 'optimizer-v1',
            position: { x: 500, y: 100 },
            dependencies: ['security'],
            conditions: [{ type: 'success' }],
            retry_policy: { max_attempts: 2, delay: 5, backoff_strategy: 'fixed' },
            timeout: 400
          }
        ],
        settings: {
          parallel_execution: false,
          failure_strategy: 'stop',
          notification_settings: [],
          resource_limits: {
            max_concurrent_tasks: 1,
            max_execution_time: 1800,
            max_memory_usage: 512,
            max_token_usage: 15000
          }
        }
      },
      {
        name: 'Full Stack Development',
        description: 'Complete development workflow from design to deployment',
        tasks: [
          {
            id: 'architect',
            agent_id: 'architect-v1',
            position: { x: 100, y: 50 },
            dependencies: [],
            conditions: [{ type: 'always' }],
            retry_policy: { max_attempts: 2, delay: 10, backoff_strategy: 'fixed' },
            timeout: 900
          },
          {
            id: 'implement',
            agent_id: 'code-reviewer-v1',
            position: { x: 300, y: 50 },
            dependencies: ['architect'],
            conditions: [{ type: 'success' }],
            retry_policy: { max_attempts: 3, delay: 5, backoff_strategy: 'linear' },
            timeout: 600
          },
          {
            id: 'test',
            agent_id: 'unit-tester-v1',
            position: { x: 500, y: 50 },
            dependencies: ['implement'],
            conditions: [{ type: 'success' }],
            retry_policy: { max_attempts: 3, delay: 5, backoff_strategy: 'exponential' },
            timeout: 400
          },
          {
            id: 'document',
            agent_id: 'documenter-v1',
            position: { x: 300, y: 200 },
            dependencies: ['implement'],
            conditions: [{ type: 'success' }],
            retry_policy: { max_attempts: 2, delay: 5, backoff_strategy: 'fixed' },
            timeout: 300
          },
          {
            id: 'deploy',
            agent_id: 'deployment-v1',
            position: { x: 700, y: 50 },
            dependencies: ['test', 'document'],
            conditions: [{ type: 'success' }],
            retry_policy: { max_attempts: 2, delay: 15, backoff_strategy: 'exponential' },
            timeout: 1200
          }
        ],
        settings: {
          parallel_execution: true,
          failure_strategy: 'continue',
          notification_settings: [],
          resource_limits: {
            max_concurrent_tasks: 3,
            max_execution_time: 3600,
            max_memory_usage: 1024,
            max_token_usage: 25000
          }
        }
      }
    ];
  }

  // Bulk Operation Templates
  public getBulkOperationTemplates(): Partial<BulkOperation>[] {
    return [
      {
        name: 'Repository Code Review',
        type: 'code_review_batch',
        agent_template: {
          type: 'code-reviewer',
          config: {
            model: 'sonnet',
            temperature: 0.1,
            max_tokens: 3000,
            timeout: 300,
            retry_count: 2,
            parallel_execution: true,
            dependencies: [],
            triggers: [],
            outputs: [],
            custom_prompts: {},
            tools: ['static_analysis'],
            mcp_servers: ['filesystem', 'git']
          }
        },
        batch_size: 5,
        parallel_limit: 3
      },
      {
        name: 'Documentation Generation',
        type: 'documentation_batch',
        agent_template: {
          type: 'documenter',
          config: {
            model: 'sonnet',
            temperature: 0.2,
            max_tokens: 4000,
            timeout: 400,
            retry_count: 2,
            parallel_execution: true,
            dependencies: [],
            triggers: [],
            outputs: [],
            custom_prompts: {},
            tools: ['doc_generator'],
            mcp_servers: ['filesystem']
          }
        },
        batch_size: 10,
        parallel_limit: 5
      },
      {
        name: 'Security Audit',
        type: 'security_scan_batch',
        agent_template: {
          type: 'security-scanner',
          config: {
            model: 'opus',
            temperature: 0.1,
            max_tokens: 5000,
            timeout: 600,
            retry_count: 3,
            parallel_execution: false,
            dependencies: [],
            triggers: [],
            outputs: [],
            custom_prompts: {},
            tools: ['security_scanner', 'vulnerability_detector'],
            mcp_servers: ['filesystem', 'github']
          }
        },
        batch_size: 3,
        parallel_limit: 1
      }
    ];
  }

  // Private helper methods
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private queueTask(taskId: string): void {
    this.executionQueue.push(taskId);
    if (!this.isProcessing) {
      this.processQueue();
    }
  }

  private async processQueue(): Promise<void> {
    this.isProcessing = true;
    
    while (this.executionQueue.length > 0) {
      const runningTasks = Array.from(this.tasks.values())
        .filter(task => task.status === 'running').length;
      
      if (runningTasks >= this.maxConcurrentTasks) {
        await this.wait(1000); // Wait 1 second before checking again
        continue;
      }
      
      const taskId = this.executionQueue.shift();
      if (taskId) {
        // Execute task in background
        this.executeTask(taskId, this.getDefaultContext()).catch(console.error);
      }
    }
    
    this.isProcessing = false;
  }

  private async checkDependencies(task: Task): Promise<void> {
    for (const depId of task.dependencies) {
      const depTask = this.tasks.get(depId);
      if (!depTask || depTask.status !== 'completed') {
        throw new Error(`Dependency ${depId} not completed`);
      }
    }
  }

  private async executeAgent(agentId: string, task: Task, context: ExecutionContext): Promise<void> {
    const library = AIAgentLibrary.getInstance();
    const agent = library.getAgent(agentId);
    
    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    // Simulate agent execution
    const startTime = Date.now();
    
    try {
      // Update agent metrics
      const duration = Date.now() - startTime;
      library.updateAgentMetrics(agentId, {
        average_execution_time: duration,
        success_rate: 0.95,
        error_rate: 0.05,
        resource_usage: {
          cpu_usage: 0.3,
          memory_usage: 0.2,
          token_usage: 2000,
          api_calls: 5
        },
        quality_score: 0.9
      });
    } catch (error) {
      library.updateAgentMetrics(agentId, {
        error_rate: 0.1,
        success_rate: 0.9
      });
      throw error;
    }
  }

  private async validateQualityGates(task: Task): Promise<void> {
    for (const gate of task.metadata.quality_gates) {
      // Implement quality gate validation logic
      if (gate.required && !this.evaluateCondition(gate.condition, gate.threshold)) {
        throw new Error(`Quality gate ${gate.name} failed`);
      }
    }
  }

  private evaluateCondition(condition: string, threshold: number): boolean {
    // Implement condition evaluation logic
    return Math.random() > 0.1; // Simplified for example
  }

  private async executeWorkflowParallel(workflow: Workflow, context: ExecutionContext): Promise<void> {
    // Implement parallel workflow execution
    const promises = workflow.tasks.map(task => this.executeWorkflowTask(task, workflow, context));
    await Promise.all(promises);
  }

  private async executeWorkflowSequential(workflow: Workflow, context: ExecutionContext): Promise<void> {
    // Implement sequential workflow execution
    for (const task of workflow.tasks) {
      await this.executeWorkflowTask(task, workflow, context);
    }
  }

  private async executeWorkflowTask(workflowTask: any, workflow: Workflow, context: ExecutionContext): Promise<void> {
    // Implement workflow task execution
    await this.executeAgent(workflowTask.agent_id, {} as Task, context);
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async processBatch(batch: any[], operation: BulkOperation, context: ExecutionContext): Promise<void> {
    const promises = batch.map(target => this.processBulkTarget(target, operation, context));
    await Promise.allSettled(promises);
  }

  private async processBulkTarget(target: any, operation: BulkOperation, context: ExecutionContext): Promise<void> {
    // Implement bulk target processing
    operation.progress.in_progress++;
    
    try {
      // Simulate processing
      await this.wait(Math.random() * 1000);
      
      operation.progress.completed++;
      operation.results.push({
        target_id: target.id,
        status: 'success',
        output: 'Processed successfully',
        duration: Math.random() * 1000,
        metrics: { quality_score: Math.random() }
      });
    } catch (error) {
      operation.progress.failed++;
      operation.results.push({
        target_id: target.id,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Math.random() * 1000
      });
    } finally {
      operation.progress.in_progress--;
    }
  }

  private updateBulkProgress(operation: BulkOperation): void {
    const { completed, failed, total } = operation.progress;
    operation.progress.percentage = ((completed + failed) / total) * 100;
    operation.progress.estimated_remaining = total - completed - failed;
  }

  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getDefaultContext(): ExecutionContext {
    return {
      session_id: 'default',
      project_path: '',
      environment: 'development',
      variables: {},
      secrets: {},
      permissions: [],
      resource_limits: {
        max_concurrent_tasks: 5,
        max_execution_time: 3600,
        max_memory_usage: 1024,
        max_token_usage: 10000
      },
      logging_level: 'info'
    };
  }

  // Public API methods
  public getTask(id: string): Task | undefined {
    return this.tasks.get(id);
  }

  public getAllTasks(): Task[] {
    return Array.from(this.tasks.values());
  }

  public getTasksByStatus(status: TaskStatus): Task[] {
    return this.getAllTasks().filter(task => task.status === status);
  }

  public getWorkflow(id: string): Workflow | undefined {
    return this.workflows.get(id);
  }

  public getAllWorkflows(): Workflow[] {
    return Array.from(this.workflows.values());
  }

  public getBulkOperation(id: string): BulkOperation | undefined {
    return this.bulkOperations.get(id);
  }

  public getAllBulkOperations(): BulkOperation[] {
    return Array.from(this.bulkOperations.values());
  }

  public cancelTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task && task.status === 'running') {
      task.status = 'cancelled';
      task.completed_at = new Date().toISOString();
    }
  }

  public pauseTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task && task.status === 'running') {
      task.status = 'paused';
    }
  }

  public resumeTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (task && task.status === 'paused') {
      task.status = 'running';
    }
  }
}