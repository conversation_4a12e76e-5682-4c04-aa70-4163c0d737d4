import { BaseAgent, Task, ExecutionContext } from './types';
import { AIAgentLibrary } from './AgentLibrary';

export interface AgentConversation {
  id: string;
  participants: string[]; // agent IDs
  topic: string;
  context: Record<string, any>;
  messages: ConversationMessage[];
  status: 'active' | 'completed' | 'paused';
  created_at: string;
  updated_at: string;
}

export interface ConversationMessage {
  id: string;
  sender_id: string;
  recipient_id?: string; // for direct messages
  content: string;
  message_type: 'text' | 'code' | 'data' | 'task_delegation' | 'knowledge_share';
  metadata: {
    task_id?: string;
    code_snippet?: string;
    data_payload?: any;
    confidence_score?: number;
    requires_response?: boolean;
  };
  timestamp: string;
}

export interface KnowledgeShare {
  id: string;
  source_agent: string;
  target_agents: string[];
  knowledge_type: 'pattern' | 'solution' | 'best_practice' | 'warning' | 'optimization';
  content: string;
  context: Record<string, any>;
  confidence: number;
  usage_count: number;
  effectiveness_score: number;
  created_at: string;
}

export interface AgentDelegation {
  id: string;
  delegator_id: string;
  delegate_id: string;
  task_id: string;
  reason: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: string;
  status: 'pending' | 'accepted' | 'rejected' | 'completed';
  result?: string;
  created_at: string;
}

export class AgentCollaborationSystem {
  private static instance: AgentCollaborationSystem;
  private conversations: Map<string, AgentConversation> = new Map();
  private knowledgeBase: Map<string, KnowledgeShare> = new Map();
  private delegations: Map<string, AgentDelegation> = new Map();
  private agentConnections: Map<string, Set<string>> = new Map(); // agent network graph

  public static getInstance(): AgentCollaborationSystem {
    if (!AgentCollaborationSystem.instance) {
      AgentCollaborationSystem.instance = new AgentCollaborationSystem();
    }
    return AgentCollaborationSystem.instance;
  }

  // Agent Conversation Management
  public async startConversation(
    initiatorId: string,
    participantIds: string[],
    topic: string,
    context: Record<string, any> = {}
  ): Promise<AgentConversation> {
    const conversation: AgentConversation = {
      id: this.generateId(),
      participants: [initiatorId, ...participantIds],
      topic,
      context,
      messages: [],
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.conversations.set(conversation.id, conversation);
    
    // Update agent connections
    this.updateAgentConnections(initiatorId, participantIds);
    
    return conversation;
  }

  public async sendMessage(
    conversationId: string,
    senderId: string,
    content: string,
    messageType: ConversationMessage['message_type'] = 'text',
    metadata: ConversationMessage['metadata'] = {}
  ): Promise<ConversationMessage> {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      throw new Error(`Conversation ${conversationId} not found`);
    }

    const message: ConversationMessage = {
      id: this.generateId(),
      sender_id: senderId,
      content,
      message_type: messageType,
      metadata,
      timestamp: new Date().toISOString()
    };

    conversation.messages.push(message);
    conversation.updated_at = new Date().toISOString();

    // Process message for knowledge extraction
    await this.processMessageForKnowledge(message, conversation);

    return message;
  }

  // Knowledge Sharing System
  public async shareKnowledge(
    sourceAgentId: string,
    targetAgentIds: string[],
    knowledgeType: KnowledgeShare['knowledge_type'],
    content: string,
    context: Record<string, any> = {},
    confidence: number = 0.8
  ): Promise<KnowledgeShare> {
    const knowledge: KnowledgeShare = {
      id: this.generateId(),
      source_agent: sourceAgentId,
      target_agents: targetAgentIds,
      knowledge_type: knowledgeType,
      content,
      context,
      confidence,
      usage_count: 0,
      effectiveness_score: 0,
      created_at: new Date().toISOString()
    };

    this.knowledgeBase.set(knowledge.id, knowledge);

    // Notify target agents
    for (const targetId of targetAgentIds) {
      await this.notifyAgentOfKnowledge(targetId, knowledge);
    }

    return knowledge;
  }

  public getRelevantKnowledge(
    agentId: string,
    context: Record<string, any>,
    limit: number = 10
  ): KnowledgeShare[] {
    const allKnowledge = Array.from(this.knowledgeBase.values());
    
    return allKnowledge
      .filter(k => k.target_agents.includes(agentId) || k.source_agent === agentId)
      .filter(k => this.isKnowledgeRelevant(k, context))
      .sort((a, b) => (b.confidence * b.effectiveness_score) - (a.confidence * a.effectiveness_score))
      .slice(0, limit);
  }

  // Task Delegation System
  public async delegateTask(
    delegatorId: string,
    delegateId: string,
    taskId: string,
    reason: string,
    priority: AgentDelegation['priority'] = 'medium',
    deadline?: string
  ): Promise<AgentDelegation> {
    const delegation: AgentDelegation = {
      id: this.generateId(),
      delegator_id: delegatorId,
      delegate_id: delegateId,
      task_id: taskId,
      reason,
      priority,
      deadline,
      status: 'pending',
      created_at: new Date().toISOString()
    };

    this.delegations.set(delegation.id, delegation);

    // Notify delegate agent
    await this.notifyAgentOfDelegation(delegateId, delegation);

    return delegation;
  }

  public async respondToDelegation(
    delegationId: string,
    response: 'accepted' | 'rejected',
    reason?: string
  ): Promise<void> {
    const delegation = this.delegations.get(delegationId);
    if (!delegation) {
      throw new Error(`Delegation ${delegationId} not found`);
    }

    delegation.status = response;
    if (reason) {
      delegation.result = reason;
    }

    // Notify delegator
    await this.notifyAgentOfDelegationResponse(delegation.delegator_id, delegation);
  }

  // Collaborative Problem Solving
  public async initiateCollaborativeSolving(
    problemDescription: string,
    requiredCapabilities: string[],
    context: ExecutionContext
  ): Promise<string> {
    const library = AIAgentLibrary.getInstance();
    
    // Find agents with required capabilities
    const suitableAgents = library.getAllAgents()
      .filter(agent => 
        requiredCapabilities.some(cap => 
          agent.capabilities.includes(cap)
        )
      )
      .slice(0, 5); // Limit to 5 agents for manageable collaboration

    if (suitableAgents.length === 0) {
      throw new Error('No suitable agents found for collaborative solving');
    }

    // Start conversation
    const conversation = await this.startConversation(
      suitableAgents[0].id,
      suitableAgents.slice(1).map(a => a.id),
      `Collaborative Problem Solving: ${problemDescription}`,
      { problem: problemDescription, capabilities: requiredCapabilities, context }
    );

    // Initiate problem-solving workflow
    await this.runCollaborativeProblemSolving(conversation.id, problemDescription, suitableAgents);

    return conversation.id;
  }

  private async runCollaborativeProblemSolving(
    conversationId: string,
    problem: string,
    agents: BaseAgent[]
  ): Promise<void> {
    // Phase 1: Problem Analysis
    for (const agent of agents) {
      await this.sendMessage(
        conversationId,
        agent.id,
        `Analyzing problem: ${problem}. My perspective based on ${agent.capabilities.join(', ')} capabilities.`,
        'text',
        { phase: 'analysis', agent_capabilities: agent.capabilities }
      );
    }

    // Phase 2: Solution Brainstorming
    for (const agent of agents) {
      await this.sendMessage(
        conversationId,
        agent.id,
        `Proposing solution approach based on my ${agent.type} expertise.`,
        'text',
        { phase: 'brainstorming', solution_approach: `${agent.type}_approach` }
      );
    }

    // Phase 3: Solution Synthesis
    const leadAgent = agents.find(a => a.type === 'architect') || agents[0];
    await this.sendMessage(
      conversationId,
      leadAgent.id,
      'Synthesizing all proposed solutions into a comprehensive approach.',
      'text',
      { phase: 'synthesis', role: 'synthesizer' }
    );
  }

  // Agent Network Analysis
  public getAgentNetwork(): Map<string, Set<string>> {
    return this.agentConnections;
  }

  public getAgentCollaborationScore(agentId: string): number {
    const connections = this.agentConnections.get(agentId)?.size || 0;
    const conversations = Array.from(this.conversations.values())
      .filter(c => c.participants.includes(agentId)).length;
    const knowledgeShared = Array.from(this.knowledgeBase.values())
      .filter(k => k.source_agent === agentId).length;
    
    return (connections * 0.3) + (conversations * 0.4) + (knowledgeShared * 0.3);
  }

  public suggestCollaborators(agentId: string, taskContext: Record<string, any>): string[] {
    const library = AIAgentLibrary.getInstance();
    const allAgents = library.getAllAgents();
    
    // Score agents based on collaboration history and relevance
    const scores = allAgents
      .filter(a => a.id !== agentId)
      .map(agent => ({
        id: agent.id,
        score: this.calculateCollaborationRelevance(agentId, agent.id, taskContext)
      }))
      .sort((a, b) => b.score - a.score);

    return scores.slice(0, 3).map(s => s.id);
  }

  // Private Helper Methods
  private updateAgentConnections(initiatorId: string, participantIds: string[]): void {
    if (!this.agentConnections.has(initiatorId)) {
      this.agentConnections.set(initiatorId, new Set());
    }

    const initiatorConnections = this.agentConnections.get(initiatorId)!;
    participantIds.forEach(id => initiatorConnections.add(id));

    // Update bidirectional connections
    participantIds.forEach(participantId => {
      if (!this.agentConnections.has(participantId)) {
        this.agentConnections.set(participantId, new Set());
      }
      this.agentConnections.get(participantId)!.add(initiatorId);
    });
  }

  private async processMessageForKnowledge(
    message: ConversationMessage,
    conversation: AgentConversation
  ): Promise<void> {
    // Extract potential knowledge from messages
    if (message.message_type === 'code' || message.content.includes('solution:')) {
      // This would use AI to extract knowledge patterns
      // For now, we'll create a simple knowledge entry
      await this.shareKnowledge(
        message.sender_id,
        conversation.participants.filter(p => p !== message.sender_id),
        'solution',
        message.content,
        conversation.context,
        0.7
      );
    }
  }

  private async notifyAgentOfKnowledge(agentId: string, knowledge: KnowledgeShare): Promise<void> {
    // This would integrate with the agent's notification system
    console.log(`Notifying agent ${agentId} of new knowledge: ${knowledge.knowledge_type}`);
  }

  private async notifyAgentOfDelegation(agentId: string, delegation: AgentDelegation): Promise<void> {
    // This would integrate with the agent's task queue
    console.log(`Notifying agent ${agentId} of task delegation: ${delegation.task_id}`);
  }

  private async notifyAgentOfDelegationResponse(
    agentId: string,
    delegation: AgentDelegation
  ): Promise<void> {
    console.log(`Notifying agent ${agentId} of delegation response: ${delegation.status}`);
  }

  private isKnowledgeRelevant(knowledge: KnowledgeShare, context: Record<string, any>): boolean {
    // Simple relevance check - in practice, this would use more sophisticated matching
    const contextKeys = Object.keys(context);
    const knowledgeKeys = Object.keys(knowledge.context);
    
    return contextKeys.some(key => knowledgeKeys.includes(key)) ||
           knowledge.knowledge_type === 'best_practice'; // Best practices are always relevant
  }

  private calculateCollaborationRelevance(
    agentId: string,
    candidateId: string,
    taskContext: Record<string, any>
  ): number {
    let score = 0;

    // Historical collaboration
    const hasCollaborated = this.agentConnections.get(agentId)?.has(candidateId);
    if (hasCollaborated) score += 0.3;

    // Shared knowledge
    const sharedKnowledge = Array.from(this.knowledgeBase.values())
      .filter(k => 
        (k.source_agent === agentId && k.target_agents.includes(candidateId)) ||
        (k.source_agent === candidateId && k.target_agents.includes(agentId))
      ).length;
    score += Math.min(sharedKnowledge * 0.1, 0.4);

    // Task context relevance (simplified)
    const library = AIAgentLibrary.getInstance();
    const candidateAgent = library.getAgent(candidateId);
    if (candidateAgent && taskContext.required_capabilities) {
      const relevantCapabilities = candidateAgent.capabilities.filter(cap =>
        taskContext.required_capabilities.includes(cap)
      ).length;
      score += relevantCapabilities * 0.1;
    }

    return score;
  }

  private generateId(): string {
    return `collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public API for getting collaboration data
  public getActiveConversations(): AgentConversation[] {
    return Array.from(this.conversations.values()).filter(c => c.status === 'active');
  }

  public getKnowledgeBase(): KnowledgeShare[] {
    return Array.from(this.knowledgeBase.values());
  }

  public getPendingDelegations(agentId: string): AgentDelegation[] {
    return Array.from(this.delegations.values())
      .filter(d => d.delegate_id === agentId && d.status === 'pending');
  }

  public getCollaborationMetrics(agentId: string) {
    const conversations = Array.from(this.conversations.values())
      .filter(c => c.participants.includes(agentId));
    
    const knowledgeShared = Array.from(this.knowledgeBase.values())
      .filter(k => k.source_agent === agentId);
    
    const knowledgeReceived = Array.from(this.knowledgeBase.values())
      .filter(k => k.target_agents.includes(agentId));
    
    const delegationsGiven = Array.from(this.delegations.values())
      .filter(d => d.delegator_id === agentId);
    
    const delegationsReceived = Array.from(this.delegations.values())
      .filter(d => d.delegate_id === agentId);

    return {
      conversations_participated: conversations.length,
      knowledge_shared: knowledgeShared.length,
      knowledge_received: knowledgeReceived.length,
      tasks_delegated: delegationsGiven.length,
      tasks_received: delegationsReceived.length,
      collaboration_score: this.getAgentCollaborationScore(agentId),
      network_connections: this.agentConnections.get(agentId)?.size || 0
    };
  }
}