import { EnhancedParallelSystem } from './EnhancedParallelSystem';
import { TaskMaster } from './TaskMaster';
import { BulkOperationsManager } from './BulkOperations';
import { Task, ExecutionContext } from './types';

export interface ParallelExecutionResult {
  taskId: string;
  status: 'success' | 'failure';
  result?: any;
  error?: string;
  executionTime: number;
  agentId: string;
  clusterId: string;
}

export interface ParallelBatchConfig {
  maxConcurrentTasks: number;
  maxConcurrentAgents: number;
  batchSize: number;
  retryAttempts: number;
  timeoutMs: number;
  loadBalancing: boolean;
  autoScaling: boolean;
}

export class ParallelAgentsManager {
  private static instance: ParallelAgentsManager;
  private parallelSystem: EnhancedParallelSystem;
  private taskMaster: TaskMaster;
  private bulkManager: BulkOperationsManager;
  private activeExecutions: Map<string, Promise<ParallelExecutionResult>> = new Map();

  public static getInstance(): ParallelAgentsManager {
    if (!ParallelAgentsManager.instance) {
      ParallelAgentsManager.instance = new ParallelAgentsManager();
    }
    return ParallelAgentsManager.instance;
  }

  constructor() {
    this.parallelSystem = EnhancedParallelSystem.getInstance();
    this.taskMaster = TaskMaster.getInstance();
    this.bulkManager = BulkOperationsManager.getInstance();
  }

  // Enhanced parallel execution with intelligent load balancing
  public async executeTasksInParallel(
    tasks: Task[],
    config: Partial<ParallelBatchConfig> = {},
    context: ExecutionContext
  ): Promise<ParallelExecutionResult[]> {
    const finalConfig: ParallelBatchConfig = {
      maxConcurrentTasks: 10,
      maxConcurrentAgents: 5,
      batchSize: 20,
      retryAttempts: 3,
      timeoutMs: 30000,
      loadBalancing: true,
      autoScaling: true,
      ...config
    };

    // Create execution plan
    const plan = await this.parallelSystem.createExecutionPlan(tasks, 'hybrid');
    
    // Execute with parallel system
    await this.parallelSystem.executeParallelPlan(plan.id, context);

    // Return results
    return this.collectExecutionResults(tasks);
  }

  // Smart agent allocation based on task types and current load
  public async allocateOptimalAgents(tasks: Task[]): Promise<Map<string, string[]>> {
    const allocation = new Map<string, string[]>();
    const clusters = this.parallelSystem.getClusters();

    // Group tasks by type for optimal cluster assignment
    const taskGroups = this.groupTasksByType(tasks);

    for (const [taskType, taskList] of taskGroups) {
      const optimalCluster = this.findOptimalCluster(taskType, clusters);
      if (optimalCluster) {
        allocation.set(optimalCluster.id, taskList.map(t => t.id));
      }
    }

    return allocation;
  }

  // Dynamic scaling based on workload
  public async autoScaleAgents(currentLoad: number, targetUtilization: number = 0.75): Promise<void> {
    const clusters = this.parallelSystem.getClusters();
    
    for (const cluster of clusters) {
      const utilization = cluster.currentLoad / cluster.maxConcurrency;
      
      if (utilization > targetUtilization * 1.2) {
        // Scale up
        await this.parallelSystem.scaleCluster(cluster.id, cluster.agents.length + 2);
      } else if (utilization < targetUtilization * 0.5) {
        // Scale down
        await this.parallelSystem.scaleCluster(cluster.id, Math.max(2, cluster.agents.length - 1));
      }
    }
  }

  // Real-time monitoring and metrics
  public getParallelExecutionMetrics() {
    const systemMetrics = this.parallelSystem.getSystemMetrics();
    const clusters = this.parallelSystem.getClusters();
    
    return {
      system: systemMetrics,
      clusters: clusters.map(cluster => ({
        id: cluster.id,
        name: cluster.name,
        utilization: cluster.currentLoad / cluster.maxConcurrency,
        performance: cluster.performance,
        agents: cluster.agents.length,
        activeAgents: cluster.currentLoad
      })),
      activeExecutions: this.activeExecutions.size,
      totalThroughput: clusters.reduce((sum, c) => sum + c.performance.throughput, 0)
    };
  }

  // Bulk operations with parallel execution
  public async executeBulkOperationParallel(
    operationType: 'code_review' | 'refactor' | 'test_generation' | 'documentation' | 'security_scan',
    targets: string[],
    config: Partial<ParallelBatchConfig> = {}
  ): Promise<ParallelExecutionResult[]> {
    // Create bulk operation
    let bulkOperation;
    const bulkTargets = targets.map((target, index) => ({
      id: `target-${index}`,
      type: 'file' as const,
      path: target,
      priority: 1
    }));

    switch (operationType) {
      case 'code_review':
        bulkOperation = await this.bulkManager.createCodeReviewBatch(bulkTargets);
        break;
      case 'refactor':
        bulkOperation = await this.bulkManager.createRefactorBatch(bulkTargets);
        break;
      case 'test_generation':
        bulkOperation = await this.bulkManager.createTestGenerationBatch(bulkTargets);
        break;
      case 'documentation':
        bulkOperation = await this.bulkManager.createDocumentationBatch(bulkTargets);
        break;
      case 'security_scan':
        bulkOperation = await this.bulkManager.createSecurityScanBatch(bulkTargets);
        break;
    }

    // Execute with enhanced parallel system
    const context = this.createExecutionContext();
    await this.bulkManager.executeBulkOperation(bulkOperation, context);

    return this.convertBulkResultsToParallelResults(bulkOperation.results);
  }

  // Advanced load balancing strategies
  public async rebalanceWorkload(): Promise<void> {
    const clusters = this.parallelSystem.getClusters();
    const overloadedClusters = clusters.filter(c => c.currentLoad / c.maxConcurrency > 0.8);
    const underloadedClusters = clusters.filter(c => c.currentLoad / c.maxConcurrency < 0.3);

    // Implement workload redistribution logic
    for (const overloaded of overloadedClusters) {
      for (const underloaded of underloadedClusters) {
        if (this.canTransferWorkload(overloaded, underloaded)) {
          await this.transferWorkload(overloaded.id, underloaded.id, 2);
        }
      }
    }
  }

  // Performance optimization
  public async optimizePerformance(): Promise<void> {
    const metrics = this.getParallelExecutionMetrics();
    
    // Identify bottlenecks
    const bottlenecks = this.identifyBottlenecks(metrics);
    
    // Apply optimizations
    for (const bottleneck of bottlenecks) {
      await this.applyOptimization(bottleneck);
    }
  }

  // Private helper methods
  private groupTasksByType(tasks: Task[]): Map<string, Task[]> {
    const groups = new Map<string, Task[]>();
    
    tasks.forEach(task => {
      const type = task.type || 'general';
      if (!groups.has(type)) {
        groups.set(type, []);
      }
      groups.get(type)!.push(task);
    });

    return groups;
  }

  private findOptimalCluster(taskType: string, clusters: any[]): any {
    // Find cluster with matching specialization and lowest load
    const suitableClusters = clusters.filter(cluster => 
      this.isClusterSuitableForTaskType(cluster, taskType)
    );

    return suitableClusters.sort((a, b) => 
      (a.currentLoad / a.maxConcurrency) - (b.currentLoad / b.maxConcurrency)
    )[0];
  }

  private isClusterSuitableForTaskType(cluster: any, taskType: string): boolean {
    const typeMapping: Record<string, string[]> = {
      'code_review': ['code_analysis'],
      'refactoring': ['code_analysis'],
      'testing': ['testing'],
      'documentation': ['content_generation'],
      'security_scan': ['security']
    };

    const requiredSpecializations = typeMapping[taskType] || ['code_analysis'];
    return requiredSpecializations.includes(cluster.specialization);
  }

  private async collectExecutionResults(tasks: Task[]): Promise<ParallelExecutionResult[]> {
    return tasks.map(task => ({
      taskId: task.id,
      status: task.status === 'completed' ? 'success' : 'failure',
      result: task.outputs,
      error: task.status === 'failed' ? 'Task execution failed' : undefined,
      executionTime: this.calculateExecutionTime(task),
      agentId: task.assigned_agents[0] || 'unknown',
      clusterId: 'auto-assigned'
    }));
  }

  private calculateExecutionTime(task: Task): number {
    if (task.started_at && task.completed_at) {
      return new Date(task.completed_at).getTime() - new Date(task.started_at).getTime();
    }
    return 0;
  }

  private createExecutionContext(): ExecutionContext {
    return {
      session_id: `parallel-${Date.now()}`,
      project_path: '',
      environment: 'production',
      variables: {},
      secrets: {},
      permissions: [],
      resource_limits: {
        max_concurrent_tasks: 20,
        max_execution_time: 3600,
        max_memory_usage: 2048,
        max_token_usage: 50000
      },
      logging_level: 'info'
    };
  }

  private convertBulkResultsToParallelResults(bulkResults: any[]): ParallelExecutionResult[] {
    return bulkResults.map(result => ({
      taskId: result.target_id,
      status: result.status === 'success' ? 'success' : 'failure',
      result: result.output,
      error: result.error,
      executionTime: result.duration || 0,
      agentId: 'bulk-agent',
      clusterId: 'bulk-cluster'
    }));
  }

  private canTransferWorkload(from: any, to: any): boolean {
    return from.specialization === to.specialization && 
           to.currentLoad < to.maxConcurrency * 0.7;
  }

  private async transferWorkload(fromClusterId: string, toClusterId: string, taskCount: number): Promise<void> {
    // Implementation for transferring workload between clusters
    console.log(`Transferring ${taskCount} tasks from ${fromClusterId} to ${toClusterId}`);
  }

  private identifyBottlenecks(metrics: any): string[] {
    const bottlenecks: string[] = [];
    
    if (metrics.system.averageUtilization > 0.9) {
      bottlenecks.push('high_utilization');
    }
    
    if (metrics.system.averageResponseTime > 5000) {
      bottlenecks.push('slow_response');
    }
    
    if (metrics.system.overallSuccessRate < 0.9) {
      bottlenecks.push('low_success_rate');
    }

    return bottlenecks;
  }

  private async applyOptimization(bottleneck: string): Promise<void> {
    switch (bottleneck) {
      case 'high_utilization':
        await this.autoScaleAgents(0.9, 0.7);
        break;
      case 'slow_response':
        await this.optimizeResponseTime();
        break;
      case 'low_success_rate':
        await this.improveSuccessRate();
        break;
    }
  }

  private async optimizeResponseTime(): Promise<void> {
    // Implement response time optimization
    console.log('Optimizing response time...');
  }

  private async improveSuccessRate(): Promise<void> {
    // Implement success rate improvement
    console.log('Improving success rate...');
  }

  // Public API methods for external integration
  public async startParallelExecution(tasks: Task[]): Promise<string> {
    const plan = await this.parallelSystem.createExecutionPlan(tasks);
    const context = this.createExecutionContext();
    
    // Start execution in background
    this.parallelSystem.executeParallelPlan(plan.id, context);
    
    return plan.id;
  }

  public getExecutionStatus(planId: string): any {
    const plans = this.parallelSystem.getExecutionPlans();
    return plans.find(p => p.id === planId);
  }

  public async pauseExecution(planId: string): Promise<void> {
    // Implementation for pausing execution
    console.log(`Pausing execution plan: ${planId}`);
  }

  public async resumeExecution(planId: string): Promise<void> {
    // Implementation for resuming execution
    console.log(`Resuming execution plan: ${planId}`);
  }

  public async cancelExecution(planId: string): Promise<void> {
    // Implementation for canceling execution
    console.log(`Canceling execution plan: ${planId}`);
  }
}