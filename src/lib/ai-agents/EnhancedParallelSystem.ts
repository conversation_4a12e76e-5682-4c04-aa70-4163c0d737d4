import { Task, Agent, ExecutionContext, BulkOperation } from './types';
import { TaskMaster } from './TaskMaster';
import { AIAgentLibrary } from './AgentLibrary';
import { AgentCollaborationSystem } from './AgentCollaboration';

export interface ParallelAgentCluster {
  id: string;
  name: string;
  agents: string[];
  specialization: string;
  maxConcurrency: number;
  currentLoad: number;
  performance: {
    throughput: number;
    successRate: number;
    averageResponseTime: number;
  };
}

export interface DynamicScalingConfig {
  enabled: boolean;
  minAgents: number;
  maxAgents: number;
  scaleUpThreshold: number;
  scaleDownThreshold: number;
  cooldownPeriod: number;
}

export interface LoadBalancerConfig {
  strategy: 'round_robin' | 'weighted' | 'least_connections' | 'capability_aware';
  healthCheckInterval: number;
  failoverEnabled: boolean;
}

export interface ParallelExecutionPlan {
  id: string;
  tasks: Task[];
  clusters: ParallelAgentCluster[];
  executionStrategy: 'parallel' | 'pipeline' | 'hybrid';
  dependencies: Map<string, string[]>;
  estimatedDuration: number;
  resourceRequirements: {
    memory: number;
    cpu: number;
    tokens: number;
  };
}

export class EnhancedParallelSystem {
  private static instance: EnhancedParallelSystem;
  private clusters: Map<string, ParallelAgentCluster> = new Map();
  private executionPlans: Map<string, ParallelExecutionPlan> = new Map();
  private scalingConfig: DynamicScalingConfig;
  private loadBalancerConfig: LoadBalancerConfig;
  private isRunning = false;
  private monitoringInterval?: NodeJS.Timeout;

  public static getInstance(): EnhancedParallelSystem {
    if (!EnhancedParallelSystem.instance) {
      EnhancedParallelSystem.instance = new EnhancedParallelSystem();
    }
    return EnhancedParallelSystem.instance;
  }

  constructor() {
    this.scalingConfig = {
      enabled: true,
      minAgents: 3,
      maxAgents: 50,
      scaleUpThreshold: 0.8,
      scaleDownThreshold: 0.3,
      cooldownPeriod: 60000 // 1 minute
    };

    this.loadBalancerConfig = {
      strategy: 'capability_aware',
      healthCheckInterval: 10000,
      failoverEnabled: true
    };

    this.initializeDefaultClusters();
  }

  private initializeDefaultClusters(): void {
    const defaultClusters = [
      {
        id: 'code-analysis-cluster',
        name: 'Code Analysis Cluster',
        agents: [],
        specialization: 'code_analysis',
        maxConcurrency: 10,
        currentLoad: 0,
        performance: { throughput: 0, successRate: 1.0, averageResponseTime: 0 }
      },
      {
        id: 'content-generation-cluster',
        name: 'Content Generation Cluster',
        agents: [],
        specialization: 'content_generation',
        maxConcurrency: 8,
        currentLoad: 0,
        performance: { throughput: 0, successRate: 1.0, averageResponseTime: 0 }
      },
      {
        id: 'testing-cluster',
        name: 'Testing & QA Cluster',
        agents: [],
        specialization: 'testing',
        maxConcurrency: 6,
        currentLoad: 0,
        performance: { throughput: 0, successRate: 1.0, averageResponseTime: 0 }
      },
      {
        id: 'security-cluster',
        name: 'Security Analysis Cluster',
        agents: [],
        specialization: 'security',
        maxConcurrency: 4,
        currentLoad: 0,
        performance: { throughput: 0, successRate: 1.0, averageResponseTime: 0 }
      }
    ];

    defaultClusters.forEach(cluster => {
      this.clusters.set(cluster.id, cluster);
    });

    this.populateClusters();
  }

  private populateClusters(): void {
    const library = AIAgentLibrary.getInstance();
    const agents = library.getAllAgents();

    agents.forEach(agent => {
      const clusterId = this.determineClusterForAgent(agent);
      const cluster = this.clusters.get(clusterId);
      if (cluster && cluster.agents.length < cluster.maxConcurrency) {
        cluster.agents.push(agent.id);
      }
    });
  }

  private determineClusterForAgent(agent: any): string {
    // Ensure agent has capabilities array
    if (!agent || !Array.isArray(agent.capabilities)) {
      console.warn('Agent missing capabilities:', agent);
      return 'code-analysis-cluster'; // Default
    }

    // Check capabilities as strings (they are string descriptions, not keys)
    const capabilities = agent.capabilities.map(cap => cap.toLowerCase());
    
    if (capabilities.some(cap => cap.includes('code') && (cap.includes('analysis') || cap.includes('review')))) {
      return 'code-analysis-cluster';
    }
    if (capabilities.some(cap => cap.includes('content') || cap.includes('documentation'))) {
      return 'content-generation-cluster';
    }
    if (capabilities.some(cap => cap.includes('test'))) {
      return 'testing-cluster';
    }
    if (capabilities.some(cap => cap.includes('security') || cap.includes('vulnerability'))) {
      return 'security-cluster';
    }
    return 'code-analysis-cluster'; // Default
  }

  // Enhanced Parallel Execution
  public async createExecutionPlan(
    tasks: Task[],
    strategy: 'parallel' | 'pipeline' | 'hybrid' = 'hybrid'
  ): Promise<ParallelExecutionPlan> {
    const plan: ParallelExecutionPlan = {
      id: `plan-${Date.now()}`,
      tasks,
      clusters: Array.from(this.clusters.values()),
      executionStrategy: strategy,
      dependencies: this.analyzeDependencies(tasks),
      estimatedDuration: this.estimateExecutionDuration(tasks, strategy),
      resourceRequirements: this.calculateResourceRequirements(tasks)
    };

    this.executionPlans.set(plan.id, plan);
    return plan;
  }

  private analyzeDependencies(tasks: Task[]): Map<string, string[]> {
    const dependencies = new Map<string, string[]>();
    
    tasks.forEach(task => {
      if (task.dependencies && task.dependencies.length > 0) {
        dependencies.set(task.id, task.dependencies);
      }
    });

    return dependencies;
  }

  private estimateExecutionDuration(tasks: Task[], strategy: string): number {
    const totalEstimatedTime = tasks.reduce((sum, task) => 
      sum + (task.estimated_duration || 300), 0
    );

    switch (strategy) {
      case 'parallel':
        return Math.max(...tasks.map(t => t.estimated_duration || 300));
      case 'pipeline':
        return totalEstimatedTime * 0.7; // Pipeline efficiency
      case 'hybrid':
        return totalEstimatedTime * 0.4; // Best of both worlds
      default:
        return totalEstimatedTime;
    }
  }

  private calculateResourceRequirements(tasks: Task[]): { memory: number; cpu: number; tokens: number } {
    return tasks.reduce((total, task) => ({
      memory: total.memory + (task.metadata?.resourceRequirements?.memory || 100),
      cpu: total.cpu + (task.metadata?.resourceRequirements?.cpu || 10),
      tokens: total.tokens + (task.metadata?.resourceRequirements?.tokens || 1000)
    }), { memory: 0, cpu: 0, tokens: 0 });
  }

  public async executeParallelPlan(
    planId: string,
    context: ExecutionContext
  ): Promise<void> {
    const plan = this.executionPlans.get(planId);
    if (!plan) throw new Error(`Execution plan ${planId} not found`);

    this.isRunning = true;
    this.startMonitoring();

    try {
      switch (plan.executionStrategy) {
        case 'parallel':
          await this.executeParallelStrategy(plan, context);
          break;
        case 'pipeline':
          await this.executePipelineStrategy(plan, context);
          break;
        case 'hybrid':
          await this.executeHybridStrategy(plan, context);
          break;
      }
    } finally {
      this.isRunning = false;
      this.stopMonitoring();
    }
  }

  private async executeParallelStrategy(
    plan: ParallelExecutionPlan,
    context: ExecutionContext
  ): Promise<void> {
    const taskGroups = this.groupTasksByCluster(plan.tasks);
    const executionPromises: Promise<void>[] = [];

    for (const [clusterId, tasks] of taskGroups) {
      const cluster = this.clusters.get(clusterId);
      if (cluster) {
        executionPromises.push(
          this.executeTasksInCluster(cluster, tasks, context)
        );
      }
    }

    await Promise.all(executionPromises);
  }

  private async executePipelineStrategy(
    plan: ParallelExecutionPlan,
    context: ExecutionContext
  ): Promise<void> {
    const stages = this.createPipelineStages(plan.tasks, plan.dependencies);
    
    for (const stage of stages) {
      const stagePromises = stage.map(task => 
        this.executeTaskInOptimalCluster(task, context)
      );
      await Promise.all(stagePromises);
    }
  }

  private async executeHybridStrategy(
    plan: ParallelExecutionPlan,
    context: ExecutionContext
  ): Promise<void> {
    // Combine parallel and pipeline approaches
    const independentTasks = plan.tasks.filter(task => 
      !plan.dependencies.has(task.id) && 
      !Array.from(plan.dependencies.values()).flat().includes(task.id)
    );

    const dependentTasks = plan.tasks.filter(task => 
      plan.dependencies.has(task.id) || 
      Array.from(plan.dependencies.values()).flat().includes(task.id)
    );

    // Execute independent tasks in parallel
    const independentPromise = this.executeParallelTasks(independentTasks, context);

    // Execute dependent tasks in pipeline
    const dependentPromise = this.executeDependentTasks(dependentTasks, plan.dependencies, context);

    await Promise.all([independentPromise, dependentPromise]);
  }

  private groupTasksByCluster(tasks: Task[]): Map<string, Task[]> {
    const groups = new Map<string, Task[]>();

    tasks.forEach(task => {
      const clusterId = this.selectOptimalCluster(task);
      if (!groups.has(clusterId)) {
        groups.set(clusterId, []);
      }
      groups.get(clusterId)!.push(task);
    });

    return groups;
  }

  private selectOptimalCluster(task: Task): string {
    const suitableClusters = Array.from(this.clusters.values())
      .filter(cluster => this.isClusterSuitableForTask(cluster, task))
      .sort((a, b) => this.calculateClusterScore(b, task) - this.calculateClusterScore(a, task));

    return suitableClusters[0]?.id || 'code-analysis-cluster';
  }

  private isClusterSuitableForTask(cluster: ParallelAgentCluster, task: Task): boolean {
    // Check if cluster specialization matches task requirements
    const taskRequirements = this.getTaskRequirements(task);
    return taskRequirements.some(req => cluster.specialization.includes(req));
  }

  private getTaskRequirements(task: Task): string[] {
    const requirementMap: Record<string, string[]> = {
      'code_review': ['code_analysis', 'quality_assessment'],
      'refactoring': ['code_analysis', 'code_transformation'],
      'testing': ['testing', 'quality_assurance'],
      'documentation': ['content_generation', 'technical_writing'],
      'security_scan': ['security', 'vulnerability_detection'],
      'optimization': ['code_analysis', 'performance']
    };

    return requirementMap[task.type] || ['code_analysis'];
  }

  private calculateClusterScore(cluster: ParallelAgentCluster, task: Task): number {
    const loadScore = (cluster.maxConcurrency - cluster.currentLoad) / cluster.maxConcurrency;
    const performanceScore = cluster.performance.successRate * cluster.performance.throughput;
    const specializationScore = this.isClusterSuitableForTask(cluster, task) ? 1 : 0.5;

    return loadScore * 0.4 + performanceScore * 0.3 + specializationScore * 0.3;
  }

  private async executeTasksInCluster(
    cluster: ParallelAgentCluster,
    tasks: Task[],
    context: ExecutionContext
  ): Promise<void> {
    const batchSize = Math.min(cluster.maxConcurrency, cluster.agents.length);
    const batches = this.createBatches(tasks, batchSize);

    for (const batch of batches) {
      const batchPromises = batch.map((task, index) => {
        const agentId = cluster.agents[index % cluster.agents.length];
        return this.executeTaskWithAgent(task, agentId, context);
      });

      await Promise.all(batchPromises);
    }
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private async executeTaskWithAgent(
    task: Task,
    agentId: string,
    context: ExecutionContext
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Update cluster load
      this.updateClusterLoad(agentId, 1);
      
      // Execute task
      const taskMaster = TaskMaster.getInstance();
      await taskMaster.executeTask(task.id, context);
      
      // Update performance metrics
      const executionTime = Date.now() - startTime;
      this.updateClusterPerformance(agentId, executionTime, true);
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateClusterPerformance(agentId, executionTime, false);
      throw error;
    } finally {
      this.updateClusterLoad(agentId, -1);
    }
  }

  private updateClusterLoad(agentId: string, delta: number): void {
    for (const cluster of this.clusters.values()) {
      if (cluster.agents.includes(agentId)) {
        cluster.currentLoad = Math.max(0, cluster.currentLoad + delta);
        break;
      }
    }
  }

  private updateClusterPerformance(agentId: string, executionTime: number, success: boolean): void {
    for (const cluster of this.clusters.values()) {
      if (cluster.agents.includes(agentId)) {
        // Update average response time
        cluster.performance.averageResponseTime = 
          (cluster.performance.averageResponseTime + executionTime) / 2;
        
        // Update success rate
        cluster.performance.successRate = 
          (cluster.performance.successRate * 0.9) + (success ? 0.1 : 0);
        
        // Update throughput
        cluster.performance.throughput = 1000 / cluster.performance.averageResponseTime;
        break;
      }
    }
  }

  private createPipelineStages(tasks: Task[], dependencies: Map<string, string[]>): Task[][] {
    const stages: Task[][] = [];
    const processed = new Set<string>();
    const remaining = new Set(tasks.map(t => t.id));

    while (remaining.size > 0) {
      const currentStage: Task[] = [];
      
      for (const taskId of remaining) {
        const task = tasks.find(t => t.id === taskId)!;
        const deps = dependencies.get(taskId) || [];
        
        // Check if all dependencies are processed
        if (deps.every(dep => processed.has(dep))) {
          currentStage.push(task);
        }
      }

      if (currentStage.length === 0) {
        // Circular dependency or other issue
        break;
      }

      stages.push(currentStage);
      currentStage.forEach(task => {
        processed.add(task.id);
        remaining.delete(task.id);
      });
    }

    return stages;
  }

  private async executeParallelTasks(tasks: Task[], context: ExecutionContext): Promise<void> {
    const promises = tasks.map(task => this.executeTaskInOptimalCluster(task, context));
    await Promise.all(promises);
  }

  private async executeDependentTasks(
    tasks: Task[],
    dependencies: Map<string, string[]>,
    context: ExecutionContext
  ): Promise<void> {
    const stages = this.createPipelineStages(tasks, dependencies);
    
    for (const stage of stages) {
      await this.executeParallelTasks(stage, context);
    }
  }

  private async executeTaskInOptimalCluster(task: Task, context: ExecutionContext): Promise<void> {
    const clusterId = this.selectOptimalCluster(task);
    const cluster = this.clusters.get(clusterId);
    
    if (!cluster || cluster.agents.length === 0) {
      throw new Error(`No available agents in cluster ${clusterId}`);
    }

    // Select least loaded agent in cluster
    const agentId = this.selectLeastLoadedAgent(cluster);
    await this.executeTaskWithAgent(task, agentId, context);
  }

  private selectLeastLoadedAgent(cluster: ParallelAgentCluster): string {
    // For simplicity, use round-robin. In practice, you'd track individual agent loads
    const agentIndex = cluster.currentLoad % cluster.agents.length;
    return cluster.agents[agentIndex];
  }

  // Dynamic Scaling
  public async scaleCluster(clusterId: string, targetSize: number): Promise<void> {
    const cluster = this.clusters.get(clusterId);
    if (!cluster) throw new Error(`Cluster ${clusterId} not found`);

    const currentSize = cluster.agents.length;
    
    if (targetSize > currentSize) {
      await this.scaleUpCluster(cluster, targetSize - currentSize);
    } else if (targetSize < currentSize) {
      await this.scaleDownCluster(cluster, currentSize - targetSize);
    }
  }

  private async scaleUpCluster(cluster: ParallelAgentCluster, count: number): Promise<void> {
    const library = AIAgentLibrary.getInstance();
    const availableAgents = library.getAllAgents()
      .filter(agent => !this.isAgentInAnyCluster(agent.id))
      .filter(agent => this.isAgentSuitableForCluster(agent, cluster))
      .slice(0, count);

    availableAgents.forEach(agent => {
      cluster.agents.push(agent.id);
    });

    cluster.maxConcurrency = Math.min(cluster.maxConcurrency + count, 20);
  }

  private async scaleDownCluster(cluster: ParallelAgentCluster, count: number): Promise<void> {
    // Remove agents that are not currently processing tasks
    const agentsToRemove = cluster.agents.slice(-count);
    cluster.agents = cluster.agents.slice(0, -count);
    cluster.maxConcurrency = Math.max(cluster.maxConcurrency - count, 1);
  }

  private isAgentInAnyCluster(agentId: string): boolean {
    return Array.from(this.clusters.values()).some(cluster => 
      cluster.agents.includes(agentId)
    );
  }

  private isAgentSuitableForCluster(agent: any, cluster: ParallelAgentCluster): boolean {
    const requiredCapabilities = this.getClusterRequiredCapabilities(cluster.specialization);
    return requiredCapabilities.some(cap => agent.capabilities.includes(cap));
  }

  private getClusterRequiredCapabilities(specialization: string): string[] {
    const capabilityMap: Record<string, string[]> = {
      'code_analysis': ['code_analysis', 'code_review', 'static_analysis'],
      'content_generation': ['content_generation', 'documentation', 'technical_writing'],
      'testing': ['testing', 'quality_assurance', 'test_generation'],
      'security': ['security_analysis', 'vulnerability_detection', 'security_audit']
    };

    return capabilityMap[specialization] || ['general_purpose'];
  }

  // Monitoring and Health Checks
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      this.performHealthChecks();
      this.autoScale();
      this.rebalanceLoad();
    }, this.loadBalancerConfig.healthCheckInterval);
  }

  private stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  private performHealthChecks(): void {
    for (const cluster of this.clusters.values()) {
      // Check cluster health based on performance metrics
      if (cluster.performance.successRate < 0.8) {
        console.warn(`Cluster ${cluster.id} has low success rate: ${cluster.performance.successRate}`);
      }
      
      if (cluster.performance.averageResponseTime > 30000) { // 30 seconds
        console.warn(`Cluster ${cluster.id} has high response time: ${cluster.performance.averageResponseTime}ms`);
      }
    }
  }

  private autoScale(): void {
    if (!this.scalingConfig.enabled) return;

    for (const cluster of this.clusters.values()) {
      const utilization = cluster.currentLoad / cluster.maxConcurrency;
      
      if (utilization > this.scalingConfig.scaleUpThreshold && 
          cluster.agents.length < this.scalingConfig.maxAgents) {
        this.scaleUpCluster(cluster, 1);
      } else if (utilization < this.scalingConfig.scaleDownThreshold && 
                 cluster.agents.length > this.scalingConfig.minAgents) {
        this.scaleDownCluster(cluster, 1);
      }
    }
  }

  private rebalanceLoad(): void {
    // Implement load rebalancing logic
    const overloadedClusters = Array.from(this.clusters.values())
      .filter(cluster => cluster.currentLoad / cluster.maxConcurrency > 0.8);
    
    const underloadedClusters = Array.from(this.clusters.values())
      .filter(cluster => cluster.currentLoad / cluster.maxConcurrency < 0.3);

    // In a real implementation, you would move agents or tasks between clusters
    // For now, we just log the rebalancing opportunity
    if (overloadedClusters.length > 0 && underloadedClusters.length > 0) {
      console.log('Load rebalancing opportunity detected');
    }
  }

  // Public API
  public getClusters(): ParallelAgentCluster[] {
    return Array.from(this.clusters.values());
  }

  public getExecutionPlans(): ParallelExecutionPlan[] {
    return Array.from(this.executionPlans.values());
  }

  public getSystemMetrics() {
    const clusters = Array.from(this.clusters.values());
    
    return {
      totalClusters: clusters.length,
      totalAgents: clusters.reduce((sum, c) => sum + c.agents.length, 0),
      activeAgents: clusters.reduce((sum, c) => sum + c.currentLoad, 0),
      averageUtilization: clusters.reduce((sum, c) => 
        sum + (c.currentLoad / c.maxConcurrency), 0) / clusters.length,
      overallSuccessRate: clusters.reduce((sum, c) => 
        sum + c.performance.successRate, 0) / clusters.length,
      averageResponseTime: clusters.reduce((sum, c) => 
        sum + c.performance.averageResponseTime, 0) / clusters.length
    };
  }

  public updateScalingConfig(config: Partial<DynamicScalingConfig>): void {
    this.scalingConfig = { ...this.scalingConfig, ...config };
  }

  public updateLoadBalancerConfig(config: Partial<LoadBalancerConfig>): void {
    this.loadBalancerConfig = { ...this.loadBalancerConfig, ...config };
  }
}