import { Task, ExecutionResult, Agent } from './types';

export interface ComplexityMetrics {
  computationalComplexity: number;
  memoryRequirements: number;
  timeComplexity: number;
  dependencyComplexity: number;
  riskLevel: number;
}

export interface ExecutionStrategy {
  type: 'parallel' | 'pipeline' | 'hybrid' | 'swarm' | 'quantum';
  confidence: number;
  estimatedPerformance: number;
  resourceOptimization: number;
  reasoning: string;
}

export interface LearningMetrics {
  accuracyImprovement: number;
  performanceGains: number;
  adaptationSpeed: number;
  predictionConfidence: number;
}

export interface UserBehaviorProfile {
  userId: string;
  preferredStrategies: string[];
  taskPatterns: Map<string, number>;
  performancePreferences: {
    speed: number;
    accuracy: number;
    resourceEfficiency: number;
  };
  learningHistory: LearningEvent[];
}

export interface LearningEvent {
  timestamp: number;
  taskType: string;
  strategy: string;
  performance: number;
  userSatisfaction: number;
}

export class AdaptiveLearningEngine {
  private static instance: AdaptiveLearningEngine;
  private executionHistory: ExecutionResult[] = [];
  private strategyPerformance: Map<string, number[]> = new Map();
  private userProfiles: Map<string, UserBehaviorProfile> = new Map();
  private learningModel: Map<string, any> = new Map();
  private adaptationThreshold = 0.1;

  public static getInstance(): AdaptiveLearningEngine {
    if (!AdaptiveLearningEngine.instance) {
      AdaptiveLearningEngine.instance = new AdaptiveLearningEngine();
    }
    return AdaptiveLearningEngine.instance;
  }

  constructor() {
    this.initializeLearningModel();
  }

  private initializeLearningModel(): void {
    // Initialize with baseline strategies
    const strategies = ['parallel', 'pipeline', 'hybrid', 'swarm', 'quantum'];
    strategies.forEach(strategy => {
      this.strategyPerformance.set(strategy, [0.5]); // Start with neutral performance
    });
  }

  public async learnFromExecution(results: ExecutionResult[]): Promise<void> {
    this.executionHistory.push(...results);
    
    // Update strategy performance metrics
    for (const result of results) {
      await this.updateStrategyPerformance(result);
      await this.updateComplexityModel(result);
      await this.adaptClusterConfiguration(result);
    }

    // Trigger model retraining if enough new data
    if (this.executionHistory.length % 100 === 0) {
      await this.retrainModel();
    }
  }

  private async updateStrategyPerformance(result: ExecutionResult): Promise<void> {
    const strategy = result.strategy || 'hybrid';
    const performance = this.calculatePerformanceScore(result);
    
    const currentPerformances = this.strategyPerformance.get(strategy) || [];
    currentPerformances.push(performance);
    
    // Keep only last 1000 results for each strategy
    if (currentPerformances.length > 1000) {
      currentPerformances.shift();
    }
    
    this.strategyPerformance.set(strategy, currentPerformances);
  }

  private calculatePerformanceScore(result: ExecutionResult): number {
    const weights = {
      executionTime: 0.3,
      resourceUsage: 0.2,
      accuracy: 0.3,
      userSatisfaction: 0.2
    };

    const normalizedTime = Math.max(0, 1 - (result.executionTime / result.estimatedTime));
    const normalizedResources = Math.max(0, 1 - (result.resourceUsage / result.estimatedResources));
    const accuracy = result.accuracy || 0.8;
    const satisfaction = result.userSatisfaction || 0.7;

    return (
      normalizedTime * weights.executionTime +
      normalizedResources * weights.resourceUsage +
      accuracy * weights.accuracy +
      satisfaction * weights.userSatisfaction
    );
  }

  public async predictOptimalStrategy(tasks: Task[], userId?: string): Promise<ExecutionStrategy> {
    const complexity = await this.analyzeTaskComplexity(tasks);
    const userProfile = userId ? this.getUserProfile(userId) : null;
    
    // Calculate strategy scores based on learned patterns
    const strategyScores = await this.calculateStrategyScores(tasks, complexity, userProfile);
    
    // Select best strategy
    const bestStrategy = this.selectBestStrategy(strategyScores);
    
    return {
      type: bestStrategy.type as any,
      confidence: bestStrategy.confidence,
      estimatedPerformance: bestStrategy.performance,
      resourceOptimization: bestStrategy.resourceOptimization,
      reasoning: bestStrategy.reasoning
    };
  }

  private async analyzeTaskComplexity(tasks: Task[]): Promise<ComplexityMetrics> {
    const totalLines = tasks.reduce((sum, task) => sum + (task.metadata?.linesOfCode || 100), 0);
    const dependencies = tasks.reduce((sum, task) => sum + (task.dependencies?.length || 0), 0);
    const estimatedTime = tasks.reduce((sum, task) => sum + (task.estimated_duration || 300), 0);

    return {
      computationalComplexity: Math.min(1, totalLines / 10000),
      memoryRequirements: Math.min(1, tasks.length / 100),
      timeComplexity: Math.min(1, estimatedTime / 3600),
      dependencyComplexity: Math.min(1, dependencies / 50),
      riskLevel: this.calculateRiskLevel(tasks)
    };
  }

  private calculateRiskLevel(tasks: Task[]): number {
    const riskFactors = {
      hasFileOperations: tasks.some(t => t.type.includes('file')),
      hasNetworkOperations: tasks.some(t => t.type.includes('network')),
      hasSystemOperations: tasks.some(t => t.type.includes('system')),
      hasComplexLogic: tasks.some(t => t.metadata?.complexity === 'high'),
      hasExternalDependencies: tasks.some(t => (t.dependencies?.length || 0) > 5)
    };

    const riskCount = Object.values(riskFactors).filter(Boolean).length;
    return riskCount / Object.keys(riskFactors).length;
  }

  private async calculateStrategyScores(
    tasks: Task[], 
    complexity: ComplexityMetrics, 
    userProfile: UserBehaviorProfile | null
  ): Promise<Map<string, any>> {
    const scores = new Map();

    for (const [strategy, performances] of this.strategyPerformance) {
      const avgPerformance = performances.reduce((a, b) => a + b, 0) / performances.length;
      const complexityFit = this.calculateComplexityFit(strategy, complexity);
      const userPreference = userProfile ? this.getUserPreference(userProfile, strategy) : 0.5;
      
      const confidence = this.calculateConfidence(performances);
      const resourceOptimization = this.estimateResourceOptimization(strategy, complexity);
      
      scores.set(strategy, {
        type: strategy,
        performance: avgPerformance * complexityFit * (0.7 + userPreference * 0.3),
        confidence,
        resourceOptimization,
        reasoning: this.generateReasoning(strategy, complexity, avgPerformance, complexityFit)
      });
    }

    return scores;
  }

  private calculateComplexityFit(strategy: string, complexity: ComplexityMetrics): number {
    const strategyFits = {
      parallel: 1 - complexity.dependencyComplexity,
      pipeline: complexity.dependencyComplexity,
      hybrid: 0.8, // Generally good for most cases
      swarm: complexity.computationalComplexity,
      quantum: complexity.computationalComplexity * complexity.timeComplexity
    };

    return strategyFits[strategy as keyof typeof strategyFits] || 0.5;
  }

  private getUserPreference(profile: UserBehaviorProfile, strategy: string): number {
    return profile.preferredStrategies.includes(strategy) ? 1 : 0;
  }

  private calculateConfidence(performances: number[]): number {
    if (performances.length < 5) return 0.5;
    
    const mean = performances.reduce((a, b) => a + b, 0) / performances.length;
    const variance = performances.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / performances.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Lower standard deviation = higher confidence
    return Math.max(0.1, 1 - standardDeviation);
  }

  private estimateResourceOptimization(strategy: string, complexity: ComplexityMetrics): number {
    const optimizations = {
      parallel: 0.9 - complexity.memoryRequirements * 0.3,
      pipeline: 0.8 - complexity.timeComplexity * 0.2,
      hybrid: 0.85,
      swarm: 0.95 - complexity.riskLevel * 0.2,
      quantum: 0.98 - complexity.computationalComplexity * 0.1
    };

    return Math.max(0.1, optimizations[strategy as keyof typeof optimizations] || 0.7);
  }

  private generateReasoning(
    strategy: string, 
    complexity: ComplexityMetrics, 
    performance: number, 
    fit: number
  ): string {
    const reasons = [];

    if (performance > 0.8) reasons.push("High historical performance");
    if (fit > 0.8) reasons.push("Excellent complexity match");
    if (complexity.dependencyComplexity < 0.3 && strategy === 'parallel') {
      reasons.push("Low dependencies favor parallel execution");
    }
    if (complexity.computationalComplexity > 0.7 && strategy === 'quantum') {
      reasons.push("High computational complexity benefits from quantum optimization");
    }

    return reasons.join(", ") || "Balanced approach for general use case";
  }

  private selectBestStrategy(scores: Map<string, any>): any {
    let bestStrategy = null;
    let bestScore = -1;

    for (const [strategy, data] of scores) {
      const combinedScore = data.performance * data.confidence * data.resourceOptimization;
      if (combinedScore > bestScore) {
        bestScore = combinedScore;
        bestStrategy = data;
      }
    }

    return bestStrategy || {
      type: 'hybrid',
      performance: 0.7,
      confidence: 0.5,
      resourceOptimization: 0.8,
      reasoning: "Default fallback strategy"
    };
  }

  public async personalizeForUser(userId: string, preferences: any): Promise<void> {
    let profile = this.userProfiles.get(userId);
    
    if (!profile) {
      profile = {
        userId,
        preferredStrategies: [],
        taskPatterns: new Map(),
        performancePreferences: {
          speed: 0.5,
          accuracy: 0.5,
          resourceEfficiency: 0.5
        },
        learningHistory: []
      };
    }

    // Update preferences
    if (preferences.speed !== undefined) profile.performancePreferences.speed = preferences.speed;
    if (preferences.accuracy !== undefined) profile.performancePreferences.accuracy = preferences.accuracy;
    if (preferences.resourceEfficiency !== undefined) {
      profile.performancePreferences.resourceEfficiency = preferences.resourceEfficiency;
    }

    this.userProfiles.set(userId, profile);
  }

  private getUserProfile(userId: string): UserBehaviorProfile | null {
    return this.userProfiles.get(userId) || null;
  }

  private async updateComplexityModel(result: ExecutionResult): Promise<void> {
    // Update the complexity prediction model based on actual vs predicted complexity
    const taskType = result.taskType || 'general';
    const actualComplexity = this.calculateActualComplexity(result);
    
    const modelKey = `complexity_${taskType}`;
    const currentModel = this.learningModel.get(modelKey) || { predictions: [], actuals: [] };
    
    currentModel.predictions.push(result.estimatedComplexity || 0.5);
    currentModel.actuals.push(actualComplexity);
    
    // Keep only last 500 data points
    if (currentModel.predictions.length > 500) {
      currentModel.predictions.shift();
      currentModel.actuals.shift();
    }
    
    this.learningModel.set(modelKey, currentModel);
  }

  private calculateActualComplexity(result: ExecutionResult): number {
    const timeRatio = result.executionTime / (result.estimatedTime || 1);
    const resourceRatio = result.resourceUsage / (result.estimatedResources || 1);
    const errorRate = result.errors ? result.errors.length / 10 : 0;
    
    return Math.min(1, (timeRatio + resourceRatio + errorRate) / 3);
  }

  private async adaptClusterConfiguration(result: ExecutionResult): Promise<void> {
    // Adapt cluster configurations based on execution results
    if (result.clusterPerformance) {
      for (const [clusterId, performance] of Object.entries(result.clusterPerformance)) {
        if (performance < 0.6) {
          // Suggest cluster optimization
          console.log(`Cluster ${clusterId} underperforming, suggesting optimization`);
        }
      }
    }
  }

  private async retrainModel(): Promise<void> {
    console.log('Retraining adaptive learning model with new data...');
    
    // In a real implementation, this would involve:
    // 1. Feature extraction from execution history
    // 2. Model training using ML algorithms
    // 3. Cross-validation and performance evaluation
    // 4. Model deployment and A/B testing
    
    // For now, we'll update our simple heuristics
    this.updateHeuristics();
  }

  private updateHeuristics(): void {
    // Update strategy selection heuristics based on recent performance
    const recentResults = this.executionHistory.slice(-100);
    
    for (const [strategy, performances] of this.strategyPerformance) {
      const recentPerformances = recentResults
        .filter(r => r.strategy === strategy)
        .map(r => this.calculatePerformanceScore(r));
      
      if (recentPerformances.length > 5) {
        const recentAvg = recentPerformances.reduce((a, b) => a + b, 0) / recentPerformances.length;
        const historicalAvg = performances.reduce((a, b) => a + b, 0) / performances.length;
        
        // If recent performance significantly differs, adjust the model
        if (Math.abs(recentAvg - historicalAvg) > this.adaptationThreshold) {
          console.log(`Adapting model for strategy ${strategy}: ${historicalAvg} -> ${recentAvg}`);
        }
      }
    }
  }

  public getLearningMetrics(): LearningMetrics {
    const recentResults = this.executionHistory.slice(-50);
    const olderResults = this.executionHistory.slice(-100, -50);
    
    const recentAvg = recentResults.length > 0 
      ? recentResults.reduce((sum, r) => sum + this.calculatePerformanceScore(r), 0) / recentResults.length 
      : 0.5;
    
    const olderAvg = olderResults.length > 0 
      ? olderResults.reduce((sum, r) => sum + this.calculatePerformanceScore(r), 0) / olderResults.length 
      : 0.5;

    return {
      accuracyImprovement: recentAvg - olderAvg,
      performanceGains: Math.max(0, recentAvg - 0.5) * 100,
      adaptationSpeed: this.executionHistory.length / 100, // Adaptations per 100 executions
      predictionConfidence: this.calculateOverallConfidence()
    };
  }

  private calculateOverallConfidence(): number {
    const confidences = Array.from(this.strategyPerformance.values())
      .map(performances => this.calculateConfidence(performances));
    
    return confidences.length > 0 
      ? confidences.reduce((a, b) => a + b, 0) / confidences.length 
      : 0.5;
  }

  public exportLearningData(): any {
    return {
      executionHistory: this.executionHistory.slice(-1000), // Last 1000 executions
      strategyPerformance: Object.fromEntries(this.strategyPerformance),
      userProfiles: Object.fromEntries(this.userProfiles),
      learningModel: Object.fromEntries(this.learningModel),
      metrics: this.getLearningMetrics()
    };
  }

  public importLearningData(data: any): void {
    if (data.executionHistory) this.executionHistory = data.executionHistory;
    if (data.strategyPerformance) {
      this.strategyPerformance = new Map(Object.entries(data.strategyPerformance));
    }
    if (data.userProfiles) {
      this.userProfiles = new Map(Object.entries(data.userProfiles));
    }
    if (data.learningModel) {
      this.learningModel = new Map(Object.entries(data.learningModel));
    }
  }
}