// Robust JSON utilities with comprehensive error handling
export interface JsonParseResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface JsonStringifyResult {
  success: boolean;
  data?: string;
  error?: string;
}

/**
 * Safe JSON.parse with comprehensive error handling
 */
export function safeJsonParse<T = any>(
  jsonString: string | null | undefined,
  fallback?: T
): JsonParseResult<T> {
  // Handle null/undefined/empty cases
  if (!jsonString || typeof jsonString !== 'string') {
    return {
      success: false,
      error: 'Input is null, undefined, or not a string',
      data: fallback
    };
  }

  // Trim whitespace
  const trimmed = jsonString.trim();
  if (!trimmed) {
    return {
      success: false,
      error: 'Input is empty after trimming',
      data: fallback
    };
  }

  try {
    // Validate basic JSON structure
    if (!isValidJsonStructure(trimmed)) {
      return {
        success: false,
        error: 'Invalid JSON structure detected',
        data: fallback
      };
    }

    const parsed = JSON.parse(trimmed);
    return {
      success: true,
      data: parsed
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown JSON parse error';
    console.warn('JSON parse failed:', errorMessage, 'Input:', trimmed.substring(0, 100));
    
    return {
      success: false,
      error: errorMessage,
      data: fallback
    };
  }
}

/**
 * Safe JSON.stringify with error handling
 */
export function safeJsonStringify(
  value: any,
  replacer?: (key: string, value: any) => any,
  space?: string | number
): JsonStringifyResult {
  try {
    // Handle circular references and other problematic values
    const cleanValue = sanitizeForJson(value);
    const result = JSON.stringify(cleanValue, replacer, space);
    
    return {
      success: true,
      data: result
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown JSON stringify error';
    console.warn('JSON stringify failed:', errorMessage);
    
    return {
      success: false,
      error: errorMessage,
      data: '{}'
    };
  }
}

/**
 * Validate basic JSON structure without parsing
 */
function isValidJsonStructure(str: string): boolean {
  // Check for basic JSON structure patterns
  const trimmed = str.trim();
  
  // Must start and end with proper brackets/braces
  const validStarts = ['{', '[', '"'];
  const validEnds = ['}', ']', '"'];
  
  if (!validStarts.some(start => trimmed.startsWith(start))) {
    // Allow primitive values
    if (!['true', 'false', 'null'].includes(trimmed) && !isNumeric(trimmed)) {
      return false;
    }
  }
  
  // Check for common malformed patterns
  if (trimmed.includes('undefined') || trimmed.includes('NaN')) {
    return false;
  }
  
  // Check for unescaped control characters
  if (/[\x00-\x1f]/.test(trimmed)) {
    return false;
  }
  
  return true;
}

/**
 * Check if string represents a valid number
 */
function isNumeric(str: string): boolean {
  return !isNaN(Number(str)) && !isNaN(parseFloat(str));
}

/**
 * Sanitize value for JSON serialization
 */
function sanitizeForJson(value: any, seen = new WeakSet()): any {
  if (value === null || value === undefined) {
    return null;
  }
  
  if (typeof value === 'function') {
    return '[Function]';
  }
  
  if (typeof value === 'symbol') {
    return value.toString();
  }
  
  if (typeof value === 'bigint') {
    return value.toString();
  }
  
  if (value instanceof Date) {
    return value.toISOString();
  }
  
  if (value instanceof Error) {
    return {
      name: value.name,
      message: value.message,
      stack: value.stack
    };
  }
  
  if (typeof value === 'object') {
    // Handle circular references
    if (seen.has(value)) {
      return '[Circular Reference]';
    }
    seen.add(value);
    
    if (Array.isArray(value)) {
      return value.map(item => sanitizeForJson(item, seen));
    }
    
    const sanitized: any = {};
    for (const [key, val] of Object.entries(value)) {
      sanitized[key] = sanitizeForJson(val, seen);
    }
    return sanitized;
  }
  
  // Handle NaN and Infinity
  if (typeof value === 'number') {
    if (isNaN(value)) return null;
    if (!isFinite(value)) return null;
  }
  
  return value;
}

/**
 * Parse JSON with automatic repair attempts
 */
export function parseJsonWithRepair<T = any>(
  jsonString: string,
  fallback?: T
): JsonParseResult<T> {
  // First try normal parsing
  const normalResult = safeJsonParse<T>(jsonString, fallback);
  if (normalResult.success) {
    return normalResult;
  }
  
  // Try repair strategies
  const repairStrategies = [
    // Fix common trailing comma issues
    (str: string) => str.replace(/,(\s*[}\]])/g, '$1'),
    
    // Fix unquoted keys
    (str: string) => str.replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":'),
    
    // Fix single quotes
    (str: string) => str.replace(/'/g, '"'),
    
    // Fix incomplete objects/arrays
    (str: string) => {
      if (str.trim().endsWith(',')) {
        return str.trim().slice(0, -1);
      }
      return str;
    },
    
    // Add missing closing brackets
    (str: string) => {
      const openBraces = (str.match(/{/g) || []).length;
      const closeBraces = (str.match(/}/g) || []).length;
      const openBrackets = (str.match(/\[/g) || []).length;
      const closeBrackets = (str.match(/\]/g) || []).length;
      
      let fixed = str;
      for (let i = 0; i < openBraces - closeBraces; i++) {
        fixed += '}';
      }
      for (let i = 0; i < openBrackets - closeBrackets; i++) {
        fixed += ']';
      }
      return fixed;
    }
  ];
  
  for (const strategy of repairStrategies) {
    try {
      const repaired = strategy(jsonString);
      const result = safeJsonParse<T>(repaired, fallback);
      if (result.success) {
        console.log('JSON repaired successfully using strategy');
        return result;
      }
    } catch (error) {
      // Continue to next strategy
    }
  }
  
  // All repair attempts failed
  return {
    success: false,
    error: `JSON parsing failed even after repair attempts: ${normalResult.error}`,
    data: fallback
  };
}

/**
 * Validate and parse localStorage data safely
 */
export function parseStorageData<T = any>(
  key: string,
  fallback?: T
): JsonParseResult<T> {
  try {
    const stored = localStorage.getItem(key);
    return safeJsonParse<T>(stored, fallback);
  } catch (error) {
    return {
      success: false,
      error: `localStorage access failed: ${error}`,
      data: fallback
    };
  }
}

/**
 * Safely store data to localStorage
 */
export function storeData(key: string, value: any): boolean {
  try {
    const result = safeJsonStringify(value);
    if (result.success && result.data) {
      localStorage.setItem(key, result.data);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Failed to store data:', error);
    return false;
  }
}

/**
 * Parse JSONL (JSON Lines) safely
 */
export function parseJsonLines<T = any>(
  jsonlString: string,
  fallback: T[] = []
): JsonParseResult<T[]> {
  try {
    const lines = jsonlString.split('\n').filter(line => line.trim());
    const results: T[] = [];
    const errors: string[] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const result = safeJsonParse<T>(lines[i]);
      if (result.success && result.data !== undefined) {
        results.push(result.data);
      } else {
        errors.push(`Line ${i + 1}: ${result.error}`);
      }
    }
    
    if (errors.length > 0 && results.length === 0) {
      return {
        success: false,
        error: `All lines failed to parse: ${errors.join(', ')}`,
        data: fallback
      };
    }
    
    return {
      success: true,
      data: results
    };
  } catch (error) {
    return {
      success: false,
      error: `JSONL parsing failed: ${error}`,
      data: fallback
    };
  }
}