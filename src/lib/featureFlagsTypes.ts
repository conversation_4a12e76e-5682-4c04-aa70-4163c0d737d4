export interface FeatureFlag {
  enabled: boolean;
  [key: string]: any;
}

export interface FeatureFlags {
  collaboration: {
    enabled: boolean;
    useLocalBackend: boolean;
    localWebSocketUrl: string;
    localShareUrl: string;
  };
  voiceControl: FeatureFlag;
  codeFlow: FeatureFlag;
  performanceProfiler: FeatureFlag;
  smartTemplates: FeatureFlag;
  pluginSystem: FeatureFlag;
  agentSystem: FeatureFlag;
  mcpMarketplace: FeatureFlag;
  focusMode: FeatureFlag;
  workflow: FeatureFlag;
  smartFeatureSelection: FeatureFlag;
}

export const defaultFeatureFlags: FeatureFlags = {
  collaboration: {
    enabled: false,
    useLocalBackend: true,
    localWebSocketUrl: 'ws://localhost:8080/ws',
    localShareUrl: 'http://localhost:3000/join'
  },
  voiceControl: {
    enabled: false
  },
  codeFlow: {
    enabled: true
  },
  performanceProfiler: {
    enabled: true
  },
  smartTemplates: {
    enabled: true
  },
  pluginSystem: {
    enabled: true
  },
  agentSystem: {
    enabled: true
  },
  mcpMarketplace: {
    enabled: true
  },
  focusMode: {
    enabled: false
  },
  workflow: {
    enabled: true
  },
  smartFeatureSelection: {
    enabled: false
  }
};