import { invoke } from "@tauri-apps/api/core";
import { notifications, handleApiError } from "./notifications";

/** Process type for tracking in ProcessRegistry */
export type ProcessType = 
  | { AgentRun: { agent_id: number; agent_name: string } }
  | { ClaudeSession: { session_id: string } };

/** Information about a running process */
export interface ProcessInfo {
  run_id: number;
  process_type: ProcessType;
  pid: number;
  started_at: string;
  project_path: string;
  task: string;
  model: string;
}

/**
 * Represents a project in the ~/.claude/projects directory
 */
export interface Project {
  /** The project ID (derived from the directory name) */
  id: string;
  /** The original project path (decoded from the directory name) */
  path: string;
  /** List of session IDs (JSONL file names without extension) */
  sessions: string[];
  /** Unix timestamp when the project directory was created */
  created_at: number;
}

/**
 * Represents a session with its metadata
 */
export interface Session {
  /** The session ID (UUID) */
  id: string;
  /** The project ID this session belongs to */
  project_id: string;
  /** The project path */
  project_path: string;
  /** Optional todo data associated with this session */
  todo_data?: TodoData;
  /** Unix timestamp when the session file was created */
  created_at: number;
  /** First user message content (if available) */
  first_message?: string;
  /** Timestamp of the first user message (if available) */
  message_timestamp?: string;
}

/**
 * Represents todo item data in sessions
 */
export interface TodoData {
  todos: Array<{
    id: string;
    content: string;
    status: 'pending' | 'in_progress' | 'completed';
    priority: 'low' | 'medium' | 'high';
    created_at?: number;
    updated_at?: number;
  }>;
  metadata?: {
    total_count: number;
    completed_count: number;
    last_updated: number;
  };
}

/**
 * SuperClaude specific settings and configurations
 */
export interface SuperClaudeSettings {
  // Core settings
  thinking_modes?: {
    introspection?: boolean;
    ultra_compressed?: boolean;
    code_economy?: boolean;
  };
  
  // Performance settings
  token_economy?: {
    max_tokens?: number;
    compression_level?: 'low' | 'medium' | 'high';
  };
  
  // Persona settings
  active_persona?: string;
  persona_config?: {
    [key: string]: unknown;
  };
  
  // MCP settings
  mcp_config?: {
    enabled_servers?: string[];
    server_overrides?: {
      [key: string]: unknown;
    };
  };
  
  // Additional configuration
  [key: string]: unknown;
}

/**
 * Represents the settings from ~/.claude/settings.json
 */
export interface ClaudeSettings {
  // Core Claude settings
  anthropic_api_key?: string;
  model?: string;
  max_tokens?: number;
  temperature?: number;
  
  // SuperClaude settings
  superclaude?: SuperClaudeSettings;
  
  // Additional dynamic settings
  [key: string]: unknown;
}

/**
 * Represents the Claude Code version status
 */
export interface ClaudeVersionStatus {
  /** Whether Claude Code is installed and working */
  is_installed: boolean;
  /** The version string if available */
  version?: string;
  /** The full output from the command */
  output: string;
}

/**
 * Represents a CLAUDE.md file found in the project
 */
export interface ClaudeMdFile {
  /** Relative path from the project root */
  relative_path: string;
  /** Absolute path to the file */
  absolute_path: string;
  /** File size in bytes */
  size: number;
  /** Last modified timestamp */
  modified: number;
}

/**
 * Represents a file or directory entry
 */
export interface FileEntry {
  name: string;
  path: string;
  is_directory: boolean;
  size: number;
  extension?: string;
}

/**
 * Represents a Claude installation found on the system
 */
export interface ClaudeInstallation {
  /** Full path to the Claude binary (or "claude-code" for sidecar) */
  path: string;
  /** Version string if available */
  version?: string;
  /** Source of discovery (e.g., "nvm", "system", "homebrew", "which", "bundled") */
  source: string;
  /** Type of installation */
  installation_type: "Bundled" | "System" | "Custom";
}

// Theme API types (see more specific definition below)

export interface ColorScheme {
  primary: string;
  accent: string;
  background: string;
}

// Plugin API types
export interface Plugin {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  enabled: boolean;
  installed: boolean;
  verified: boolean;
  category?: string;
  icon?: string;
  repository?: string;
  capabilities: string[];
  settings?: PluginSettings;
  rating?: number;
  downloads?: number;
  readme?: string;
  changelog?: ChangelogEntry[];
}

export interface PluginManifest {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  entry: string;
  capabilities: string[];
  repository?: string;
  icon?: string;
  category?: string;
}

export interface PluginSettings {
  [key: string]: PluginSettingDefinition;
}

export interface PluginSettingDefinition {
  label: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  value: string | number | boolean | unknown[] | Record<string, unknown>;
  default?: string | number | boolean | unknown[] | Record<string, unknown>;
}

export interface ChangelogEntry {
  version: string;
  changes: string;
}

// Agent API types
export interface Agent {
  id?: number;
  name: string;
  icon: string;
  system_prompt: string;
  default_task?: string;
  model: string;
  created_at: string;
  updated_at: string;
}

export interface AgentExport {
  version: number;
  exported_at: string;
  agent: {
    name: string;
    icon: string;
    system_prompt: string;
    default_task?: string;
    model: string;
  };
}

export interface GitHubAgentFile {
  name: string;
  path: string;
  download_url: string;
  size: number;
  sha: string;
}

export interface AgentRun {
  id?: number;
  agent_id: number;
  agent_name: string;
  agent_icon: string;
  task: string;
  model: string;
  project_path: string;
  session_id: string;
  status: string; // 'pending', 'running', 'completed', 'failed', 'cancelled'
  pid?: number;
  process_started_at?: string;
  created_at: string;
  completed_at?: string;
}

export interface AgentRunMetrics {
  duration_ms?: number;
  total_tokens?: number;
  cost_usd?: number;
  message_count?: number;
}

export interface AgentRunWithMetrics {
  id?: number;
  agent_id: number;
  agent_name: string;
  agent_icon: string;
  task: string;
  model: string;
  project_path: string;
  session_id: string;
  status: string; // 'pending', 'running', 'completed', 'failed', 'cancelled'
  pid?: number;
  process_started_at?: string;
  created_at: string;
  completed_at?: string;
  metrics?: AgentRunMetrics;
  output?: string; // Real-time JSONL content
}

// Enhanced Agent System Types
export interface EnhancedAgent {
  id: string;
  name: string;
  type: string;
  description: string;
  capabilities: string[];
  status: string;
  avatar: string;
  personality: EnhancedAgentPersonality;
  metrics: EnhancedAgentMetrics;
  configuration: EnhancedAgentConfiguration;
  isActive: boolean;
  createdAt: Date;
  lastUsed: Date;
  version: string;
  specialty: string[];
}

export interface EnhancedAgentPersonality {
  communication_style: string;
  expertise_level: string;
  response_speed: string;
  creativity_level: number;
  detail_orientation: number;
  collaboration_preference: string;
}

export interface EnhancedAgentMetrics {
  tasksCompleted: number;
  successRate: number;
  averageResponseTime: number;
  userSatisfaction: number;
  tokensUsed: number;
  costEfficiency: number;
  learningProgress: number;
  collaborationScore: number;
}

export interface EnhancedAgentConfiguration {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  tools: string[];
  permissions: EnhancedAgentPermissions;
  autonomyLevel: number;
  memorySize: number;
  learningEnabled: boolean;
}

export interface EnhancedAgentPermissions {
  canReadFiles: boolean;
  canWriteFiles: boolean;
  canExecuteCode: boolean;
  canAccessInternet: boolean;
  canModifySystem: boolean;
  canCollaborate: boolean;
  canLearn: boolean;
  maxCostPerTask: number;
}

export interface EnhancedAgentTask {
  id: string;
  agentId: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  progress: number;
  startTime: Date;
  estimatedCompletion: Date;
  actualCompletion?: Date;
  result?: string;
  dependencies: string[];
  tags: string[];
}

export interface EnhancedAgentConversation {
  id: string;
  agentId: string;
  messages: EnhancedAgentMessage[];
  context: string;
  startTime: Date;
  isActive: boolean;
}

export interface EnhancedAgentMessage {
  id: string;
  role: string;
  content: string;
  timestamp: Date;
  metadata?: {
    tokensUsed?: number;
    responseTime?: number;
    confidence?: number;
    sources?: string[];
  };
}

// Usage Dashboard types
export interface UsageEntry {
  project: string;
  timestamp: string;
  model: string;
  input_tokens: number;
  output_tokens: number;
  cache_write_tokens: number;
  cache_read_tokens: number;
  cost: number;
}

export interface ModelUsage {
  model: string;
  total_cost: number;
  total_tokens: number;
  input_tokens: number;
  output_tokens: number;
  cache_creation_tokens: number;
  cache_read_tokens: number;
  session_count: number;
}

export interface DailyUsage {
  date: string;
  total_cost: number;
  total_tokens: number;
  models_used: string[];
}

export interface ProjectUsage {
  project_path: string;
  project_name: string;
  total_cost: number;
  total_tokens: number;
  session_count: number;
  last_used: string;
}

export interface UsageStats {
  total_cost: number;
  total_tokens: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cache_creation_tokens: number;
  total_cache_read_tokens: number;
  total_sessions: number;
  by_model: ModelUsage[];
  by_date: DailyUsage[];
  by_project: ProjectUsage[];
}

/**
 * Represents a checkpoint in the session timeline
 */
export interface Checkpoint {
  id: string;
  sessionId: string;
  projectId: string;
  messageIndex: number;
  timestamp: string;
  description?: string;
  parentCheckpointId?: string;
  metadata: CheckpointMetadata;
}

/**
 * Metadata associated with a checkpoint
 */
export interface CheckpointMetadata {
  totalTokens: number;
  modelUsed: string;
  userPrompt: string;
  fileChanges: number;
  snapshotSize: number;
}

/**
 * Represents a file snapshot at a checkpoint
 */
export interface FileSnapshot {
  checkpointId: string;
  filePath: string;
  content: string;
  hash: string;
  isDeleted: boolean;
  permissions?: number;
  size: number;
}

/**
 * Represents a node in the timeline tree
 */
export interface TimelineNode {
  checkpoint: Checkpoint;
  children: TimelineNode[];
  fileSnapshotIds: string[];
}

/**
 * The complete timeline for a session
 */
export interface SessionTimeline {
  sessionId: string;
  rootNode?: TimelineNode;
  currentCheckpointId?: string;
  autoCheckpointEnabled: boolean;
  checkpointStrategy: CheckpointStrategy;
  totalCheckpoints: number;
}

/**
 * Strategy for automatic checkpoint creation
 */
export type CheckpointStrategy = 'manual' | 'per_prompt' | 'per_tool_use' | 'smart';

/**
 * Result of a checkpoint operation
 */
export interface CheckpointResult {
  checkpoint: Checkpoint;
  filesProcessed: number;
  warnings: string[];
}

/**
 * Diff between two checkpoints
 */
export interface CheckpointDiff {
  fromCheckpointId: string;
  toCheckpointId: string;
  modifiedFiles: FileDiff[];
  addedFiles: string[];
  deletedFiles: string[];
  tokenDelta: number;
}

/**
 * Diff for a single file
 */
export interface FileDiff {
  path: string;
  additions: number;
  deletions: number;
  diffContent?: string;
}

/**
 * Represents an MCP server configuration
 */
export interface MCPServer {
  /** Server name/identifier */
  name: string;
  /** Transport type: "stdio" or "sse" */
  transport: string;
  /** Command to execute (for stdio) */
  command?: string;
  /** Command arguments (for stdio) */
  args: string[];
  /** Environment variables */
  env: Record<string, string>;
  /** URL endpoint (for SSE) */
  url?: string;
  /** Configuration scope: "local", "project", or "user" */
  scope: string;
  /** Whether the server is currently active */
  is_active: boolean;
  /** Server status */
  status: ServerStatus;
}

/**
 * Server status information
 */
export interface ServerStatus {
  /** Whether the server is running */
  running: boolean;
  /** Last error message if any */
  error?: string;
  /** Last checked timestamp */
  last_checked?: number;
}

/**
 * MCP configuration for project scope (.mcp.json)
 */
export interface MCPProjectConfig {
  mcpServers: Record<string, MCPServerConfig>;
}

/**
 * Individual server configuration in .mcp.json
 */
export interface MCPServerConfig {
  command: string;
  args: string[];
  env: Record<string, string>;
}

/**
 * Result of adding a server
 */
export interface AddServerResult {
  success: boolean;
  message: string;
  server_name?: string;
}

/**
 * Import result for multiple servers
 */
export interface ImportResult {
  imported_count: number;
  failed_count: number;
  servers: ImportServerResult[];
}

/**
 * Code review result for a file
 */
export interface CodeReviewResult {
  file: string;
  issues: ReviewIssue[];
  suggestions: ReviewSuggestion[];
  metrics: ReviewMetrics;
}

/**
 * Code review issue
 */
export interface ReviewIssue {
  severity: 'error' | 'warning' | 'info';
  line: number;
  message: string;
  rule?: string;
}

/**
 * Code review suggestion
 */
export interface ReviewSuggestion {
  type: 'performance' | 'security' | 'best-practice' | 'refactor';
  description: string;
  code?: string;
}

/**
 * Code review metrics
 */
export interface ReviewMetrics {
  complexity: number;
  maintainability: number;
  coverage?: number;
  duplicateLines?: number;
}

/**
 * Result for individual server import
 */
export interface ImportServerResult {
  name: string;
  success: boolean;
  error?: string;
}

/**
 * Collaborative session
 */
export interface CollaborativeSession {
  id: string;
  sessionId: string;
  projectPath: string;
  sessionCode: string;
  shareUrl: string;
  wsUrl: string;
  ownerId: string;
  createdAt: number;
  collaborators: Collaborator[];
}

/**
 * Collaborator in a session
 */
export interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer';
  status: 'online' | 'offline' | 'away';
  cursor?: CursorPosition;
  color: string;
  joinedAt: number;
}

/**
 * Cursor position in collaborative editing
 */
export interface CursorPosition {
  line: number;
  column: number;
  file: string;
}

/**
 * Session permissions for a user
 */
export interface SessionPermissions {
  canEdit: boolean;
  canInvite: boolean;
  canRemove: boolean;
  canChat: boolean;
  canVoice: boolean;
}

/**
 * Smart template
 */
export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  icon: string;
  tags: string[];
  content: string;
  variables: TemplateVariable[];
  examples: TemplateExample[];
  isFavorite: boolean;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
  author: string;
  isPublic: boolean;
}

/**
 * Template variable
 */
export interface TemplateVariable {
  name: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  default?: string | number | boolean | unknown[] | Record<string, unknown>;
  required: boolean;
  placeholder?: string;
}

/**
 * Template example
 */
export interface TemplateExample {
  title: string;
  values: Record<string, string | number | boolean | unknown[] | Record<string, unknown>>;
  output: string;
}

/**
 * Template category
 */
export type TemplateCategory = 
  | 'code-generation'
  | 'debugging'
  | 'documentation'
  | 'testing'
  | 'refactoring'
  | 'security'
  | 'performance'
  | 'architecture'
  | 'data-processing'
  | 'devops'
  | 'custom';

/**
 * Code flow visualization data
 */
export interface CodeFlowData {
  nodes: CodeNode[];
  links: CodeLink[];
  clusters: Cluster[];
}

/**
 * Code node in visualization
 */
export interface CodeNode {
  id: string;
  name: string;
  type: 'file' | 'function' | 'class' | 'module' | 'component';
  path: string;
  lines: number;
  complexity: number;
  dependencies: string[];
  dependents: string[];
  metrics: NodeMetrics;
}

/**
 * Link between code nodes
 */
export interface CodeLink {
  source: string;
  target: string;
  type: 'import' | 'call' | 'extends' | 'implements' | 'uses';
  strength: number;
}

/**
 * Node metrics
 */
export interface NodeMetrics {
  linesOfCode: number;
  cyclomaticComplexity: number;
  maintainabilityIndex: number;
  testCoverage?: number;
  lastModified: Date;
  author?: string;
}

/**
 * Cluster of nodes
 */
export interface Cluster {
  id: string;
  name: string;
  nodes: string[];
  type: 'module' | 'feature' | 'layer';
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  timestamp: Date;
  apiCalls: number;
  totalTokens: number;
  inputTokens: number;
  outputTokens: number;
  latency: number;
  cost: number;
  model: string;
  endpoint: string;
  success: boolean;
  errorRate: number;
  cacheHits: number;
  cacheMisses: number;
}

/**
 * Aggregated performance metrics
 */
export interface AggregatedMetrics {
  totalCalls: number;
  totalTokens: number;
  totalCost: number;
  averageLatency: number;
  errorRate: number;
  cacheHitRate: number;
  tokensPerMinute: number;
  costPerHour: number;
  modelUsage: Record<string, number>;
  endpointUsage: Record<string, number>;
  peakUsageTime: Date;
  bottlenecks: Bottleneck[];
}

/**
 * Performance bottleneck
 */
export interface Bottleneck {
  type: 'latency' | 'cost' | 'error' | 'token';
  severity: 'low' | 'medium' | 'high';
  description: string;
  recommendation: string;
  impact: number;
}

/**
 * Performance data
 */
export interface PerformanceData {
  metrics: PerformanceMetrics[];
  aggregated: AggregatedMetrics;
}

/**
 * Theme configuration
 */
export interface ThemeConfig {
  theme: 'light' | 'dark' | 'system';
  color_scheme: string;
  font_size: number;
  reduce_motion: boolean;
  high_contrast: boolean;
  color_blind_mode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
}

// Plugin types defined above

/**
 * API client for interacting with the Rust backend
 */
export const api = {
  /**
   * Lists all projects in the ~/.claude/projects directory
   * @returns Promise resolving to an array of projects
   */
  async listProjects(): Promise<Project[]> {
    try {
      return await invoke<Project[]>("list_projects");
    } catch (error) {
      console.error("Failed to list projects:", error);
      handleApiError(error, "list projects");
      throw error;
    }
  },

  /**
   * Retrieves sessions for a specific project
   * @param projectId - The ID of the project to retrieve sessions for
   * @returns Promise resolving to an array of sessions
   */
  async getProjectSessions(projectId: string): Promise<Session[]> {
    try {
      return await invoke<Session[]>('get_project_sessions', { projectId });
    } catch (error) {
      console.error("Failed to get project sessions:", error);
      handleApiError(error, "get project sessions");
      throw error;
    }
  },

  /**
   * Fetch list of agents from GitHub repository
   * @returns Promise resolving to list of available agents on GitHub
   */
  async fetchGitHubAgents(): Promise<GitHubAgentFile[]> {
    try {
      return await invoke<GitHubAgentFile[]>('fetch_github_agents');
    } catch (error) {
      console.error("Failed to fetch GitHub agents:", error);
      throw error;
    }
  },

  /**
   * Fetch and preview a specific agent from GitHub
   * @param downloadUrl - The download URL for the agent file
   * @returns Promise resolving to the agent export data
   */
  async fetchGitHubAgentContent(downloadUrl: string): Promise<AgentExport> {
    try {
      return await invoke<AgentExport>('fetch_github_agent_content', { downloadUrl });
    } catch (error) {
      console.error("Failed to fetch GitHub agent content:", error);
      throw error;
    }
  },

  /**
   * Import an agent directly from GitHub
   * @param downloadUrl - The download URL for the agent file
   * @returns Promise resolving to the imported agent
   */
  async importAgentFromGitHub(downloadUrl: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_agent_from_github', { downloadUrl });
    } catch (error) {
      console.error("Failed to import agent from GitHub:", error);
      throw error;
    }
  },

  /**
   * Reads the Claude settings file
   * @returns Promise resolving to the settings object
   */
  async getClaudeSettings(): Promise<ClaudeSettings> {
    try {
      const result = await invoke<{ data: ClaudeSettings }>("get_claude_settings");
      console.log("Raw result from get_claude_settings:", result);
      
      // The Rust backend returns ClaudeSettings { data: ... }
      // We need to extract the data field
      if (result && typeof result === 'object' && 'data' in result) {
        return result.data;
      }
      
      // If the result is already the settings object, return it
      return result as ClaudeSettings;
    } catch (error) {
      console.error("Failed to get Claude settings:", error);
      throw error;
    }
  },

  /**
   * Opens a new Claude Code session
   * @param path - Optional path to open the session in
   * @returns Promise resolving when the session is opened
   */
  async openNewSession(path?: string): Promise<string> {
    try {
      const sessionId = await invoke<string>("open_new_session", { path });
      notifications.success("New Claude session opened successfully");
      return sessionId;
    } catch (error) {
      console.error("Failed to open new session:", error);
      handleApiError(error, "open new Claude session");
      throw error;
    }
  },

  /**
   * Reads the CLAUDE.md system prompt file
   * @returns Promise resolving to the system prompt content
   */
  async getSystemPrompt(): Promise<string> {
    try {
      return await invoke<string>("get_system_prompt");
    } catch (error) {
      console.error("Failed to get system prompt:", error);
      throw error;
    }
  },

  /**
   * Checks if Claude Code is installed and gets its version
   * @returns Promise resolving to the version status
   */
  async checkClaudeVersion(): Promise<ClaudeVersionStatus> {
    try {
      return await invoke<ClaudeVersionStatus>("check_claude_version");
    } catch (error) {
      console.error("Failed to check Claude version:", error);
      throw error;
    }
  },

  /**
   * Saves the CLAUDE.md system prompt file
   * @param content - The new content for the system prompt
   * @returns Promise resolving when the file is saved
   */
  async saveSystemPrompt(content: string): Promise<string> {
    try {
      const result = await invoke<string>("save_system_prompt", { content });
      notifications.success("System prompt saved successfully");
      return result;
    } catch (error) {
      console.error("Failed to save system prompt:", error);
      handleApiError(error, "save system prompt");
      throw error;
    }
  },

  /**
   * Saves the Claude settings file
   * @param settings - The settings object to save
   * @returns Promise resolving when the settings are saved
   */
  async saveClaudeSettings(settings: ClaudeSettings): Promise<string> {
    try {
      const result = await invoke<string>("save_claude_settings", { settings });
      notifications.success("Settings saved successfully");
      return result;
    } catch (error) {
      console.error("Failed to save Claude settings:", error);
      handleApiError(error, "save Claude settings");
      throw error;
    }
  },

  /**
   * Finds all CLAUDE.md files in a project directory
   * @param projectPath - The absolute path to the project
   * @returns Promise resolving to an array of CLAUDE.md files
   */
  async findClaudeMdFiles(projectPath: string): Promise<ClaudeMdFile[]> {
    try {
      return await invoke<ClaudeMdFile[]>("find_claude_md_files", { projectPath });
    } catch (error) {
      console.error("Failed to find CLAUDE.md files:", error);
      throw error;
    }
  },

  // Theme Management

  /**
   * Gets the current theme configuration
   * @returns Promise resolving to the theme config
   */
  async getThemeConfig(): Promise<ThemeConfig> {
    try {
      return await invoke<ThemeConfig>("get_theme_config");
    } catch (error) {
      console.error("Failed to get theme config:", error);
      throw error;
    }
  },

  /**
   * Sets the theme configuration
   * @param config - The theme configuration to set
   * @returns Promise resolving when the config is saved
   */
  async setThemeConfig(config: ThemeConfig): Promise<void> {
    try {
      return await invoke<void>("set_theme_config", { config });
    } catch (error) {
      console.error("Failed to set theme config:", error);
      throw error;
    }
  },

  /**
   * Gets available color schemes
   * @returns Promise resolving to a map of color schemes
   */
  async getAvailableColorSchemes(): Promise<Record<string, ColorScheme>> {
    try {
      return await invoke<Record<string, ColorScheme>>("get_available_color_schemes");
    } catch (error) {
      console.error("Failed to get color schemes:", error);
      throw error;
    }
  },

  /**
   * Imports a custom theme
   * @param name - The name of the theme
   * @param scheme - The color scheme
   * @returns Promise resolving when the theme is imported
   */
  async importTheme(name: string, scheme: ColorScheme): Promise<void> {
    try {
      return await invoke<void>("import_theme", { name, scheme });
    } catch (error) {
      console.error("Failed to import theme:", error);
      throw error;
    }
  },

  /**
   * Exports a custom theme
   * @param name - The name of the theme to export
   * @returns Promise resolving to the color scheme
   */
  async exportTheme(name: string): Promise<ColorScheme> {
    try {
      return await invoke<ColorScheme>("export_theme", { name });
    } catch (error) {
      console.error("Failed to export theme:", error);
      throw error;
    }
  },

  /**
   * Lists all custom themes
   * @returns Promise resolving to an array of theme names
   */
  async listCustomThemes(): Promise<string[]> {
    try {
      return await invoke<string[]>("list_custom_themes");
    } catch (error) {
      console.error("Failed to list custom themes:", error);
      throw error;
    }
  },

  // Plugin Management

  /**
   * Gets installed plugins
   * @returns Promise resolving to an array of installed plugins
   */
  async getInstalledPlugins(): Promise<Plugin[]> {
    try {
      return await invoke<Plugin[]>("get_installed_plugins");
    } catch (error) {
      console.error("Failed to get installed plugins:", error);
      throw error;
    }
  },

  /**
   * Gets available plugins from the registry
   * @returns Promise resolving to an array of available plugins
   */
  async getAvailablePlugins(): Promise<Plugin[]> {
    try {
      return await invoke<Plugin[]>("get_available_plugins");
    } catch (error) {
      console.error("Failed to get available plugins:", error);
      throw error;
    }
  },

  /**
   * Installs a plugin
   * @param pluginId - The plugin ID to install
   * @returns Promise resolving when the plugin is installed
   */
  async installPlugin(pluginId: string): Promise<void> {
    try {
      await invoke<void>("install_plugin", { pluginId });
      notifications.success(`Plugin ${pluginId} installed successfully`, {
        action: {
          label: "Reload",
          onClick: () => api.reloadPlugins(),
        },
      });
    } catch (error) {
      console.error("Failed to install plugin:", error);
      handleApiError(error, `install plugin ${pluginId}`);
      throw error;
    }
  },

  /**
   * Uninstalls a plugin
   * @param pluginId - The plugin ID to uninstall
   * @returns Promise resolving when the plugin is uninstalled
   */
  async uninstallPlugin(pluginId: string): Promise<void> {
    try {
      return await invoke<void>("uninstall_plugin", { pluginId });
    } catch (error) {
      console.error("Failed to uninstall plugin:", error);
      throw error;
    }
  },

  /**
   * Toggles a plugin on/off
   * @param pluginId - The plugin ID
   * @param enabled - Whether to enable or disable
   * @returns Promise resolving when the plugin state is updated
   */
  async togglePlugin(pluginId: string, enabled: boolean): Promise<void> {
    try {
      return await invoke<void>("toggle_plugin", { pluginId, enabled });
    } catch (error) {
      console.error("Failed to toggle plugin:", error);
      throw error;
    }
  },

  /**
   * Updates plugin settings
   * @param pluginId - The plugin ID
   * @param settings - The new settings
   * @returns Promise resolving when settings are updated
   */
  async updatePluginSettings(pluginId: string, settings: PluginSettings): Promise<void> {
    try {
      return await invoke<void>("update_plugin_settings", { pluginId, settings });
    } catch (error) {
      console.error("Failed to update plugin settings:", error);
      throw error;
    }
  },

  /**
   * Creates a new plugin
   * @param manifest - The plugin manifest
   * @returns Promise resolving when the plugin is created
   */
  async createPlugin(manifest: PluginManifest): Promise<void> {
    try {
      return await invoke<void>("create_plugin", { manifest });
    } catch (error) {
      console.error("Failed to create plugin:", error);
      throw error;
    }
  },

  /**
   * Reloads all plugins
   * @returns Promise resolving when plugins are reloaded
   */
  async reloadPlugins(): Promise<void> {
    try {
      return await invoke<void>("reload_plugins");
    } catch (error) {
      console.error("Failed to reload plugins:", error);
      throw error;
    }
  },

  /**
   * Reads a specific CLAUDE.md file
   * @param filePath - The absolute path to the file
   * @returns Promise resolving to the file content
   */
  async readClaudeMdFile(filePath: string): Promise<string> {
    try {
      return await invoke<string>("read_claude_md_file", { filePath });
    } catch (error) {
      console.error("Failed to read CLAUDE.md file:", error);
      throw error;
    }
  },

  /**
   * Saves a specific CLAUDE.md file
   * @param filePath - The absolute path to the file
   * @param content - The new content for the file
   * @returns Promise resolving when the file is saved
   */
  async saveClaudeMdFile(filePath: string, content: string): Promise<string> {
    try {
      return await invoke<string>("save_claude_md_file", { filePath, content });
    } catch (error) {
      console.error("Failed to save CLAUDE.md file:", error);
      throw error;
    }
  },

  // Agent API methods
  
  /**
   * Lists all CC agents
   * @returns Promise resolving to an array of agents
   */
  async listAgents(): Promise<Agent[]> {
    try {
      return await invoke<Agent[]>('list_agents');
    } catch (error) {
      console.error("Failed to list agents:", error);
      throw error;
    }
  },

  /**
   * Creates a new agent
   * @param name - The agent name
   * @param icon - The icon identifier
   * @param system_prompt - The system prompt for the agent
   * @param default_task - Optional default task
   * @param model - Optional model (defaults to 'sonnet')
   * @returns Promise resolving to the created agent
   */
  async createAgent(
    name: string, 
    icon: string, 
    system_prompt: string, 
    default_task?: string, 
    model?: string
  ): Promise<Agent> {
    try {
      return await invoke<Agent>('create_agent', { 
        name, 
        icon, 
        systemPrompt: system_prompt,
        defaultTask: default_task,
        model
      });
    } catch (error) {
      console.error("Failed to create agent:", error);
      throw error;
    }
  },

  /**
   * Updates an existing agent
   * @param id - The agent ID
   * @param name - The updated name
   * @param icon - The updated icon
   * @param system_prompt - The updated system prompt
   * @param default_task - Optional default task
   * @param model - Optional model
   * @returns Promise resolving to the updated agent
   */
  async updateAgent(
    id: number, 
    name: string, 
    icon: string, 
    system_prompt: string, 
    default_task?: string, 
    model?: string
  ): Promise<Agent> {
    try {
      return await invoke<Agent>('update_agent', { 
        id, 
        name, 
        icon, 
        systemPrompt: system_prompt,
        defaultTask: default_task,
        model
      });
    } catch (error) {
      console.error("Failed to update agent:", error);
      throw error;
    }
  },

  /**
   * Deletes an agent
   * @param id - The agent ID to delete
   * @returns Promise resolving when the agent is deleted
   */
  async deleteAgent(id: number): Promise<void> {
    try {
      return await invoke('delete_agent', { id });
    } catch (error) {
      console.error("Failed to delete agent:", error);
      throw error;
    }
  },

  /**
   * Gets a single agent by ID
   * @param id - The agent ID
   * @returns Promise resolving to the agent
   */
  async getAgent(id: number): Promise<Agent> {
    try {
      return await invoke<Agent>('get_agent', { id });
    } catch (error) {
      console.error("Failed to get agent:", error);
      throw error;
    }
  },

  /**
   * Exports a single agent to JSON format
   * @param id - The agent ID to export
   * @returns Promise resolving to the JSON string
   */
  async exportAgent(id: number): Promise<string> {
    try {
      return await invoke<string>('export_agent', { id });
    } catch (error) {
      console.error("Failed to export agent:", error);
      throw error;
    }
  },

  /**
   * Imports an agent from JSON data
   * @param jsonData - The JSON string containing the agent export
   * @returns Promise resolving to the imported agent
   */
  async importAgent(jsonData: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_agent', { jsonData });
    } catch (error) {
      console.error("Failed to import agent:", error);
      throw error;
    }
  },

  /**
   * Imports an agent from a file
   * @param filePath - The path to the JSON file
   * @returns Promise resolving to the imported agent
   */
  async importAgentFromFile(filePath: string): Promise<Agent> {
    try {
      return await invoke<Agent>('import_agent_from_file', { filePath });
    } catch (error) {
      console.error("Failed to import agent from file:", error);
      throw error;
    }
  },

  /**
   * Executes an agent
   * @param agentId - The agent ID to execute
   * @param projectPath - The project path to run the agent in
   * @param task - The task description
   * @param model - Optional model override
   * @returns Promise resolving to the run ID when execution starts
   */
  async executeAgent(agentId: number, projectPath: string, task: string, model?: string): Promise<number> {
    try {
      return await invoke<number>('execute_agent', { agentId, projectPath, task, model });
    } catch (error) {
      console.error("Failed to execute agent:", error);
      // Return a sentinel value to indicate error
      throw new Error(`Failed to execute agent: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Lists agent runs with metrics
   * @param agentId - Optional agent ID to filter runs
   * @returns Promise resolving to an array of agent runs with metrics
   */
  async listAgentRuns(agentId?: number): Promise<AgentRunWithMetrics[]> {
    try {
      return await invoke<AgentRunWithMetrics[]>('list_agent_runs', { agentId });
    } catch (error) {
      console.error("Failed to list agent runs:", error);
      // Return empty array instead of throwing to prevent UI crashes
      return [];
    }
  },

  /**
   * Gets a single agent run by ID with metrics
   * @param id - The run ID
   * @returns Promise resolving to the agent run with metrics
   */
  async getAgentRun(id: number): Promise<AgentRunWithMetrics> {
    try {
      return await invoke<AgentRunWithMetrics>('get_agent_run', { id });
    } catch (error) {
      console.error("Failed to get agent run:", error);
      throw new Error(`Failed to get agent run: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Gets a single agent run by ID with real-time metrics from JSONL
   * @param id - The run ID
   * @returns Promise resolving to the agent run with metrics
   */
  async getAgentRunWithRealTimeMetrics(id: number): Promise<AgentRunWithMetrics> {
    try {
      return await invoke<AgentRunWithMetrics>('get_agent_run_with_real_time_metrics', { id });
    } catch (error) {
      console.error("Failed to get agent run with real-time metrics:", error);
      throw new Error(`Failed to get agent run with real-time metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Lists all currently running agent sessions
   * @returns Promise resolving to list of running agent sessions
   */
  async listRunningAgentSessions(): Promise<AgentRun[]> {
    try {
      return await invoke<AgentRun[]>('list_running_sessions');
    } catch (error) {
      console.error("Failed to list running agent sessions:", error);
      throw new Error(`Failed to list running agent sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Kills a running agent session
   * @param runId - The run ID to kill
   * @returns Promise resolving to whether the session was successfully killed
   */
  async killAgentSession(runId: number): Promise<boolean> {
    try {
      return await invoke<boolean>('kill_agent_session', { runId });
    } catch (error) {
      console.error("Failed to kill agent session:", error);
      throw new Error(`Failed to kill agent session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Gets the status of a specific agent session
   * @param runId - The run ID to check
   * @returns Promise resolving to the session status or null if not found
   */
  async getSessionStatus(runId: number): Promise<string | null> {
    try {
      return await invoke<string | null>('get_session_status', { runId });
    } catch (error) {
      console.error("Failed to get session status:", error);
      throw new Error(`Failed to get session status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Cleanup finished processes and update their status
   * @returns Promise resolving to list of run IDs that were cleaned up
   */
  async cleanupFinishedProcesses(): Promise<number[]> {
    try {
      return await invoke<number[]>('cleanup_finished_processes');
    } catch (error) {
      console.error("Failed to cleanup finished processes:", error);
      throw new Error(`Failed to cleanup finished processes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Get real-time output for a running session (with live output fallback)
   * @param runId - The run ID to get output for
   * @returns Promise resolving to the current session output (JSONL format)
   */
  async getSessionOutput(runId: number): Promise<string> {
    try {
      return await invoke<string>('get_session_output', { runId });
    } catch (error) {
      console.error("Failed to get session output:", error);
      throw new Error(`Failed to get session output: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Get live output directly from process stdout buffer
   * @param runId - The run ID to get live output for
   * @returns Promise resolving to the current live output
   */
  async getLiveSessionOutput(runId: number): Promise<string> {
    try {
      return await invoke<string>('get_live_session_output', { runId });
    } catch (error) {
      console.error("Failed to get live session output:", error);
      throw new Error(`Failed to get live session output: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Start streaming real-time output for a running session
   * @param runId - The run ID to stream output for
   * @returns Promise that resolves when streaming starts
   */
  async streamSessionOutput(runId: number): Promise<void> {
    try {
      return await invoke<void>('stream_session_output', { runId });
    } catch (error) {
      console.error("Failed to start streaming session output:", error);
      throw new Error(`Failed to start streaming session output: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },

  /**
   * Loads the JSONL history for a specific session
   */
  async loadSessionHistory(sessionId: string, projectId: string): Promise<any[]> {
    return invoke("load_session_history", { sessionId, projectId });
  },

  /**
   * Loads the JSONL history for a specific agent session
   * Similar to loadSessionHistory but searches across all project directories
   * @param sessionId - The session ID (UUID)
   * @returns Promise resolving to array of session messages
   */
  async loadAgentSessionHistory(sessionId: string): Promise<any[]> {
    try {
      return await invoke<any[]>('load_agent_session_history', { sessionId });
    } catch (error) {
      console.error("Failed to load agent session history:", error);
      throw error;
    }
  },

  /**
   * Executes a new interactive Claude Code session with streaming output
   */
  async executeClaudeCode(projectPath: string, prompt: string, model: string, superClaudeSettings?: SuperClaudeSettings): Promise<void> {
    return invoke("execute_claude_code", { projectPath, prompt, model, superClaudeSettings });
  },

  /**
   * Continues an existing Claude Code conversation with streaming output
   */
  async continueClaudeCode(projectPath: string, prompt: string, model: string, superClaudeSettings?: SuperClaudeSettings): Promise<void> {
    return invoke("continue_claude_code", { projectPath, prompt, model, superClaudeSettings });
  },

  /**
   * Resumes an existing Claude Code session by ID with streaming output
   */
  async resumeClaudeCode(projectPath: string, sessionId: string, prompt: string, model: string, superClaudeSettings?: SuperClaudeSettings): Promise<void> {
    return invoke("resume_claude_code", { projectPath, sessionId, prompt, model, superClaudeSettings });
  },

  /**
   * Cancels the currently running Claude Code execution
   * @param sessionId - Optional session ID to cancel a specific session
   */
  async cancelClaudeExecution(sessionId?: string): Promise<void> {
    return invoke("cancel_claude_execution", { sessionId });
  },

  /**
   * Lists all currently running Claude sessions
   * @returns Promise resolving to list of running Claude sessions
   */
  async listRunningClaudeSessions(): Promise<any[]> {
    return invoke("list_running_claude_sessions");
  },

  /**
   * Gets live output from a Claude session
   * @param sessionId - The session ID to get output for
   * @returns Promise resolving to the current live output
   */
  async getClaudeSessionOutput(sessionId: string): Promise<string> {
    return invoke("get_claude_session_output", { sessionId });
  },

  /**
   * Lists files and directories in a given path
   */
  async listDirectoryContents(directoryPath: string): Promise<FileEntry[]> {
    return invoke("list_directory_contents", { directoryPath });
  },

  /**
   * Searches for files and directories matching a pattern
   */
  async searchFiles(basePath: string, query: string): Promise<FileEntry[]> {
    return invoke("search_files", { basePath, query });
  },

  /**
   * Gets overall usage statistics
   * @returns Promise resolving to usage statistics
   */
  async getUsageStats(): Promise<UsageStats> {
    try {
      return await invoke<UsageStats>("get_usage_stats");
    } catch (error) {
      console.error("Failed to get usage stats:", error);
      throw error;
    }
  },

  /**
   * Gets usage statistics filtered by date range
   * @param startDate - Start date (ISO format)
   * @param endDate - End date (ISO format)
   * @returns Promise resolving to usage statistics
   */
  async getUsageByDateRange(startDate: string, endDate: string): Promise<UsageStats> {
    try {
      return await invoke<UsageStats>("get_usage_by_date_range", { startDate, endDate });
    } catch (error) {
      console.error("Failed to get usage by date range:", error);
      throw error;
    }
  },

  /**
   * Gets usage statistics grouped by session
   * @param since - Optional start date (YYYYMMDD)
   * @param until - Optional end date (YYYYMMDD)
   * @param order - Optional sort order ('asc' or 'desc')
   * @returns Promise resolving to an array of session usage data
   */
  async getSessionStats(
    since?: string,
    until?: string,
    order?: "asc" | "desc"
  ): Promise<ProjectUsage[]> {
    try {
      return await invoke<ProjectUsage[]>("get_session_stats", {
        since,
        until,
        order,
      });
    } catch (error) {
      console.error("Failed to get session stats:", error);
      throw error;
    }
  },

  /**
   * Gets detailed usage entries with optional filtering
   * @param limit - Optional limit for number of entries
   * @returns Promise resolving to array of usage entries
   */
  async getUsageDetails(limit?: number): Promise<UsageEntry[]> {
    try {
      return await invoke<UsageEntry[]>("get_usage_details", { limit });
    } catch (error) {
      console.error("Failed to get usage details:", error);
      throw error;
    }
  },

  /**
   * Creates a checkpoint for the current session state
   */
  async createCheckpoint(
    sessionId: string,
    projectId: string,
    projectPath: string,
    messageIndex?: number,
    description?: string
  ): Promise<CheckpointResult> {
    return invoke("create_checkpoint", {
      sessionId,
      projectId,
      projectPath,
      messageIndex,
      description
    });
  },

  /**
   * Restores a session to a specific checkpoint
   */
  async restoreCheckpoint(
    checkpointId: string,
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<CheckpointResult> {
    return invoke("restore_checkpoint", {
      checkpointId,
      sessionId,
      projectId,
      projectPath
    });
  },

  /**
   * Lists all checkpoints for a session
   */
  async listCheckpoints(
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<Checkpoint[]> {
    return invoke("list_checkpoints", {
      sessionId,
      projectId,
      projectPath
    });
  },

  /**
   * Forks a new timeline branch from a checkpoint
   */
  async forkFromCheckpoint(
    checkpointId: string,
    sessionId: string,
    projectId: string,
    projectPath: string,
    newSessionId: string,
    description?: string
  ): Promise<CheckpointResult> {
    return invoke("fork_from_checkpoint", {
      checkpointId,
      sessionId,
      projectId,
      projectPath,
      newSessionId,
      description
    });
  },

  /**
   * Gets the timeline for a session
   */
  async getSessionTimeline(
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<SessionTimeline> {
    return invoke("get_session_timeline", {
      sessionId,
      projectId,
      projectPath
    });
  },

  /**
   * Updates checkpoint settings for a session
   */
  async updateCheckpointSettings(
    sessionId: string,
    projectId: string,
    projectPath: string,
    autoCheckpointEnabled: boolean,
    checkpointStrategy: CheckpointStrategy
  ): Promise<void> {
    return invoke("update_checkpoint_settings", {
      sessionId,
      projectId,
      projectPath,
      autoCheckpointEnabled,
      checkpointStrategy
    });
  },

  /**
   * Gets diff between two checkpoints
   */
  async getCheckpointDiff(
    fromCheckpointId: string,
    toCheckpointId: string,
    sessionId: string,
    projectId: string
  ): Promise<CheckpointDiff> {
    try {
      return await invoke<CheckpointDiff>("get_checkpoint_diff", {
        fromCheckpointId,
        toCheckpointId,
        sessionId,
        projectId
      });
    } catch (error) {
      console.error("Failed to get checkpoint diff:", error);
      throw error;
    }
  },

  /**
   * Tracks a message for checkpointing
   */
  async trackCheckpointMessage(
    sessionId: string,
    projectId: string,
    projectPath: string,
    message: string
  ): Promise<void> {
    try {
      await invoke("track_checkpoint_message", {
        sessionId,
        projectId,
        projectPath,
        message
      });
    } catch (error) {
      console.error("Failed to track checkpoint message:", error);
      throw error;
    }
  },

  /**
   * Checks if auto-checkpoint should be triggered
   */
  async checkAutoCheckpoint(
    sessionId: string,
    projectId: string,
    projectPath: string,
    message: string
  ): Promise<boolean> {
    try {
      return await invoke<boolean>("check_auto_checkpoint", {
        sessionId,
        projectId,
        projectPath,
        message
      });
    } catch (error) {
      console.error("Failed to check auto checkpoint:", error);
      throw error;
    }
  },

  /**
   * Triggers cleanup of old checkpoints
   */
  async cleanupOldCheckpoints(
    sessionId: string,
    projectId: string,
    projectPath: string,
    keepCount: number
  ): Promise<number> {
    try {
      return await invoke<number>("cleanup_old_checkpoints", {
        sessionId,
        projectId,
        projectPath,
        keepCount
      });
    } catch (error) {
      console.error("Failed to cleanup old checkpoints:", error);
      throw error;
    }
  },

  /**
   * Gets checkpoint settings for a session
   */
  async getCheckpointSettings(
    sessionId: string,
    projectId: string,
    projectPath: string
  ): Promise<{
    auto_checkpoint_enabled: boolean;
    checkpoint_strategy: CheckpointStrategy;
    total_checkpoints: number;
    current_checkpoint_id?: string;
  }> {
    try {
      return await invoke("get_checkpoint_settings", {
        sessionId,
        projectId,
        projectPath
      });
    } catch (error) {
      console.error("Failed to get checkpoint settings:", error);
      throw error;
    }
  },

  /**
   * Clears checkpoint manager for a session (cleanup on session end)
   */
  async clearCheckpointManager(sessionId: string): Promise<void> {
    try {
      await invoke("clear_checkpoint_manager", { sessionId });
    } catch (error) {
      console.error("Failed to clear checkpoint manager:", error);
      throw error;
    }
  },

  /**
   * Tracks a batch of messages for a session for checkpointing
   */
  trackSessionMessages: (
    sessionId: string, 
    projectId: string, 
    projectPath: string, 
    messages: string[]
  ): Promise<void> =>
    invoke("track_session_messages", { sessionId, projectId, projectPath, messages }),

  /**
   * Adds a new MCP server
   */
  async mcpAdd(
    name: string,
    transport: string,
    command?: string,
    args: string[] = [],
    env: Record<string, string> = {},
    url?: string,
    scope: string = "local"
  ): Promise<AddServerResult> {
    try {
      return await invoke<AddServerResult>("mcp_add", {
        name,
        transport,
        command,
        args,
        env,
        url,
        scope
      });
    } catch (error) {
      console.error("Failed to add MCP server:", error);
      throw error;
    }
  },

  /**
   * Lists all configured MCP servers
   */
  async mcpList(): Promise<MCPServer[]> {
    try {
      console.log("API: Calling mcp_list...");
      const result = await invoke<MCPServer[]>("mcp_list");
      console.log("API: mcp_list returned:", result);
      return result;
    } catch (error) {
      console.error("API: Failed to list MCP servers:", error);
      throw error;
    }
  },

  /**
   * Gets details for a specific MCP server
   */
  async mcpGet(name: string): Promise<MCPServer> {
    try {
      return await invoke<MCPServer>("mcp_get", { name });
    } catch (error) {
      console.error("Failed to get MCP server:", error);
      throw error;
    }
  },

  /**
   * Removes an MCP server
   */
  async mcpRemove(name: string): Promise<string> {
    try {
      return await invoke<string>("mcp_remove", { name });
    } catch (error) {
      console.error("Failed to remove MCP server:", error);
      throw error;
    }
  },

  /**
   * Adds an MCP server from JSON configuration
   */
  async mcpAddJson(name: string, jsonConfig: string, scope: string = "local"): Promise<AddServerResult> {
    try {
      return await invoke<AddServerResult>("mcp_add_json", { name, jsonConfig, scope });
    } catch (error) {
      console.error("Failed to add MCP server from JSON:", error);
      throw error;
    }
  },

  /**
   * Imports MCP servers from Claude Desktop
   */
  async mcpAddFromClaudeDesktop(scope: string = "local"): Promise<ImportResult> {
    try {
      return await invoke<ImportResult>("mcp_add_from_claude_desktop", { scope });
    } catch (error) {
      console.error("Failed to import from Claude Desktop:", error);
      throw error;
    }
  },

  /**
   * Starts Claude Code as an MCP server
   */
  async mcpServe(): Promise<string> {
    try {
      return await invoke<string>("mcp_serve");
    } catch (error) {
      console.error("Failed to start MCP server:", error);
      throw error;
    }
  },

  /**
   * Tests connection to an MCP server
   */
  async mcpTestConnection(name: string): Promise<string> {
    try {
      return await invoke<string>("mcp_test_connection", { name });
    } catch (error) {
      console.error("Failed to test MCP connection:", error);
      throw error;
    }
  },

  /**
   * Resets project-scoped server approval choices
   */
  async mcpResetProjectChoices(): Promise<string> {
    try {
      return await invoke<string>("mcp_reset_project_choices");
    } catch (error) {
      console.error("Failed to reset project choices:", error);
      throw error;
    }
  },

  /**
   * Gets the status of MCP servers
   */
  async mcpGetServerStatus(): Promise<Record<string, ServerStatus>> {
    try {
      return await invoke<Record<string, ServerStatus>>("mcp_get_server_status");
    } catch (error) {
      console.error("Failed to get server status:", error);
      throw error;
    }
  },

  /**
   * Refreshes the status of MCP servers (clears cache)
   */
  async mcpRefreshServerStatus(): Promise<Record<string, ServerStatus>> {
    try {
      return await invoke<Record<string, ServerStatus>>("mcp_refresh_server_status");
    } catch (error) {
      console.error("Failed to refresh server status:", error);
      throw error;
    }
  },

  /**
   * Reads .mcp.json from the current project
   */
  async mcpReadProjectConfig(projectPath: string): Promise<MCPProjectConfig> {
    try {
      return await invoke<MCPProjectConfig>("mcp_read_project_config", { projectPath });
    } catch (error) {
      console.error("Failed to read project MCP config:", error);
      throw error;
    }
  },

  /**
   * Saves .mcp.json to the current project
   */
  async mcpSaveProjectConfig(projectPath: string, config: MCPProjectConfig): Promise<string> {
    try {
      return await invoke<string>("mcp_save_project_config", { projectPath, config });
    } catch (error) {
      console.error("Failed to save project MCP config:", error);
      throw error;
    }
  },

  /**
   * Get the stored Claude binary path from settings
   * @returns Promise resolving to the path if set, null otherwise
   */
  async getClaudeBinaryPath(): Promise<string | null> {
    try {
      return await invoke<string | null>("get_claude_binary_path");
    } catch (error) {
      console.error("Failed to get Claude binary path:", error);
      throw error;
    }
  },

  /**
   * Set the Claude binary path in settings
   * @param path - The absolute path to the Claude binary
   * @returns Promise resolving when the path is saved
   */
  async setClaudeBinaryPath(path: string): Promise<void> {
    try {
      return await invoke<void>("set_claude_binary_path", { path });
    } catch (error) {
      console.error("Failed to set Claude binary path:", error);
      throw error;
    }
  },

  /**
   * List all available Claude installations on the system
   * @returns Promise resolving to an array of Claude installations
   */
  async listClaudeInstallations(): Promise<ClaudeInstallation[]> {
    try {
      return await invoke<ClaudeInstallation[]>("list_claude_installations");
    } catch (error) {
      console.error("Failed to list Claude installations:", error);
      throw error;
    }
  },

  // Storage API methods

  /**
   * Lists all tables in the SQLite database
   * @returns Promise resolving to an array of table information
   */
  async storageListTables(): Promise<any[]> {
    try {
      return await invoke<any[]>("storage_list_tables");
    } catch (error) {
      console.error("Failed to list tables:", error);
      throw error;
    }
  },

  /**
   * Reads table data with pagination
   * @param tableName - Name of the table to read
   * @param page - Page number (1-indexed)
   * @param pageSize - Number of rows per page
   * @param searchQuery - Optional search query
   * @returns Promise resolving to table data with pagination info
   */
  async storageReadTable(
    tableName: string,
    page: number,
    pageSize: number,
    searchQuery?: string
  ): Promise<any> {
    try {
      return await invoke<any>("storage_read_table", {
        tableName,
        page,
        pageSize,
        searchQuery,
      });
    } catch (error) {
      console.error("Failed to read table:", error);
      throw error;
    }
  },

  /**
   * Updates a row in a table
   * @param tableName - Name of the table
   * @param primaryKeyValues - Map of primary key column names to values
   * @param updates - Map of column names to new values
   * @returns Promise resolving when the row is updated
   */
  async storageUpdateRow(
    tableName: string,
    primaryKeyValues: Record<string, any>,
    updates: Record<string, any>
  ): Promise<void> {
    try {
      return await invoke<void>("storage_update_row", {
        tableName,
        primaryKeyValues,
        updates,
      });
    } catch (error) {
      console.error("Failed to update row:", error);
      throw error;
    }
  },

  /**
   * Deletes a row from a table
   * @param tableName - Name of the table
   * @param primaryKeyValues - Map of primary key column names to values
   * @returns Promise resolving when the row is deleted
   */
  async storageDeleteRow(
    tableName: string,
    primaryKeyValues: Record<string, any>
  ): Promise<void> {
    try {
      return await invoke<void>("storage_delete_row", {
        tableName,
        primaryKeyValues,
      });
    } catch (error) {
      console.error("Failed to delete row:", error);
      throw error;
    }
  },

  /**
   * Inserts a new row into a table
   * @param tableName - Name of the table
   * @param values - Map of column names to values
   * @returns Promise resolving to the last insert row ID
   */
  async storageInsertRow(
    tableName: string,
    values: Record<string, any>
  ): Promise<number> {
    try {
      return await invoke<number>("storage_insert_row", {
        tableName,
        values,
      });
    } catch (error) {
      console.error("Failed to insert row:", error);
      throw error;
    }
  },

  /**
   * Executes a raw SQL query
   * @param query - SQL query string
   * @returns Promise resolving to query result
   */
  async storageExecuteSql(query: string): Promise<any> {
    try {
      return await invoke<any>("storage_execute_sql", { query });
    } catch (error) {
      console.error("Failed to execute SQL:", error);
      throw error;
    }
  },

  /**
   * Resets the entire database
   * @returns Promise resolving when the database is reset
   */
  async storageResetDatabase(): Promise<void> {
    try {
      return await invoke<void>("storage_reset_database");
    } catch (error) {
      console.error("Failed to reset database:", error);
      throw error;
    }
  },

  /**
   * Runs AI-powered code review on the current session
   * @param sessionId - The session ID to review
   * @param customRules - Optional custom review rules
   * @returns Promise resolving to code review results
   */
  async runCodeReview(sessionId: string, customRules: string = ""): Promise<CodeReviewResult[]> {
    try {
      return await invoke<CodeReviewResult[]>("run_code_review", {
        sessionId,
        customRules
      });
    } catch (error) {
      console.error("Failed to run code review:", error);
      throw error;
    }
  },

  /**
   * Gets code review history for a session
   * @param sessionId - The session ID
   * @returns Promise resolving to review history
   */
  async getReviewHistory(sessionId: string): Promise<CodeReviewResult[]> {
    try {
      return await invoke<CodeReviewResult[]>("get_review_history", {
        sessionId
      });
    } catch (error) {
      console.error("Failed to get review history:", error);
      throw error;
    }
  },

  /**
   * Exports code review report in specified format
   * @param sessionId - The session ID
   * @param format - Export format (markdown, html, pdf)
   * @returns Promise resolving to exported report content
   */
  async exportReviewReport(sessionId: string, format: string): Promise<string> {
    try {
      return await invoke<string>("export_review_report", {
        sessionId,
        format
      });
    } catch (error) {
      console.error("Failed to export review report:", error);
      throw error;
    }
  },

  /**
   * Starts a collaborative session
   * @param sessionId - The session ID
   * @param projectPath - The project path
   * @param useLocalBackend - Whether to use local backend URLs
   * @param localWsUrl - Custom local WebSocket URL
   * @param localShareUrl - Custom local share URL
   * @returns Promise resolving to collaborative session details
   */
  async startCollaborativeSession(
    sessionId: string, 
    projectPath: string,
    useLocalBackend?: boolean,
    localWsUrl?: string,
    localShareUrl?: string
  ): Promise<CollaborativeSession> {
    try {
      return await invoke<CollaborativeSession>("start_collaborative_session", {
        sessionId,
        projectPath,
        useLocalBackend,
        localWsUrl,
        localShareUrl
      });
    } catch (error) {
      console.error("Failed to start collaborative session:", error);
      throw error;
    }
  },

  /**
   * Stops a collaborative session
   * @param sessionId - The session ID
   * @returns Promise resolving when session is stopped
   */
  async stopCollaborativeSession(sessionId: string): Promise<void> {
    try {
      return await invoke<void>("stop_collaborative_session", { sessionId });
    } catch (error) {
      console.error("Failed to stop collaborative session:", error);
      throw error;
    }
  },

  /**
   * Invites a collaborator to the session
   * @param sessionId - The session ID
   * @param email - Collaborator's email
   * @param role - Collaborator's role
   * @returns Promise resolving when invitation is sent
   */
  async inviteCollaborator(sessionId: string, email: string, role: 'editor' | 'viewer'): Promise<void> {
    try {
      return await invoke<void>("invite_collaborator", {
        sessionId,
        email,
        role
      });
    } catch (error) {
      console.error("Failed to invite collaborator:", error);
      throw error;
    }
  },

  /**
   * Removes a collaborator from the session
   * @param sessionId - The session ID
   * @param collaboratorId - The collaborator ID to remove
   * @returns Promise resolving when collaborator is removed
   */
  async removeCollaborator(sessionId: string, collaboratorId: string): Promise<void> {
    try {
      return await invoke<void>("remove_collaborator", {
        sessionId,
        collaboratorId
      });
    } catch (error) {
      console.error("Failed to remove collaborator:", error);
      throw error;
    }
  },

  /**
   * Updates cursor position for collaborative editing
   * @param sessionId - The session ID
   * @param userId - The user ID
   * @param line - Line number
   * @param column - Column number
   * @param file - File path
   * @returns Promise resolving when position is updated
   */
  async updateCursorPosition(sessionId: string, userId: string, line: number, column: number, file: string): Promise<void> {
    try {
      return await invoke<void>("update_cursor_position", {
        sessionId,
        userId,
        line,
        column,
        file
      });
    } catch (error) {
      console.error("Failed to update cursor position:", error);
      throw error;
    }
  },

  /**
   * Gets collaborators in a session
   * @param sessionId - The session ID
   * @returns Promise resolving to list of collaborators
   */
  async getSessionCollaborators(sessionId: string): Promise<Collaborator[]> {
    try {
      return await invoke<Collaborator[]>("get_session_collaborators", { sessionId });
    } catch (error) {
      console.error("Failed to get session collaborators:", error);
      throw error;
    }
  },

  /**
   * Joins a collaborative session
   * @param sessionCode - The session code
   * @param userName - User's name
   * @param userEmail - User's email
   * @returns Promise resolving to session details
   */
  async joinCollaborativeSession(sessionCode: string, userName: string, userEmail: string): Promise<CollaborativeSession> {
    try {
      return await invoke<CollaborativeSession>("join_collaborative_session", {
        sessionCode,
        userName,
        userEmail
      });
    } catch (error) {
      console.error("Failed to join collaborative session:", error);
      throw error;
    }
  },

  /**
   * Gets user permissions for a session
   * @param sessionId - The session ID
   * @param userId - The user ID
   * @returns Promise resolving to permissions
   */
  async getSessionPermissions(sessionId: string, userId: string): Promise<SessionPermissions> {
    try {
      return await invoke<SessionPermissions>("get_session_permissions", {
        sessionId,
        userId
      });
    } catch (error) {
      console.error("Failed to get session permissions:", error);
      throw error;
    }
  },

  /**
   * Gets all smart templates
   * @returns Promise resolving to list of templates
   */
  async getSmartTemplates(): Promise<Template[]> {
    try {
      return await invoke<Template[]>("get_smart_templates");
    } catch (error) {
      console.error("Failed to get smart templates:", error);
      throw error;
    }
  },

  /**
   * Creates a new smart template
   * @param template - Template data
   * @returns Promise resolving to created template
   */
  async createSmartTemplate(template: Omit<Template, 'id'>): Promise<Template> {
    try {
      return await invoke<Template>("create_smart_template", {
        templateData: template
      });
    } catch (error) {
      console.error("Failed to create smart template:", error);
      throw error;
    }
  },

  /**
   * Updates an existing smart template
   * @param templateId - Template ID
   * @param template - Updated template data
   * @returns Promise resolving to updated template
   */
  async updateSmartTemplate(templateId: string, template: Partial<Template>): Promise<Template> {
    try {
      return await invoke<Template>("update_smart_template", {
        templateId,
        templateData: template
      });
    } catch (error) {
      console.error("Failed to update smart template:", error);
      throw error;
    }
  },

  /**
   * Deletes a smart template
   * @param templateId - Template ID to delete
   * @returns Promise resolving when deleted
   */
  async deleteSmartTemplate(templateId: string): Promise<void> {
    try {
      return await invoke<void>("delete_smart_template", { templateId });
    } catch (error) {
      console.error("Failed to delete smart template:", error);
      throw error;
    }
  },

  /**
   * Toggles template favorite status
   * @param templateId - Template ID
   * @param isFavorite - New favorite status
   * @returns Promise resolving to updated template
   */
  async toggleTemplateFavorite(templateId: string, isFavorite: boolean): Promise<Template> {
    try {
      return await invoke<Template>("toggle_template_favorite", {
        templateId,
        isFavorite
      });
    } catch (error) {
      console.error("Failed to toggle template favorite:", error);
      throw error;
    }
  },

  /**
   * Tracks template usage
   * @param templateId - Template ID
   * @returns Promise resolving when tracked
   */
  async trackTemplateUsage(templateId: string): Promise<void> {
    try {
      return await invoke<void>("track_template_usage", { templateId });
    } catch (error) {
      console.error("Failed to track template usage:", error);
      throw error;
    }
  },

  /**
   * Imports a template from JSON
   * @param templateJson - Template JSON string
   * @returns Promise resolving to imported template
   */
  async importTemplate(templateJson: string): Promise<Template> {
    try {
      return await invoke<Template>("import_template", { templateJson });
    } catch (error) {
      console.error("Failed to import template:", error);
      throw error;
    }
  },

  /**
   * Exports a template to JSON
   * @param templateId - Template ID to export
   * @returns Promise resolving to JSON string
   */
  async exportTemplate(templateId: string): Promise<string> {
    try {
      return await invoke<string>("export_template", { templateId });
    } catch (error) {
      console.error("Failed to export template:", error);
      throw error;
    }
  },

  /**
   * Gets template usage statistics
   * @returns Promise resolving to usage stats
   */
  async getTemplateUsageStats(): Promise<Record<string, number>> {
    try {
      return await invoke<Record<string, number>>("get_template_usage_stats");
    } catch (error) {
      console.error("Failed to get template usage stats:", error);
      throw error;
    }
  },

  /**
   * Gets code flow visualization data
   * @param sessionId - The session ID
   * @param projectPath - The project path
   * @returns Promise resolving to code flow data
   */
  async getCodeFlowData(sessionId: string, projectPath: string): Promise<CodeFlowData> {
    try {
      return await invoke<CodeFlowData>("get_code_flow_data", {
        sessionId,
        projectPath
      });
    } catch (error) {
      console.error("Failed to get code flow data:", error);
      throw error;
    }
  },

  /**
   * Gets performance metrics for a session
   * @param sessionId - The session ID
   * @param timeRange - Time range filter
   * @returns Promise resolving to performance data
   */
  async getPerformanceMetrics(sessionId: string, timeRange: string): Promise<PerformanceData> {
    try {
      return await invoke<PerformanceData>("get_performance_metrics", {
        sessionId,
        timeRange
      });
    } catch (error) {
      console.error("Failed to get performance metrics:", error);
      throw error;
    }
  },

  /**
   * Records a performance metric
   * @param sessionId - The session ID
   * @param metric - The metric to record
   * @returns Promise resolving when recorded
   */
  async recordPerformanceMetric(sessionId: string, metric: PerformanceMetrics): Promise<void> {
    try {
      return await invoke<void>("record_performance_metric", {
        sessionId,
        metric
      });
    } catch (error) {
      console.error("Failed to record performance metric:", error);
      throw error;
    }
  },

  /**
   * Clears performance metrics for a session
   * @param sessionId - The session ID
   * @returns Promise resolving when cleared
   */
  async clearPerformanceMetrics(sessionId: string): Promise<void> {
    try {
      return await invoke<void>("clear_performance_metrics", { sessionId });
    } catch (error) {
      console.error("Failed to clear performance metrics:", error);
      throw error;
    }
  },

  /**
   * Exports performance report
   * @param sessionId - The session ID
   * @returns Promise resolving to report JSON
   */
  async exportPerformanceReport(sessionId: string): Promise<string> {
    try {
      return await invoke<string>("export_performance_report", { sessionId });
    } catch (error) {
      console.error("Failed to export performance report:", error);
      throw error;
    }
  },

  // Theme and plugin methods defined above

  /**
   * Gets all agents for a session
   * @param sessionId - The session ID
   * @returns Promise resolving to agent list
   */
  async getAgents(sessionId: string): Promise<EnhancedAgent[]> {
    try {
      // Mock implementation - replace with real backend call
      const mockAgents = localStorage.getItem(`agents_${sessionId}`);
      try {
        return mockAgents && mockAgents.trim() ? JSON.parse(mockAgents) : [];
      } catch (error) {
        console.error('Failed to parse mock agents:', error);
        return [];
      }
    } catch (error) {
      console.error("Failed to get agents:", error);
      return [];
    }
  },

  /**
   * Saves agents for a session
   * @param sessionId - The session ID
   * @param agents - The agents to save
   * @returns Promise resolving when saved
   */
  async saveAgents(sessionId: string, agents: EnhancedAgent[]): Promise<void> {
    try {
      // Mock implementation - replace with real backend call
      try {
        localStorage.setItem(`agents_${sessionId}`, JSON.stringify(agents));
      } catch (error) {
        console.error('Failed to save agents:', error);
      }
    } catch (error) {
      console.error("Failed to save agents:", error);
      throw error;
    }
  },

  /**
   * Creates a new enhanced agent
   * @param sessionId - The session ID
   * @param agent - The agent to create
   * @returns Promise resolving to created agent
   */
  async createEnhancedAgent(sessionId: string, agent: Omit<EnhancedAgent, 'id' | 'createdAt' | 'lastUsed'>): Promise<EnhancedAgent> {
    try {
      const newAgent: EnhancedAgent = {
        ...agent,
        id: `agent-${Date.now()}`,
        createdAt: new Date(),
        lastUsed: new Date()
      };
      
      const agents = await this.getAgents(sessionId);
      agents.push(newAgent);
      await this.saveAgents(sessionId, agents);
      
      return newAgent;
    } catch (error) {
      console.error("Failed to create agent:", error);
      throw error;
    }
  },

  /**
   * Updates an existing enhanced agent
   * @param sessionId - The session ID
   * @param agentId - The agent ID
   * @param updates - The updates to apply
   * @returns Promise resolving to updated agent
   */
  async updateEnhancedAgent(sessionId: string, agentId: string, updates: Partial<EnhancedAgent>): Promise<EnhancedAgent> {
    try {
      const agents = await this.getAgents(sessionId);
      const agentIndex = agents.findIndex(a => a.id === agentId);
      
      if (agentIndex === -1) {
        throw new Error(`Agent ${agentId} not found`);
      }
      
      agents[agentIndex] = { ...agents[agentIndex], ...updates, lastUsed: new Date() };
      await this.saveAgents(sessionId, agents);
      
      return agents[agentIndex];
    } catch (error) {
      console.error("Failed to update agent:", error);
      throw error;
    }
  },

  /**
   * Deletes an enhanced agent
   * @param sessionId - The session ID
   * @param agentId - The agent ID
   * @returns Promise resolving when deleted
   */
  async deleteEnhancedAgent(sessionId: string, agentId: string): Promise<void> {
    try {
      const agents = await this.getAgents(sessionId);
      const filteredAgents = agents.filter(a => a.id !== agentId);
      await this.saveAgents(sessionId, filteredAgents);
    } catch (error) {
      console.error("Failed to delete agent:", error);
      throw error;
    }
  },

  /**
   * Gets agent tasks for a session
   * @param sessionId - The session ID
   * @returns Promise resolving to task list
   */
  async getAgentTasks(sessionId: string): Promise<EnhancedAgentTask[]> {
    try {
      // Mock implementation - replace with real backend call
      const mockTasks = localStorage.getItem(`agent_tasks_${sessionId}`);
      try {
        return mockTasks && mockTasks.trim() ? JSON.parse(mockTasks) : [];
      } catch (error) {
        console.error('Failed to parse mock tasks:', error);
        return [];
      }
    } catch (error) {
      console.error("Failed to get agent tasks:", error);
      return [];
    }
  },

  /**
   * Saves agent tasks for a session
   * @param sessionId - The session ID
   * @param tasks - The tasks to save
   * @returns Promise resolving when saved
   */
  async saveAgentTasks(sessionId: string, tasks: EnhancedAgentTask[]): Promise<void> {
    try {
      // Mock implementation - replace with real backend call
      try {
        localStorage.setItem(`agent_tasks_${sessionId}`, JSON.stringify(tasks));
      } catch (error) {
        console.error('Failed to save tasks:', error);
      }
    } catch (error) {
      console.error("Failed to save agent tasks:", error);
      throw error;
    }
  },

  /**
   * Creates a new agent task
   * @param sessionId - The session ID
   * @param task - The task to create
   * @returns Promise resolving to created task
   */
  async createAgentTask(sessionId: string, task: Omit<EnhancedAgentTask, 'id'>): Promise<EnhancedAgentTask> {
    try {
      const newTask: EnhancedAgentTask = {
        ...task,
        id: `task-${Date.now()}`
      };
      
      const tasks = await this.getAgentTasks(sessionId);
      tasks.push(newTask);
      await this.saveAgentTasks(sessionId, tasks);
      
      return newTask;
    } catch (error) {
      console.error("Failed to create agent task:", error);
      throw error;
    }
  },

  /**
   * Updates an agent task
   * @param sessionId - The session ID
   * @param taskId - The task ID
   * @param updates - The updates to apply
   * @returns Promise resolving to updated task
   */
  async updateAgentTask(sessionId: string, taskId: string, updates: Partial<EnhancedAgentTask>): Promise<EnhancedAgentTask> {
    try {
      const tasks = await this.getAgentTasks(sessionId);
      const taskIndex = tasks.findIndex(t => t.id === taskId);
      
      if (taskIndex === -1) {
        throw new Error(`Task ${taskId} not found`);
      }
      
      tasks[taskIndex] = { ...tasks[taskIndex], ...updates };
      await this.saveAgentTasks(sessionId, tasks);
      
      return tasks[taskIndex];
    } catch (error) {
      console.error("Failed to update agent task:", error);
      throw error;
    }
  },

  /**
   * Gets agent conversations for a session
   * @param sessionId - The session ID
   * @returns Promise resolving to conversation list
   */
  async getAgentConversations(sessionId: string): Promise<EnhancedAgentConversation[]> {
    try {
      // Mock implementation - replace with real backend call
      const mockConversations = localStorage.getItem(`agent_conversations_${sessionId}`);
      return mockConversations ? JSON.parse(mockConversations) : [];
    } catch (error) {
      console.error("Failed to get agent conversations:", error);
      return [];
    }
  },

  /**
   * Saves agent conversations for a session
   * @param sessionId - The session ID
   * @param conversations - The conversations to save
   * @returns Promise resolving when saved
   */
  async saveAgentConversations(sessionId: string, conversations: EnhancedAgentConversation[]): Promise<void> {
    try {
      // Mock implementation - replace with real backend call
      localStorage.setItem(`agent_conversations_${sessionId}`, JSON.stringify(conversations));
    } catch (error) {
      console.error("Failed to save agent conversations:", error);
      throw error;
    }
  },

  /**
   * Sends a message to an agent
   * @param sessionId - The session ID
   * @param agentId - The agent ID
   * @param message - The message content
   * @returns Promise resolving to agent response
   */
  async sendMessageToAgent(_sessionId: string, _agentId: string, message: string): Promise<EnhancedAgentMessage> {
    try {
      // Mock implementation - in real app this would call Claude API
      const response: EnhancedAgentMessage = {
        id: `msg-${Date.now()}`,
        role: 'agent',
        content: `I understand your request: "${message}". Let me work on that for you.`,
        timestamp: new Date(),
        metadata: {
          tokensUsed: Math.floor(Math.random() * 100) + 50,
          responseTime: Math.floor(Math.random() * 2000) + 500,
          confidence: 0.85 + Math.random() * 0.15
        }
      };

      return response;
    } catch (error) {
      console.error("Failed to send message to agent:", error);
      throw error;
    }
  },

  /**
   * Executes an agent task
   * @param sessionId - The session ID
   * @param agentId - The agent ID
   * @param taskDescription - The task description
   * @returns Promise resolving to task result
   */
  async executeAgentTask(_sessionId: string, _agentId: string, _taskDescription: string): Promise<string> {
    try {
      // Mock implementation - in real app this would execute the actual task
      const mockResults = [
        "Task completed successfully. I've implemented the requested functionality with proper error handling and documentation.",
        "Analysis complete. I've identified several optimization opportunities and implemented the improvements.",
        "Code review finished. I've found and fixed potential security issues and improved code quality.",
        "Testing completed. All tests are passing and I've added additional test cases for edge scenarios.",
        "Documentation updated. I've created comprehensive guides and examples for the new features."
      ];

      const result = mockResults[Math.floor(Math.random() * mockResults.length)];
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));
      
      return result;
    } catch (error) {
      console.error("Failed to execute agent task:", error);
      throw error;
    }
  },

  /**
   * Gets agent analytics data
   * @param sessionId - The session ID
   * @returns Promise resolving to analytics data
   */
  async getAgentAnalytics(sessionId: string): Promise<{
    totalTasks: number;
    completedTasks: number;
    averageSuccessRate: number;
    totalTokensUsed: number;
    averageResponseTime: number;
    agentPerformance: Array<{
      agentId: string;
      name: string;
      tasksCompleted: number;
      successRate: number;
      averageResponseTime: number;
    }>;
  }> {
    try {
      const agents = await this.getAgents(sessionId);
      const tasks = await this.getAgentTasks(sessionId);
      
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(t => t.status === 'completed').length;
      const averageSuccessRate = agents.reduce((acc, a) => acc + a.metrics.successRate, 0) / agents.length;
      const totalTokensUsed = agents.reduce((acc, a) => acc + a.metrics.tokensUsed, 0);
      const averageResponseTime = agents.reduce((acc, a) => acc + a.metrics.averageResponseTime, 0) / agents.length;
      
      const agentPerformance = agents.map(agent => ({
        agentId: agent.id,
        name: agent.name,
        tasksCompleted: agent.metrics.tasksCompleted,
        successRate: agent.metrics.successRate,
        averageResponseTime: agent.metrics.averageResponseTime
      }));

      return {
        totalTasks,
        completedTasks,
        averageSuccessRate,
        totalTokensUsed,
        averageResponseTime,
        agentPerformance
      };
    } catch (error) {
      console.error("Failed to get agent analytics:", error);
      throw error;
    }
  },

  /**
   * Fetches MCP marketplace data from mcpservers.org
   */
  async fetchMCPMarketplace(useLive: boolean = false): Promise<any[]> {
    try {
      if (useLive) {
        // Try to fetch live data
        try {
          const liveData = await invoke<any[]>("fetch_mcp_marketplace_live");
          // Cache the live data
          await invoke("cache_mcp_marketplace", { servers: liveData });
          return liveData;
        } catch (error) {
          console.warn("Failed to fetch live data, falling back to cached/mock data:", error);
        }
      }
      
      // Try to load from cache first
      try {
        const cachedData = await invoke<any[]>("load_mcp_marketplace_cache");
        if (cachedData && cachedData.length > 0) {
          return cachedData;
        }
      } catch (error) {
        console.warn("No cached data available:", error);
      }
      
      // Fall back to mock data from Tauri
      return await invoke<any[]>("fetch_mcp_marketplace");
    } catch (error) {
      console.error("Failed to fetch marketplace data:", error);
      // Return client-side mock data as last resort
      return [
        {
          name: "bright-data",
          description: "Discover, extract, and interact with the web - one interface powering automated access across the public internet.",
          category: "web-data",
          featured: true,
          verified: true,
          popularity: 95,
          capabilities: ["web-scraping", "data-extraction", "automation"],
          transport: "stdio",
          installation: {
            npm: "@brightdata/mcp-server",
            command: "npx",
            args: ["@brightdata/mcp-server"],
            env: {
              BRIGHT_DATA_API_KEY: "<your-api-key>"
            }
          },
          documentation: "https://docs.brightdata.com/mcp",
          repository: "https://github.com/brightdata/mcp-server",
          tags: ["web-scraping", "proxy", "data-collection"]
        },
        {
          name: "agentql",
          description: "Enable AI agents to get structured data from unstructured web.",
          category: "web-data",
          verified: true,
          popularity: 88,
          capabilities: ["web-parsing", "data-structuring", "ai-integration"],
          transport: "stdio",
          installation: {
            pip: "agentql-mcp",
            command: "python",
            args: ["-m", "agentql_mcp"],
            env: {
              AGENTQL_API_KEY: "<your-api-key>"
            }
          },
          documentation: "https://docs.agentql.com",
          tags: ["web-data", "ai", "parsing"]
        },
        {
          name: "aws-bedrock-kb",
          description: "Query Amazon Bedrock Knowledge Bases using natural language to retrieve relevant information.",
          category: "cloud-services",
          verified: true,
          popularity: 92,
          capabilities: ["knowledge-base", "rag", "aws-integration"],
          transport: "stdio",
          installation: {
            npm: "@aws/bedrock-kb-mcp",
            command: "node",
            args: ["node_modules/@aws/bedrock-kb-mcp/dist/index.js"],
            env: {
              AWS_REGION: "us-east-1",
              AWS_ACCESS_KEY_ID: "<your-access-key>",
              AWS_SECRET_ACCESS_KEY: "<your-secret-key>"
            }
          },
          documentation: "https://aws.amazon.com/bedrock",
          tags: ["aws", "knowledge-base", "rag"]
        },
        {
          name: "apify",
          description: "Use 3,000+ pre-built cloud tools to extract data from websites, e-commerce, social media, search engines.",
          category: "web-data",
          featured: true,
          verified: true,
          popularity: 90,
          capabilities: ["web-scraping", "automation", "data-extraction"],
          transport: "sse",
          installation: {
            url: "https://api.apify.com/v2/mcp",
            env: {
              APIFY_API_TOKEN: "<your-api-token>"
            }
          },
          documentation: "https://docs.apify.com/mcp",
          tags: ["web-scraping", "automation", "cloud"]
        },
        {
          name: "github",
          description: "Interact with GitHub repositories, issues, pull requests, and more.",
          category: "development",
          verified: true,
          popularity: 94,
          capabilities: ["repository-management", "issue-tracking", "ci-cd"],
          transport: "stdio",
          installation: {
            npm: "@github/mcp-server",
            command: "node",
            args: ["node_modules/@github/mcp-server/dist/index.js"],
            env: {
              GITHUB_TOKEN: "<your-github-token>"
            }
          },
          documentation: "https://docs.github.com/mcp",
          repository: "https://github.com/github/mcp-server",
          tags: ["github", "development", "version-control"]
        },
        {
          name: "openai",
          description: "Connect to OpenAI GPT models and assistants for AI-powered workflows.",
          category: "ai-tools",
          featured: true,
          verified: true,
          popularity: 96,
          capabilities: ["ai-generation", "chat", "embeddings"],
          transport: "stdio",
          installation: {
            npm: "@openai/mcp-server",
            command: "node",
            args: ["node_modules/@openai/mcp-server/dist/index.js"],
            env: {
              OPENAI_API_KEY: "<your-openai-api-key>"
            }
          },
          documentation: "https://platform.openai.com/docs",
          tags: ["ai", "gpt", "llm"]
        },
        {
          name: "postgres",
          description: "Execute queries and manage PostgreSQL databases.",
          category: "databases",
          verified: true,
          popularity: 89,
          capabilities: ["sql-queries", "database-management", "data-export"],
          transport: "stdio",
          installation: {
            npm: "@postgres/mcp-server",
            command: "node",
            args: ["node_modules/@postgres/mcp-server/dist/index.js"],
            env: {
              POSTGRES_CONNECTION_STRING: "<your-connection-string>"
            }
          },
          documentation: "https://www.postgresql.org/docs/",
          tags: ["database", "sql", "postgres"]
        },
        {
          name: "docker",
          description: "Manage Docker containers, images, and compose stacks.",
          category: "development",
          verified: true,
          popularity: 87,
          capabilities: ["container-management", "image-building", "compose"],
          transport: "stdio",
          installation: {
            npm: "@docker/mcp-server",
            command: "node",
            args: ["node_modules/@docker/mcp-server/dist/index.js"]
          },
          documentation: "https://docs.docker.com",
          tags: ["docker", "containers", "devops"]
        },
        {
          name: "slack",
          description: "Send messages and interact with Slack workspaces.",
          category: "automation",
          verified: true,
          popularity: 85,
          capabilities: ["messaging", "channel-management", "file-sharing"],
          transport: "sse",
          installation: {
            url: "https://slack.com/api/mcp",
            env: {
              SLACK_BOT_TOKEN: "<your-bot-token>",
              SLACK_APP_TOKEN: "<your-app-token>"
            }
          },
          documentation: "https://api.slack.com",
          tags: ["communication", "messaging", "automation"]
        },
        {
          name: "stripe",
          description: "Process payments and manage Stripe accounts.",
          category: "automation",
          verified: true,
          popularity: 82,
          capabilities: ["payment-processing", "subscription-management", "invoicing"],
          transport: "stdio",
          installation: {
            npm: "@stripe/mcp-server",
            command: "node",
            args: ["node_modules/@stripe/mcp-server/dist/index.js"],
            env: {
              STRIPE_SECRET_KEY: "<your-secret-key>"
            }
          },
          documentation: "https://stripe.com/docs",
          tags: ["payments", "fintech", "automation"]
        },
        {
          name: "vault",
          description: "Manage secrets and encryption with HashiCorp Vault.",
          category: "security",
          verified: true,
          popularity: 78,
          capabilities: ["secret-management", "encryption", "authentication"],
          transport: "stdio",
          installation: {
            command: "vault",
            args: ["mcp-server"],
            env: {
              VAULT_ADDR: "<your-vault-address>",
              VAULT_TOKEN: "<your-vault-token>"
            }
          },
          documentation: "https://www.vaultproject.io/docs",
          tags: ["security", "secrets", "encryption"]
        }
      ];
    }
  },
  
  /**
   * Legacy webFetch method for backward compatibility
   */
  async webFetch(_url: string, _prompt: string): Promise<string> {
    const data = await this.fetchMCPMarketplace(false);
    return JSON.stringify(data);
  }

};
