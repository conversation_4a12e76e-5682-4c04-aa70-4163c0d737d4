"""
Claude Code Tool Adapter for TRAE-Agent Integration

This module provides an adapter to expose Claude Code CLI functionality
as a TRAE-Agent tool, enabling TRAE-Agent to leverage Claude Code's capabilities.
"""

import asyncio
import subprocess
import json
from typing import Dict, List, Any, Optional
from pathlib import Path

from trae.core.tools.base import Tool, ToolParameter
from trae.core.tools.types import ToolParameterType


class ClaudeCodeTool(Tool):
    """
    Adapter to expose Claude Code CLI as a TRAE-Agent tool.
    
    This tool allows TRAE-Agent to execute tasks using Claude Code's
    native capabilities including advanced search, code analysis, and
    specialized development tools.
    """
    
    def __init__(self, claude_code_path: str = "claude-code"):
        """
        Initialize the Claude Code tool adapter.
        
        Args:
            claude_code_path: Path to the Claude Code CLI executable
        """
        self.claude_code_path = claude_code_path
        self.name = "claude_code"
        self.description = (
            "Execute complex software engineering tasks using Claude Code CLI. "
            "Best for: code analysis, advanced search, understanding codebases, "
            "and tasks requiring <PERSON>'s native capabilities."
        )
    
    def get_name(self) -> str:
        """Get the tool name."""
        return self.name
    
    def get_parameters(self) -> List[ToolParameter]:
        """Define the parameters for the Claude Code tool."""
        return [
            ToolParameter(
                name="task",
                description="Natural language description of the task to perform",
                required=True,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="working_directory",
                description="Working directory for the task (defaults to current directory)",
                required=False,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="context_files",
                description="List of files to include as context (comma-separated paths)",
                required=False,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="mode",
                description="Execution mode: 'interactive' or 'batch' (default: batch)",
                required=False,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="timeout",
                description="Timeout in seconds (default: 300)",
                required=False,
                type=ToolParameterType.INTEGER
            )
        ]
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task using Claude Code CLI.
        
        Args:
            arguments: Dictionary containing task parameters
            
        Returns:
            Dictionary containing execution results
        """
        task = arguments.get("task")
        if not task:
            return {
                "status": "error",
                "error": "Task parameter is required"
            }
        
        working_dir = arguments.get("working_directory", ".")
        context_files = arguments.get("context_files", "")
        mode = arguments.get("mode", "batch")
        timeout = arguments.get("timeout", 300)
        
        # Build the command
        cmd = [self.claude_code_path]
        
        if mode == "batch":
            cmd.extend(["run", task])
        else:
            # For interactive mode, we'd need a different approach
            return {
                "status": "error",
                "error": "Interactive mode not yet supported in this adapter"
            }
        
        # Add context files if provided
        if context_files:
            files = [f.strip() for f in context_files.split(",")]
            for file in files:
                if Path(file).exists():
                    cmd.extend(["--context", file])
        
        # Execute the command
        try:
            result = await self._run_subprocess(
                cmd, 
                cwd=working_dir, 
                timeout=timeout
            )
            
            return {
                "status": "success",
                "output": result["stdout"],
                "error": result["stderr"] if result["stderr"] else None,
                "return_code": result["returncode"],
                "command": " ".join(cmd)
            }
            
        except asyncio.TimeoutError:
            return {
                "status": "error",
                "error": f"Command timed out after {timeout} seconds",
                "command": " ".join(cmd)
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "command": " ".join(cmd)
            }
    
    async def _run_subprocess(
        self, 
        cmd: List[str], 
        cwd: str = ".", 
        timeout: int = 300
    ) -> Dict[str, Any]:
        """
        Run a subprocess asynchronously with timeout.
        
        Args:
            cmd: Command and arguments to execute
            cwd: Working directory
            timeout: Timeout in seconds
            
        Returns:
            Dictionary with stdout, stderr, and return code
        """
        proc = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=cwd
        )
        
        try:
            stdout, stderr = await asyncio.wait_for(
                proc.communicate(), 
                timeout=timeout
            )
            
            return {
                "stdout": stdout.decode("utf-8"),
                "stderr": stderr.decode("utf-8"),
                "returncode": proc.returncode
            }
        except asyncio.TimeoutError:
            proc.kill()
            await proc.wait()
            raise


class ClaudeSearchTool(Tool):
    """
    Specialized tool for Claude Code's advanced search capabilities.
    
    This tool provides direct access to Claude Code's powerful search
    features including semantic search, pattern matching, and codebase
    understanding.
    """
    
    def __init__(self, claude_code_path: str = "claude-code"):
        self.claude_code_path = claude_code_path
        self.name = "claude_search"
        self.description = (
            "Advanced code search using Claude Code's semantic understanding. "
            "Searches for concepts, patterns, and relationships in code."
        )
    
    def get_name(self) -> str:
        return self.name
    
    def get_parameters(self) -> List[ToolParameter]:
        return [
            ToolParameter(
                name="query",
                description="Search query (natural language or pattern)",
                required=True,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="search_type",
                description="Type of search: 'semantic', 'pattern', 'dependency', 'usage'",
                required=False,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="scope",
                description="Search scope: path or glob pattern",
                required=False,
                type=ToolParameterType.STRING
            ),
            ToolParameter(
                name="limit",
                description="Maximum number of results (default: 20)",
                required=False,
                type=ToolParameterType.INTEGER
            )
        ]
    
    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a search using Claude Code's search capabilities."""
        query = arguments.get("query")
        if not query:
            return {
                "status": "error",
                "error": "Query parameter is required"
            }
        
        search_type = arguments.get("search_type", "semantic")
        scope = arguments.get("scope", "**/*")
        limit = arguments.get("limit", 20)
        
        # Construct a search task for Claude Code
        search_task = f"Search for '{query}' in {scope}"
        if search_type != "semantic":
            search_task += f" using {search_type} search"
        search_task += f". Return up to {limit} results with context."
        
        # Use the base ClaudeCodeTool to execute
        base_tool = ClaudeCodeTool(self.claude_code_path)
        result = await base_tool.execute({"task": search_task})
        
        # Parse and structure the results
        if result["status"] == "success":
            # Attempt to extract structured results from the output
            try:
                # This is a simplified example - in practice, you'd need
                # more sophisticated parsing based on Claude Code's output format
                output = result["output"]
                results = self._parse_search_results(output)
                
                return {
                    "status": "success",
                    "results": results,
                    "count": len(results),
                    "query": query,
                    "search_type": search_type
                }
            except Exception as e:
                return {
                    "status": "error",
                    "error": f"Failed to parse search results: {str(e)}",
                    "raw_output": result["output"]
                }
        else:
            return result
    
    def _parse_search_results(self, output: str) -> List[Dict[str, Any]]:
        """
        Parse search results from Claude Code output.
        
        This is a placeholder - actual implementation would depend on
        Claude Code's output format.
        """
        # Simplified parsing logic
        results = []
        lines = output.split("\n")
        
        current_result = {}
        for line in lines:
            if line.startswith("File:"):
                if current_result:
                    results.append(current_result)
                current_result = {"file": line[5:].strip()}
            elif line.startswith("Line:"):
                current_result["line"] = int(line[5:].strip())
            elif line.startswith("Match:"):
                current_result["match"] = line[6:].strip()
            elif line.strip() and current_result:
                current_result.setdefault("context", []).append(line)
        
        if current_result:
            results.append(current_result)
        
        return results


def register_claude_code_tools(tool_executor):
    """
    Register Claude Code tools with a TRAE-Agent ToolExecutor.
    
    Args:
        tool_executor: TRAE-Agent ToolExecutor instance
    """
    # Register the main Claude Code tool
    tool_executor.register_tool(ClaudeCodeTool())
    
    # Register the specialized search tool
    tool_executor.register_tool(ClaudeSearchTool())
    
    print("Claude Code tools registered successfully")


# Example usage
if __name__ == "__main__":
    async def test_claude_code_tool():
        tool = ClaudeCodeTool()
        
        # Test basic task execution
        result = await tool.execute({
            "task": "Find all Python files that import asyncio",
            "working_directory": ".",
            "timeout": 60
        })
        
        print("Result:", json.dumps(result, indent=2))
    
    # Run the test
    asyncio.run(test_claude_code_tool())