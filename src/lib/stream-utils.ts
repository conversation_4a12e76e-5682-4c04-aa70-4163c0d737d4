// Safe streaming message utilities
export interface StreamParseResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Safely parse streaming JSON messages
 */
export function parseStreamMessage<T = any>(
  payload: string | null | undefined
): StreamParseResult<T> {
  if (!payload || typeof payload !== 'string') {
    return {
      success: false,
      error: 'Empty or invalid payload'
    };
  }

  const trimmed = payload.trim();
  if (!trimmed) {
    return {
      success: false,
      error: 'Empty payload after trimming'
    };
  }

  try {
    const parsed = JSON.parse(trimmed);
    return {
      success: true,
      data: parsed
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown parse error';
    console.warn('Stream message parse failed:', errorMessage, 'Payload:', trimmed.substring(0, 100));
    
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Parse JSONL stream safely
 */
export function parseJsonlStream<T = any>(
  jsonlString: string
): StreamParseResult<T[]> {
  if (!jsonlString || typeof jsonlString !== 'string') {
    return {
      success: false,
      error: 'Empty or invalid JSONL string'
    };
  }

  try {
    const lines = jsonlString.split('\n').filter(line => line.trim());
    const results: T[] = [];
    const errors: string[] = [];
    
    for (let i = 0; i < lines.length; i++) {
      const result = parseStreamMessage<T>(lines[i]);
      if (result.success && result.data !== undefined) {
        results.push(result.data);
      } else {
        errors.push(`Line ${i + 1}: ${result.error}`);
      }
    }
    
    return {
      success: true,
      data: results
    };
  } catch (error) {
    return {
      success: false,
      error: `JSONL parsing failed: ${error}`
    };
  }
}

/**
 * Safe content stringification for display
 */
export function safeStringifyContent(content: any): string {
  if (content === null || content === undefined) {
    return '';
  }
  
  if (typeof content === 'string') {
    return content;
  }
  
  if (typeof content === 'object' && content.text) {
    if (typeof content.text === 'string') {
      return content.text;
    }
    if (typeof content.text === 'object' && content.text.text) {
      return content.text.text;
    }
  }
  
  try {
    return JSON.stringify(content, null, 2);
  } catch (error) {
    console.warn('Failed to stringify content:', error);
    return '[Content display error]';
  }
}