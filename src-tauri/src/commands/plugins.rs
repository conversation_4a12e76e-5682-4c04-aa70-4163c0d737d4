use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use tauri::State;
use tokio::sync::Mutex;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Plugin {
    pub id: String,
    pub name: String,
    pub description: String,
    pub version: String,
    pub author: String,
    pub enabled: bool,
    pub installed: bool,
    pub verified: bool,
    pub category: Option<String>,
    pub icon: Option<String>,
    pub repository: Option<String>,
    pub capabilities: Vec<String>,
    pub settings: Option<PluginSettings>,
    pub rating: Option<f32>,
    pub downloads: Option<u32>,
    pub readme: Option<String>,
    pub changelog: Option<Vec<ChangelogEntry>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginManifest {
    pub id: String,
    pub name: String,
    pub description: String,
    pub version: String,
    pub author: String,
    pub entry: String,
    pub capabilities: Vec<String>,
    pub repository: Option<String>,
    pub icon: Option<String>,
    pub category: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PluginSettings {
    #[serde(flatten)]
    pub settings: HashMap<String, PluginSettingDefinition>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginSettingDefinition {
    pub label: String,
    pub description: Option<String>,
    #[serde(rename = "type")]
    pub setting_type: String,
    pub value: serde_json::Value,
    pub default: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChangelogEntry {
    pub version: String,
    pub changes: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginRegistry {
    pub plugins: Vec<Plugin>,
    pub last_updated: String,
}

pub struct PluginsState {
    plugins_dir: PathBuf,
    installed_plugins: Mutex<HashMap<String, Plugin>>,
    registry_cache: Mutex<Option<PluginRegistry>>,
}

impl PluginsState {
    pub fn new() -> Self {
        let plugins_dir = dirs::config_dir()
            .map(|dir| dir.join("claudia").join("plugins"))
            .unwrap_or_else(|| PathBuf::from("./plugins"));
        
        // Create plugins directory if it doesn't exist
        let _ = fs::create_dir_all(&plugins_dir);
        
        Self {
            plugins_dir,
            installed_plugins: Mutex::new(HashMap::new()),
            registry_cache: Mutex::new(None),
        }
    }
    
    async fn load_installed_plugins(&self) -> Result<Vec<Plugin>, String> {
        let mut plugins = Vec::new();
        
        if let Ok(entries) = fs::read_dir(&self.plugins_dir) {
            for entry in entries.flatten() {
                if entry.path().is_dir() {
                    let manifest_path = entry.path().join("manifest.json");
                    if manifest_path.exists() {
                        if let Ok(content) = fs::read_to_string(&manifest_path) {
                            if let Ok(manifest) = serde_json::from_str::<PluginManifest>(&content) {
                                // Load plugin settings if exists
                                let settings_path = entry.path().join("settings.json");
                                let settings = if settings_path.exists() {
                                    fs::read_to_string(&settings_path)
                                        .ok()
                                        .and_then(|s| serde_json::from_str(&s).ok())
                                } else {
                                    None
                                };
                                
                                // Check if plugin is enabled
                                let enabled_path = entry.path().join(".enabled");
                                let enabled = enabled_path.exists();
                                
                                plugins.push(Plugin {
                                    id: manifest.id.clone(),
                                    name: manifest.name,
                                    description: manifest.description,
                                    version: manifest.version,
                                    author: manifest.author,
                                    enabled,
                                    installed: true,
                                    verified: false,
                                    category: manifest.category,
                                    icon: manifest.icon,
                                    repository: manifest.repository,
                                    capabilities: manifest.capabilities,
                                    settings,
                                    rating: None,
                                    downloads: None,
                                    readme: None,
                                    changelog: None,
                                });
                            }
                        }
                    }
                }
            }
        }
        
        Ok(plugins)
    }
    
    async fn fetch_registry(&self) -> Result<PluginRegistry, String> {
        // Check cache first
        if let Some(cached) = self.registry_cache.lock().await.as_ref() {
            return Ok(cached.clone());
        }
        
        // In a real implementation, this would fetch from a registry server
        // For now, return mock data
        let registry = PluginRegistry {
            plugins: vec![
                Plugin {
                    id: "git-integration".to_string(),
                    name: "Git Integration Enhanced".to_string(),
                    description: "Advanced Git operations and visualizations for Claude Code".to_string(),
                    version: "2.1.0".to_string(),
                    author: "Community".to_string(),
                    enabled: false,
                    installed: false,
                    verified: true,
                    category: Some("git".to_string()),
                    icon: None,
                    repository: Some("https://github.com/example/git-integration".to_string()),
                    capabilities: vec!["filesystem".to_string(), "commands".to_string()],
                    settings: None,
                    rating: Some(4.8),
                    downloads: Some(15420),
                    readme: Some("# Git Integration Enhanced\n\nPowerful Git features for Claude Code.".to_string()),
                    changelog: Some(vec![
                        ChangelogEntry {
                            version: "2.1.0".to_string(),
                            changes: "Added interactive rebase support".to_string(),
                        },
                        ChangelogEntry {
                            version: "2.0.0".to_string(),
                            changes: "Complete rewrite with better performance".to_string(),
                        },
                    ]),
                },
                Plugin {
                    id: "code-snippets".to_string(),
                    name: "Code Snippets Manager".to_string(),
                    description: "Create, manage, and share code snippets across projects".to_string(),
                    version: "1.5.2".to_string(),
                    author: "SnippetLabs".to_string(),
                    enabled: false,
                    installed: false,
                    verified: true,
                    category: Some("productivity".to_string()),
                    icon: None,
                    repository: Some("https://github.com/example/code-snippets".to_string()),
                    capabilities: vec!["ui".to_string(), "filesystem".to_string()],
                    settings: None,
                    rating: Some(4.6),
                    downloads: Some(8932),
                    readme: Some("# Code Snippets Manager\n\nManage your code snippets efficiently.".to_string()),
                    changelog: None,
                },
                Plugin {
                    id: "theme-studio".to_string(),
                    name: "Theme Studio Pro".to_string(),
                    description: "Create and customize beautiful themes with live preview".to_string(),
                    version: "3.0.1".to_string(),
                    author: "ThemeWorks".to_string(),
                    enabled: false,
                    installed: false,
                    verified: false,
                    category: Some("ui".to_string()),
                    icon: None,
                    repository: None,
                    capabilities: vec!["themes".to_string(), "ui".to_string()],
                    settings: None,
                    rating: Some(4.9),
                    downloads: Some(23150),
                    readme: None,
                    changelog: None,
                },
            ],
            last_updated: chrono::Utc::now().to_rfc3339(),
        };
        
        // Cache the registry
        *self.registry_cache.lock().await = Some(registry.clone());
        
        Ok(registry)
    }
}

impl Default for PluginsState {
    fn default() -> Self {
        Self::new()
    }
}

#[tauri::command]
pub async fn get_installed_plugins(state: State<'_, PluginsState>) -> Result<Vec<Plugin>, String> {
    state.load_installed_plugins().await
}

#[tauri::command]
pub async fn get_available_plugins(state: State<'_, PluginsState>) -> Result<Vec<Plugin>, String> {
    let registry = state.fetch_registry().await?;
    let installed = state.load_installed_plugins().await?;
    
    // Filter out already installed plugins
    let installed_ids: Vec<String> = installed.iter().map(|p| p.id.clone()).collect();
    let available = registry.plugins.into_iter()
        .filter(|p| !installed_ids.contains(&p.id))
        .collect();
    
    Ok(available)
}

#[tauri::command]
pub async fn install_plugin(
    plugin_id: String,
    state: State<'_, PluginsState>,
) -> Result<(), String> {
    let registry = state.fetch_registry().await?;
    let plugin = registry.plugins.iter()
        .find(|p| p.id == plugin_id)
        .ok_or_else(|| format!("Plugin {} not found", plugin_id))?;
    
    // Create plugin directory
    let plugin_dir = state.plugins_dir.join(&plugin_id);
    fs::create_dir_all(&plugin_dir)
        .map_err(|e| format!("Failed to create plugin directory: {}", e))?;
    
    // Create manifest
    let manifest = PluginManifest {
        id: plugin.id.clone(),
        name: plugin.name.clone(),
        description: plugin.description.clone(),
        version: plugin.version.clone(),
        author: plugin.author.clone(),
        entry: "index.js".to_string(),
        capabilities: plugin.capabilities.clone(),
        repository: plugin.repository.clone(),
        icon: plugin.icon.clone(),
        category: plugin.category.clone(),
    };
    
    let manifest_path = plugin_dir.join("manifest.json");
    let manifest_content = serde_json::to_string_pretty(&manifest)
        .map_err(|e| format!("Failed to serialize manifest: {}", e))?;
    fs::write(&manifest_path, manifest_content)
        .map_err(|e| format!("Failed to write manifest: {}", e))?;
    
    // Create a basic plugin entry file
    let entry_content = format!(
        r#"// {} v{}
// Plugin entry point

export function activate(context) {{
    console.log('Plugin {} activated');
    
    // Register your plugin functionality here
}}

export function deactivate() {{
    console.log('Plugin {} deactivated');
}}"#,
        plugin.name, plugin.version, plugin.name, plugin.name
    );
    
    let entry_path = plugin_dir.join("index.js");
    fs::write(&entry_path, entry_content)
        .map_err(|e| format!("Failed to write entry file: {}", e))?;
    
    // Mark as enabled by default
    let enabled_path = plugin_dir.join(".enabled");
    fs::write(&enabled_path, "")
        .map_err(|e| format!("Failed to mark plugin as enabled: {}", e))?;
    
    Ok(())
}

#[tauri::command]
pub async fn uninstall_plugin(
    plugin_id: String,
    state: State<'_, PluginsState>,
) -> Result<(), String> {
    let plugin_dir = state.plugins_dir.join(&plugin_id);
    
    if plugin_dir.exists() {
        fs::remove_dir_all(&plugin_dir)
            .map_err(|e| format!("Failed to remove plugin directory: {}", e))?;
    }
    
    Ok(())
}

#[tauri::command]
pub async fn toggle_plugin(
    plugin_id: String,
    enabled: bool,
    state: State<'_, PluginsState>,
) -> Result<(), String> {
    let plugin_dir = state.plugins_dir.join(&plugin_id);
    let enabled_path = plugin_dir.join(".enabled");
    
    if enabled {
        fs::write(&enabled_path, "")
            .map_err(|e| format!("Failed to enable plugin: {}", e))?;
    } else if enabled_path.exists() {
        fs::remove_file(&enabled_path)
            .map_err(|e| format!("Failed to disable plugin: {}", e))?;
    }
    
    Ok(())
}

#[tauri::command]
pub async fn update_plugin_settings(
    plugin_id: String,
    settings: PluginSettings,
    state: State<'_, PluginsState>,
) -> Result<(), String> {
    let plugin_dir = state.plugins_dir.join(&plugin_id);
    let settings_path = plugin_dir.join("settings.json");
    
    let settings_content = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;
    fs::write(&settings_path, settings_content)
        .map_err(|e| format!("Failed to write settings: {}", e))?;
    
    Ok(())
}

#[tauri::command]
pub async fn create_plugin(
    manifest: PluginManifest,
    state: State<'_, PluginsState>,
) -> Result<(), String> {
    let plugin_dir = state.plugins_dir.join(&manifest.id);
    
    // Check if plugin already exists
    if plugin_dir.exists() {
        return Err(format!("Plugin {} already exists", manifest.id));
    }
    
    // Create plugin directory structure
    fs::create_dir_all(&plugin_dir)
        .map_err(|e| format!("Failed to create plugin directory: {}", e))?;
    
    // Write manifest
    let manifest_path = plugin_dir.join("manifest.json");
    let manifest_content = serde_json::to_string_pretty(&manifest)
        .map_err(|e| format!("Failed to serialize manifest: {}", e))?;
    fs::write(&manifest_path, manifest_content)
        .map_err(|e| format!("Failed to write manifest: {}", e))?;
    
    // Create entry file with scaffold
    let entry_content = format!(
        r#"// {} v{}
// By {}
// 
// {}

// Plugin API available:
// - context.subscribeToCommand(command, handler)
// - context.registerUI(component)
// - context.storage.get(key)
// - context.storage.set(key, value)
// - context.api.* (access to Claudia API)

export function activate(context) {{
    console.log('[{}] Plugin activated');
    
    // Example: Register a command
    context.subscribeToCommand('{}:hello', async (args) => {{
        console.log('[{}] Hello command executed:', args);
        return {{ success: true, message: 'Hello from {}!' }};
    }});
    
    // TODO: Implement your plugin functionality here
}}

export function deactivate() {{
    console.log('[{}] Plugin deactivated');
    // TODO: Clean up resources
}}"#,
        manifest.name,
        manifest.version,
        manifest.author,
        manifest.description,
        manifest.name,
        manifest.id,
        manifest.name,
        manifest.name,
        manifest.name
    );
    
    let entry_path = plugin_dir.join(&manifest.entry);
    fs::write(&entry_path, entry_content)
        .map_err(|e| format!("Failed to write entry file: {}", e))?;
    
    // Create README
    let readme_content = format!(
        r#"# {}

{}

## Development

This plugin was created with the Claudia Plugin Manager.

### Getting Started

1. Edit `{}` to implement your plugin functionality
2. Test your plugin in Claudia
3. Share your plugin with the community!

### Available APIs

Your plugin has access to these capabilities: {}

### Plugin Structure

```
{}/
├── manifest.json    # Plugin metadata
├── {}          # Main entry point
└── README.md       # This file
```
"#,
        manifest.name,
        manifest.description,
        manifest.entry,
        manifest.capabilities.join(", "),
        manifest.id,
        manifest.entry
    );
    
    let readme_path = plugin_dir.join("README.md");
    fs::write(&readme_path, readme_content)
        .map_err(|e| format!("Failed to write README: {}", e))?;
    
    // Mark as enabled
    let enabled_path = plugin_dir.join(".enabled");
    fs::write(&enabled_path, "")
        .map_err(|e| format!("Failed to enable plugin: {}", e))?;
    
    Ok(())
}

#[tauri::command]
pub async fn reload_plugins(state: State<'_, PluginsState>) -> Result<(), String> {
    // Clear cache to force reload
    *state.registry_cache.lock().await = None;
    state.installed_plugins.lock().await.clear();
    
    Ok(())
}