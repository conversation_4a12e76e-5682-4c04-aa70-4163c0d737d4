use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;
use tauri::State;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::process::Command;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WorkflowStep {
    pub id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub step_type: String,
    pub command: String,
    pub condition: Option<String>,
    pub timeout: u32,
    pub retries: u32,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Workflow {
    pub id: String,
    pub name: String,
    pub description: String,
    pub trigger: String,
    pub trigger_config: serde_json::Value,
    pub steps: Vec<WorkflowStep>,
    pub enabled: bool,
    pub last_run: Option<DateTime<Utc>>,
    pub status: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StepResult {
    pub step_id: String,
    pub status: String,
    pub output: Option<String>,
    pub error: Option<String>,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct WorkflowExecution {
    pub id: String,
    pub workflow_id: String,
    pub start_time: DateTime<Utc>,
    pub end_time: Option<DateTime<Utc>>,
    pub status: String,
    pub current_step: usize,
    pub step_results: Vec<StepResult>,
}

pub struct WorkflowStore {
    workflows: Mutex<HashMap<String, Workflow>>,
    executions: Mutex<Vec<WorkflowExecution>>,
}

impl Default for WorkflowStore {
    fn default() -> Self {
        Self {
            workflows: Mutex::new(HashMap::new()),
            executions: Mutex::new(Vec::new()),
        }
    }
}

#[tauri::command]
pub async fn create_workflow(
    workflow: Workflow,
    store: State<'_, WorkflowStore>,
) -> Result<Workflow, String> {
    let mut workflows = store.workflows.lock().unwrap();
    let id = Uuid::new_v4().to_string();
    let mut new_workflow = workflow;
    new_workflow.id = id.clone();
    workflows.insert(id.clone(), new_workflow.clone());
    Ok(new_workflow)
}

#[tauri::command]
pub async fn get_workflows(
    store: State<'_, WorkflowStore>,
) -> Result<Vec<Workflow>, String> {
    let workflows = store.workflows.lock().unwrap();
    Ok(workflows.values().cloned().collect())
}

#[tauri::command]
pub async fn update_workflow(
    id: String,
    workflow: Workflow,
    store: State<'_, WorkflowStore>,
) -> Result<Workflow, String> {
    let mut workflows = store.workflows.lock().unwrap();
    workflows.insert(id, workflow.clone());
    Ok(workflow)
}

#[tauri::command]
pub async fn delete_workflow(
    id: String,
    store: State<'_, WorkflowStore>,
) -> Result<(), String> {
    let mut workflows = store.workflows.lock().unwrap();
    workflows.remove(&id);
    Ok(())
}

#[tauri::command]
pub async fn execute_workflow(
    workflow_id: String,
    store: State<'_, WorkflowStore>,
) -> Result<WorkflowExecution, String> {
    // Get workflow and clone it to avoid holding the lock
    let workflow = {
        let workflows = store.workflows.lock().unwrap();
        workflows.get(&workflow_id)
            .ok_or_else(|| "Workflow not found".to_string())?
            .clone()
    };

    let execution_id = Uuid::new_v4().to_string();
    let mut execution = WorkflowExecution {
        id: execution_id.clone(),
        workflow_id: workflow_id.clone(),
        start_time: Utc::now(),
        end_time: None,
        status: "running".to_string(),
        current_step: 0,
        step_results: Vec::new(),
    };

    // Update workflow status
    {
        let mut workflows = store.workflows.lock().unwrap();
        if let Some(wf) = workflows.get_mut(&workflow_id) {
            wf.status = "running".to_string();
            wf.last_run = Some(Utc::now());
        }
    }

    // Execute each step
    for (index, step) in workflow.steps.iter().enumerate() {
        if !step.enabled {
            continue;
        }

        execution.current_step = index;

        let step_result = execute_step(step).await;
        execution.step_results.push(step_result.clone());

        if step_result.status == "failed" {
            execution.status = "failed".to_string();
            execution.end_time = Some(Utc::now());
            break;
        }
    }

    if execution.status != "failed" {
        execution.status = "success".to_string();
        execution.end_time = Some(Utc::now());
    }

    // Update workflow status
    {
        let mut workflows = store.workflows.lock().unwrap();
        if let Some(wf) = workflows.get_mut(&workflow_id) {
            wf.status = execution.status.clone();
        }
    }

    // Store execution
    {
        let mut executions = store.executions.lock().unwrap();
        executions.push(execution.clone());
        
        // Keep only last 100 executions
        let executions_len = executions.len();
        if executions_len > 100 {
            executions.drain(0..executions_len - 100);
        }
    }

    Ok(execution)
}

async fn execute_step(step: &WorkflowStep) -> StepResult {
    let started_at = Utc::now();
    
    // Execute the command
    let output = tokio::task::spawn_blocking({
        let command = step.command.clone();
        move || {
            Command::new("sh")
                .arg("-c")
                .arg(&command)
                .output()
        }
    }).await;

    match output {
        Ok(Ok(output)) => {
            let stdout = String::from_utf8_lossy(&output.stdout).to_string();
            let stderr = String::from_utf8_lossy(&output.stderr).to_string();
            
            StepResult {
                step_id: step.id.clone(),
                status: if output.status.success() { "success".to_string() } else { "failed".to_string() },
                output: Some(stdout),
                error: if stderr.is_empty() { None } else { Some(stderr) },
                started_at,
                completed_at: Some(Utc::now()),
            }
        }
        _ => StepResult {
            step_id: step.id.clone(),
            status: "failed".to_string(),
            output: None,
            error: Some("Failed to execute command".to_string()),
            started_at,
            completed_at: Some(Utc::now()),
        }
    }
}

#[tauri::command]
pub async fn get_workflow_executions(
    workflow_id: Option<String>,
    limit: Option<usize>,
    store: State<'_, WorkflowStore>,
) -> Result<Vec<WorkflowExecution>, String> {
    let executions = store.executions.lock().unwrap();
    let mut result: Vec<WorkflowExecution> = if let Some(id) = workflow_id {
        executions.iter()
            .filter(|e| e.workflow_id == id)
            .cloned()
            .collect()
    } else {
        executions.clone()
    };

    // Sort by start time descending
    result.sort_by(|a, b| b.start_time.cmp(&a.start_time));

    // Apply limit
    if let Some(limit) = limit {
        result.truncate(limit);
    }

    Ok(result)
}

#[tauri::command]
pub async fn stop_workflow_execution(
    execution_id: String,
    store: State<'_, WorkflowStore>,
) -> Result<(), String> {
    let mut executions = store.executions.lock().unwrap();
    if let Some(execution) = executions.iter_mut().find(|e| e.id == execution_id) {
        execution.status = "cancelled".to_string();
        execution.end_time = Some(Utc::now());
    }
    Ok(())
}