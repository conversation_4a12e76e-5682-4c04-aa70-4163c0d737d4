use anyhow::Result;
use serde::{Deserialize, Serialize};
use scraper::{Html, Selector};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketplaceServer {
    pub name: String,
    pub description: String,
    pub category: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub author: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub featured: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub verified: Option<bool>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub popularity: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub capabilities: Option<Vec<String>>,
    pub transport: String,
    pub installation: InstallationConfig,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub documentation: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub repository: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub license: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstallationConfig {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub command: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub args: Option<Vec<String>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub env: Option<HashMap<String, String>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub url: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub npm: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pip: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub requirements: Option<Vec<String>>,
}

/// Fetches MCP marketplace data from mcpservers.org
#[tauri::command]
pub async fn fetch_mcp_marketplace() -> Result<Vec<MarketplaceServer>, String> {
    // For now, return mock data
    // In a real implementation, we would:
    // 1. Fetch HTML from mcpservers.org
    // 2. Parse the HTML using scraper
    // 3. Extract server information
    // 4. Return structured data
    
    let servers = vec![
        MarketplaceServer {
            name: "bright-data".to_string(),
            description: "Discover, extract, and interact with the web - one interface powering automated access across the public internet.".to_string(),
            category: "web-data".to_string(),
            author: None,
            featured: Some(true),
            verified: Some(true),
            popularity: Some(95),
            capabilities: Some(vec!["web-scraping".to_string(), "data-extraction".to_string(), "automation".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@brightdata/mcp-server".to_string()),
                command: Some("npx".to_string()),
                args: Some(vec!["@brightdata/mcp-server".to_string()]),
                env: Some(HashMap::from([
                    ("BRIGHT_DATA_API_KEY".to_string(), "<your-api-key>".to_string())
                ])),
                url: None,
                pip: None,
                requirements: None,
            },
            documentation: Some("https://docs.brightdata.com/mcp".to_string()),
            repository: Some("https://github.com/brightdata/mcp-server".to_string()),
            license: None,
            tags: Some(vec!["web-scraping".to_string(), "proxy".to_string(), "data-collection".to_string()]),
        },
        MarketplaceServer {
            name: "agentql".to_string(),
            description: "Enable AI agents to get structured data from unstructured web.".to_string(),
            category: "web-data".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(88),
            capabilities: Some(vec!["web-parsing".to_string(), "data-structuring".to_string(), "ai-integration".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                pip: Some("agentql-mcp".to_string()),
                command: Some("python".to_string()),
                args: Some(vec!["-m".to_string(), "agentql_mcp".to_string()]),
                env: Some(HashMap::from([
                    ("AGENTQL_API_KEY".to_string(), "<your-api-key>".to_string())
                ])),
                npm: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://docs.agentql.com".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["web-data".to_string(), "ai".to_string(), "parsing".to_string()]),
        },
        MarketplaceServer {
            name: "aws-bedrock-kb".to_string(),
            description: "Query Amazon Bedrock Knowledge Bases using natural language to retrieve relevant information.".to_string(),
            category: "cloud-services".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(92),
            capabilities: Some(vec!["knowledge-base".to_string(), "rag".to_string(), "aws-integration".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@aws/bedrock-kb-mcp".to_string()),
                command: Some("node".to_string()),
                args: Some(vec!["node_modules/@aws/bedrock-kb-mcp/dist/index.js".to_string()]),
                env: Some(HashMap::from([
                    ("AWS_REGION".to_string(), "us-east-1".to_string()),
                    ("AWS_ACCESS_KEY_ID".to_string(), "<your-access-key>".to_string()),
                    ("AWS_SECRET_ACCESS_KEY".to_string(), "<your-secret-key>".to_string())
                ])),
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://aws.amazon.com/bedrock".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["aws".to_string(), "knowledge-base".to_string(), "rag".to_string()]),
        },
        MarketplaceServer {
            name: "apify".to_string(),
            description: "Use 3,000+ pre-built cloud tools to extract data from websites, e-commerce, social media, search engines.".to_string(),
            category: "web-data".to_string(),
            author: None,
            featured: Some(true),
            verified: Some(true),
            popularity: Some(90),
            capabilities: Some(vec!["web-scraping".to_string(), "automation".to_string(), "data-extraction".to_string()]),
            transport: "sse".to_string(),
            installation: InstallationConfig {
                url: Some("https://api.apify.com/v2/mcp".to_string()),
                env: Some(HashMap::from([
                    ("APIFY_API_TOKEN".to_string(), "<your-api-token>".to_string())
                ])),
                npm: None,
                command: None,
                args: None,
                pip: None,
                requirements: None,
            },
            documentation: Some("https://docs.apify.com/mcp".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["web-scraping".to_string(), "automation".to_string(), "cloud".to_string()]),
        },
        MarketplaceServer {
            name: "github".to_string(),
            description: "Interact with GitHub repositories, issues, pull requests, and more.".to_string(),
            category: "development".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(94),
            capabilities: Some(vec!["repository-management".to_string(), "issue-tracking".to_string(), "ci-cd".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@github/mcp-server".to_string()),
                command: Some("node".to_string()),
                args: Some(vec!["node_modules/@github/mcp-server/dist/index.js".to_string()]),
                env: Some(HashMap::from([
                    ("GITHUB_TOKEN".to_string(), "<your-github-token>".to_string())
                ])),
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://docs.github.com/mcp".to_string()),
            repository: Some("https://github.com/github/mcp-server".to_string()),
            license: None,
            tags: Some(vec!["github".to_string(), "development".to_string(), "version-control".to_string()]),
        },
        MarketplaceServer {
            name: "openai".to_string(),
            description: "Connect to OpenAI GPT models and assistants for AI-powered workflows.".to_string(),
            category: "ai-tools".to_string(),
            author: None,
            featured: Some(true),
            verified: Some(true),
            popularity: Some(96),
            capabilities: Some(vec!["ai-generation".to_string(), "chat".to_string(), "embeddings".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@openai/mcp-server".to_string()),
                command: Some("node".to_string()),
                args: Some(vec!["node_modules/@openai/mcp-server/dist/index.js".to_string()]),
                env: Some(HashMap::from([
                    ("OPENAI_API_KEY".to_string(), "<your-openai-api-key>".to_string())
                ])),
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://platform.openai.com/docs".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["ai".to_string(), "gpt".to_string(), "llm".to_string()]),
        },
        MarketplaceServer {
            name: "postgres".to_string(),
            description: "Execute queries and manage PostgreSQL databases.".to_string(),
            category: "databases".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(89),
            capabilities: Some(vec!["sql-queries".to_string(), "database-management".to_string(), "data-export".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@postgres/mcp-server".to_string()),
                command: Some("node".to_string()),
                args: Some(vec!["node_modules/@postgres/mcp-server/dist/index.js".to_string()]),
                env: Some(HashMap::from([
                    ("POSTGRES_CONNECTION_STRING".to_string(), "<your-connection-string>".to_string())
                ])),
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://www.postgresql.org/docs/".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["database".to_string(), "sql".to_string(), "postgres".to_string()]),
        },
        MarketplaceServer {
            name: "docker".to_string(),
            description: "Manage Docker containers, images, and compose stacks.".to_string(),
            category: "development".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(87),
            capabilities: Some(vec!["container-management".to_string(), "image-building".to_string(), "compose".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@docker/mcp-server".to_string()),
                command: Some("node".to_string()),
                args: Some(vec!["node_modules/@docker/mcp-server/dist/index.js".to_string()]),
                env: None,
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://docs.docker.com".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["docker".to_string(), "containers".to_string(), "devops".to_string()]),
        },
        MarketplaceServer {
            name: "slack".to_string(),
            description: "Send messages and interact with Slack workspaces.".to_string(),
            category: "automation".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(85),
            capabilities: Some(vec!["messaging".to_string(), "channel-management".to_string(), "file-sharing".to_string()]),
            transport: "sse".to_string(),
            installation: InstallationConfig {
                url: Some("https://slack.com/api/mcp".to_string()),
                env: Some(HashMap::from([
                    ("SLACK_BOT_TOKEN".to_string(), "<your-bot-token>".to_string()),
                    ("SLACK_APP_TOKEN".to_string(), "<your-app-token>".to_string())
                ])),
                npm: None,
                command: None,
                args: None,
                pip: None,
                requirements: None,
            },
            documentation: Some("https://api.slack.com".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["communication".to_string(), "messaging".to_string(), "automation".to_string()]),
        },
        MarketplaceServer {
            name: "stripe".to_string(),
            description: "Process payments and manage Stripe accounts.".to_string(),
            category: "automation".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(82),
            capabilities: Some(vec!["payment-processing".to_string(), "subscription-management".to_string(), "invoicing".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                npm: Some("@stripe/mcp-server".to_string()),
                command: Some("node".to_string()),
                args: Some(vec!["node_modules/@stripe/mcp-server/dist/index.js".to_string()]),
                env: Some(HashMap::from([
                    ("STRIPE_SECRET_KEY".to_string(), "<your-secret-key>".to_string())
                ])),
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://stripe.com/docs".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["payments".to_string(), "fintech".to_string(), "automation".to_string()]),
        },
        MarketplaceServer {
            name: "vault".to_string(),
            description: "Manage secrets and encryption with HashiCorp Vault.".to_string(),
            category: "security".to_string(),
            author: None,
            featured: None,
            verified: Some(true),
            popularity: Some(78),
            capabilities: Some(vec!["secret-management".to_string(), "encryption".to_string(), "authentication".to_string()]),
            transport: "stdio".to_string(),
            installation: InstallationConfig {
                command: Some("vault".to_string()),
                args: Some(vec!["mcp-server".to_string()]),
                env: Some(HashMap::from([
                    ("VAULT_ADDR".to_string(), "<your-vault-address>".to_string()),
                    ("VAULT_TOKEN".to_string(), "<your-vault-token>".to_string())
                ])),
                npm: None,
                pip: None,
                url: None,
                requirements: None,
            },
            documentation: Some("https://www.vaultproject.io/docs".to_string()),
            repository: None,
            license: None,
            tags: Some(vec!["security".to_string(), "secrets".to_string(), "encryption".to_string()]),
        },
    ];
    
    Ok(servers)
}

/// Fetches live MCP marketplace data with HTML parsing
#[tauri::command]
pub async fn fetch_mcp_marketplace_live() -> Result<Vec<MarketplaceServer>, String> {
    // Fetch the HTML content
    let response = reqwest::get("https://mcpservers.org")
        .await
        .map_err(|e| format!("Failed to fetch data: {}", e))?;
    
    let html_content = response.text()
        .await
        .map_err(|e| format!("Failed to read response: {}", e))?;
    
    // Parse HTML and extract servers in a separate scope to ensure it's not held across await
    let servers = {
        let document = Html::parse_document(&html_content);
        
        // Define selectors for server elements
        // Note: These selectors would need to be adjusted based on the actual HTML structure
        let server_selector = match Selector::parse(".server-card, .mcp-server, [data-server]") {
            Ok(selector) => selector,
            Err(_) => return Err("Failed to create server selector".to_string()),
        };
        
        let mut servers = Vec::new();
        
        for element in document.select(&server_selector) {
            // Extract server information from HTML elements
            // This is a simplified example - actual implementation would need proper selectors
            let name = element
                .select(&Selector::parse(".server-name, h3, h2").unwrap())
                .next()
                .map(|el| el.text().collect::<String>())
                .unwrap_or_default()
                .trim()
                .to_string();
            
            let description = element
                .select(&Selector::parse(".server-description, .description, p").unwrap())
                .next()
                .map(|el| el.text().collect::<String>())
                .unwrap_or_default()
                .trim()
                .to_string();
            
            // Skip if we don't have basic information
            if name.is_empty() || description.is_empty() {
                continue;
            }
            
            // Create a basic server entry
            servers.push(MarketplaceServer {
                name,
                description,
                category: "uncategorized".to_string(),
                author: None,
                featured: None,
                verified: None,
                popularity: None,
                capabilities: None,
                transport: "stdio".to_string(),
                installation: InstallationConfig {
                    command: None,
                    args: None,
                    env: None,
                    url: None,
                    npm: None,
                    pip: None,
                    requirements: None,
                },
                documentation: None,
                repository: None,
                license: None,
                tags: None,
            });
        }
        
        servers
    };
    
    // If no servers found from parsing, return mock data as fallback
    if servers.is_empty() {
        return fetch_mcp_marketplace().await;
    }
    
    Ok(servers)
}

/// Caches marketplace data locally
#[tauri::command]
pub async fn cache_mcp_marketplace(servers: Vec<MarketplaceServer>) -> Result<(), String> {
    let cache_dir = dirs::cache_dir()
        .ok_or("Failed to get cache directory")?
        .join("claudia")
        .join("mcp-marketplace");
    
    std::fs::create_dir_all(&cache_dir)
        .map_err(|e| format!("Failed to create cache directory: {}", e))?;
    
    let cache_file = cache_dir.join("servers.json");
    let json_data = serde_json::to_string_pretty(&servers)
        .map_err(|e| format!("Failed to serialize data: {}", e))?;
    
    std::fs::write(cache_file, json_data)
        .map_err(|e| format!("Failed to write cache file: {}", e))?;
    
    Ok(())
}

/// Loads cached marketplace data
#[tauri::command]
pub async fn load_mcp_marketplace_cache() -> Result<Vec<MarketplaceServer>, String> {
    let cache_file = dirs::cache_dir()
        .ok_or("Failed to get cache directory")?
        .join("claudia")
        .join("mcp-marketplace")
        .join("servers.json");
    
    if !cache_file.exists() {
        return Ok(Vec::new());
    }
    
    let json_data = std::fs::read_to_string(cache_file)
        .map_err(|e| format!("Failed to read cache file: {}", e))?;
    
    let servers: Vec<MarketplaceServer> = serde_json::from_str(&json_data)
        .map_err(|e| format!("Failed to parse cache data: {}", e))?;
    
    Ok(servers)
}