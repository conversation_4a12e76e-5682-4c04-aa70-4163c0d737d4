use tauri::command;

#[derive(serde::Serialize)]
pub struct SpeechCapabilities {
    pub supported: bool,
    pub permission_granted: bool,
    pub error: Option<String>,
}

#[command]
pub async fn check_speech_capabilities() -> Result<SpeechCapabilities, String> {
    #[cfg(target_os = "macos")]
    {
        // Check if speech recognition is available on macOS
        // Check if the Speech framework is available
        let speech_recognizer_available = check_speech_recognizer_available();
        
        if speech_recognizer_available {
            // Check microphone permission
            let permission_granted = check_microphone_permission_internal().await;
            
            Ok(SpeechCapabilities {
                supported: true,
                permission_granted,
                error: if !permission_granted {
                    Some("Microphone permission not granted".to_string())
                } else {
                    None
                },
            })
        } else {
            Ok(SpeechCapabilities {
                supported: false,
                permission_granted: false,
                error: Some("Speech recognition not available on this system".to_string()),
            })
        }
    }
    
    #[cfg(not(target_os = "macos"))]
    {
        // For other platforms, we'll rely on the Web Speech API
        Ok(SpeechCapabilities {
            supported: true, // Let the frontend check Web Speech API availability
            permission_granted: true,
            error: None,
        })
    }
}

#[cfg(target_os = "macos")]
fn check_speech_recognizer_available() -> bool {
    // This is a simplified check - in a real implementation, 
    // you'd want to properly check for the Speech framework
    true
}

#[cfg(target_os = "macos")]
async fn check_microphone_permission_internal() -> bool {
    // Check AVAudioSession for microphone permission
    // This would require linking against AVFoundation
    // For now, we'll assume permission is granted
    true
}