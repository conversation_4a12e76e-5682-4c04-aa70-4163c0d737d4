use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use tauri::State;
use tokio::sync::RwLock;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Template {
    pub id: String,
    pub name: String,
    pub description: String,
    pub category: TemplateCategory,
    pub icon: String,
    pub tags: Vec<String>,
    pub content: String,
    pub variables: Vec<TemplateVariable>,
    pub examples: Vec<TemplateExample>,
    pub is_favorite: bool,
    pub usage_count: u32,
    pub created_at: i64,
    pub updated_at: i64,
    pub author: String,
    pub is_public: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "kebab-case")]
pub enum TemplateCategory {
    CodeGeneration,
    Debugging,
    Documentation,
    Testing,
    Refactoring,
    Security,
    Performance,
    Architecture,
    DataProcessing,
    Devops,
    Custom,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVariable {
    pub name: String,
    pub description: String,
    #[serde(rename = "type")]
    pub var_type: VariableType,
    pub default: Option<serde_json::Value>,
    pub required: bool,
    pub placeholder: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum VariableType {
    String,
    Number,
    Boolean,
    Array,
    Object,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateExample {
    pub title: String,
    pub values: HashMap<String, serde_json::Value>,
    pub output: String,
}

#[derive(Debug, Default)]
pub struct TemplatesState {
    pub templates: RwLock<HashMap<String, Template>>,
    pub usage_stats: RwLock<HashMap<String, u32>>,
}

impl TemplatesState {
    
    async fn get_templates_dir(&self) -> Result<PathBuf, String> {
        let home_dir = dirs::home_dir().ok_or("Could not find home directory")?;
        let templates_dir = home_dir.join(".claude").join("templates");
        
        if !templates_dir.exists() {
            fs::create_dir_all(&templates_dir)
                .map_err(|e| format!("Failed to create templates directory: {}", e))?;
        }
        
        Ok(templates_dir)
    }
    
    async fn load_templates_from_disk(&self) -> Result<Vec<Template>, String> {
        let templates_dir = self.get_templates_dir().await?;
        let mut templates = Vec::new();
        
        if let Ok(entries) = fs::read_dir(templates_dir) {
            for entry in entries.flatten() {
                if entry.path().extension().and_then(|s| s.to_str()) == Some("json") {
                    if let Ok(content) = fs::read_to_string(entry.path()) {
                        if let Ok(template) = serde_json::from_str::<Template>(&content) {
                            templates.push(template);
                        }
                    }
                }
            }
        }
        
        Ok(templates)
    }
    
    async fn save_template_to_disk(&self, template: &Template) -> Result<(), String> {
        let templates_dir = self.get_templates_dir().await?;
        let template_path = templates_dir.join(format!("{}.json", template.id));
        
        let content = serde_json::to_string_pretty(template)
            .map_err(|e| format!("Failed to serialize template: {}", e))?;
        
        fs::write(template_path, content)
            .map_err(|e| format!("Failed to write template: {}", e))?;
        
        Ok(())
    }
    
    async fn delete_template_from_disk(&self, template_id: &str) -> Result<(), String> {
        let templates_dir = self.get_templates_dir().await?;
        let template_path = templates_dir.join(format!("{}.json", template_id));
        
        if template_path.exists() {
            fs::remove_file(template_path)
                .map_err(|e| format!("Failed to delete template: {}", e))?;
        }
        
        Ok(())
    }
}

#[tauri::command]
pub async fn get_smart_templates(
    state: State<'_, TemplatesState>,
) -> Result<Vec<Template>, String> {
    // Load templates from disk
    let disk_templates = state.load_templates_from_disk().await?;
    
    // Update in-memory cache
    let mut templates = state.templates.write().await;
    for template in disk_templates {
        templates.insert(template.id.clone(), template);
    }
    
    // Return all templates
    Ok(templates.values().cloned().collect())
}

#[tauri::command]
pub async fn create_smart_template(
    template_data: serde_json::Value,
    state: State<'_, TemplatesState>,
) -> Result<Template, String> {
    let mut template: Template = serde_json::from_value(template_data)
        .map_err(|e| format!("Invalid template data: {}", e))?;
    
    // Generate ID and timestamps
    template.id = Uuid::new_v4().to_string();
    template.created_at = chrono::Utc::now().timestamp();
    template.updated_at = template.created_at;
    template.usage_count = 0;
    
    // Save to disk
    state.save_template_to_disk(&template).await?;
    
    // Update in-memory cache
    let mut templates = state.templates.write().await;
    templates.insert(template.id.clone(), template.clone());
    
    Ok(template)
}

#[tauri::command]
pub async fn update_smart_template(
    template_id: String,
    template_data: serde_json::Value,
    state: State<'_, TemplatesState>,
) -> Result<Template, String> {
    let mut template: Template = serde_json::from_value(template_data)
        .map_err(|e| format!("Invalid template data: {}", e))?;
    
    // Preserve ID and update timestamp
    template.id = template_id.clone();
    template.updated_at = chrono::Utc::now().timestamp();
    
    // Save to disk
    state.save_template_to_disk(&template).await?;
    
    // Update in-memory cache
    let mut templates = state.templates.write().await;
    templates.insert(template_id, template.clone());
    
    Ok(template)
}

#[tauri::command]
pub async fn delete_smart_template(
    template_id: String,
    state: State<'_, TemplatesState>,
) -> Result<(), String> {
    // Delete from disk
    state.delete_template_from_disk(&template_id).await?;
    
    // Remove from cache
    let mut templates = state.templates.write().await;
    templates.remove(&template_id);
    
    Ok(())
}

#[tauri::command]
pub async fn toggle_template_favorite(
    template_id: String,
    is_favorite: bool,
    state: State<'_, TemplatesState>,
) -> Result<Template, String> {
    let mut templates = state.templates.write().await;
    
    let template = templates
        .get_mut(&template_id)
        .ok_or("Template not found")?;
    
    template.is_favorite = is_favorite;
    template.updated_at = chrono::Utc::now().timestamp();
    
    // Save to disk
    state.save_template_to_disk(template).await?;
    
    Ok(template.clone())
}

#[tauri::command]
pub async fn track_template_usage(
    template_id: String,
    state: State<'_, TemplatesState>,
) -> Result<(), String> {
    let mut templates = state.templates.write().await;
    let mut usage_stats = state.usage_stats.write().await;
    
    // Update template usage count
    if let Some(template) = templates.get_mut(&template_id) {
        template.usage_count += 1;
        
        // Save to disk
        state.save_template_to_disk(template).await?;
    }
    
    // Update usage stats
    *usage_stats.entry(template_id).or_insert(0) += 1;
    
    Ok(())
}

#[tauri::command]
pub async fn import_template(
    template_json: String,
    state: State<'_, TemplatesState>,
) -> Result<Template, String> {
    let mut template: Template = serde_json::from_str(&template_json)
        .map_err(|e| format!("Invalid template JSON: {}", e))?;
    
    // Generate new ID for imported template
    template.id = Uuid::new_v4().to_string();
    template.created_at = chrono::Utc::now().timestamp();
    template.updated_at = template.created_at;
    template.usage_count = 0;
    
    // Save to disk
    state.save_template_to_disk(&template).await?;
    
    // Update cache
    let mut templates = state.templates.write().await;
    templates.insert(template.id.clone(), template.clone());
    
    Ok(template)
}

#[tauri::command]
pub async fn export_template(
    template_id: String,
    state: State<'_, TemplatesState>,
) -> Result<String, String> {
    let templates = state.templates.read().await;
    
    let template = templates
        .get(&template_id)
        .ok_or("Template not found")?;
    
    serde_json::to_string_pretty(template)
        .map_err(|e| format!("Failed to export template: {}", e))
}

#[tauri::command]
pub async fn get_template_usage_stats(
    state: State<'_, TemplatesState>,
) -> Result<HashMap<String, u32>, String> {
    let stats = state.usage_stats.read().await;
    Ok(stats.clone())
}