use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc, Duration};
use rusqlite::{Connection, params};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub timestamp: DateTime<Utc>,
    pub api_calls: u32,
    pub total_tokens: u32,
    pub input_tokens: u32,
    pub output_tokens: u32,
    pub latency: f64,
    pub cost: f64,
    pub model: String,
    pub endpoint: String,
    pub success: bool,
    pub error_rate: f64,
    pub cache_hits: u32,
    pub cache_misses: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AggregatedMetrics {
    pub total_calls: u32,
    pub total_tokens: u32,
    pub total_cost: f64,
    pub average_latency: f64,
    pub error_rate: f64,
    pub cache_hit_rate: f64,
    pub tokens_per_minute: f64,
    pub cost_per_hour: f64,
    pub model_usage: HashMap<String, u32>,
    pub endpoint_usage: HashMap<String, u32>,
    pub peak_usage_time: DateTime<Utc>,
    pub bottlenecks: Vec<Bottleneck>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Bottleneck {
    #[serde(rename = "type")]
    pub bottleneck_type: BottleneckType,
    pub severity: Severity,
    pub description: String,
    pub recommendation: String,
    pub impact: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum BottleneckType {
    Latency,
    Cost,
    Error,
    Token,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Severity {
    Low,
    Medium,
    High,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceData {
    pub metrics: Vec<PerformanceMetrics>,
    pub aggregated: AggregatedMetrics,
}

#[derive(Debug, Clone)]
pub struct PerformanceState {
    pub metrics: Arc<RwLock<HashMap<String, Vec<PerformanceMetrics>>>>,
    pub db_path: String,
}

impl PerformanceState {
    pub fn new(app_data_dir: String) -> Self {
        let db_path = format!("{}/performance_metrics.db", app_data_dir);
        let state = Self {
            metrics: Arc::new(RwLock::new(HashMap::new())),
            db_path,
        };
        
        // Initialize database
        if let Err(e) = state.init_database() {
            eprintln!("Failed to initialize performance database: {}", e);
        }
        
        // Note: Loading from database will be done asynchronously later
        // to avoid blocking issues during initialization
        
        state
    }
    
    fn init_database(&self) -> Result<()> {
        let conn = Connection::open(&self.db_path)?;
        
        conn.execute(
            "CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                api_calls INTEGER NOT NULL,
                total_tokens INTEGER NOT NULL,
                input_tokens INTEGER NOT NULL,
                output_tokens INTEGER NOT NULL,
                latency REAL NOT NULL,
                cost REAL NOT NULL,
                model TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                success INTEGER NOT NULL,
                error_rate REAL NOT NULL,
                cache_hits INTEGER NOT NULL,
                cache_misses INTEGER NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )",
            [],
        )?;
        
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_session_timestamp 
             ON performance_metrics(session_id, timestamp)",
            [],
        )?;
        
        Ok(())
    }
    
    pub async fn ensure_loaded(&self) -> Result<()> {
        // Check if metrics are already loaded
        let metrics = self.metrics.read().await;
        if !metrics.is_empty() {
            return Ok(());
        }
        drop(metrics);
        
        // Load from database if empty
        self.load_from_database_async().await
    }
    
    async fn load_from_database_async(&self) -> Result<()> {
        let db_path = self.db_path.clone();
        let metrics_map = tokio::task::spawn_blocking(move || -> Result<HashMap<String, Vec<PerformanceMetrics>>> {
            let conn = Connection::open(&db_path)?;
            let mut stmt = conn.prepare(
                "SELECT session_id, timestamp, api_calls, total_tokens, input_tokens, 
                        output_tokens, latency, cost, model, endpoint, success, 
                        error_rate, cache_hits, cache_misses 
                 FROM performance_metrics 
                 WHERE timestamp > datetime('now', '-7 days')
                 ORDER BY timestamp DESC"
            )?;
            
            let metrics_iter = stmt.query_map([], |row| {
                let session_id: String = row.get(0)?;
                let timestamp_str: String = row.get(1)?;
                let timestamp = DateTime::parse_from_rfc3339(&timestamp_str)
                    .map(|dt| dt.with_timezone(&Utc))
                    .unwrap_or_else(|_| Utc::now());
                
                let metric = PerformanceMetrics {
                    timestamp,
                    api_calls: row.get(2)?,
                    total_tokens: row.get(3)?,
                    input_tokens: row.get(4)?,
                    output_tokens: row.get(5)?,
                    latency: row.get(6)?,
                    cost: row.get(7)?,
                    model: row.get(8)?,
                    endpoint: row.get(9)?,
                    success: row.get(10)?,
                    error_rate: row.get(11)?,
                    cache_hits: row.get(12)?,
                    cache_misses: row.get(13)?,
                };
                
                Ok((session_id, metric))
            })?;
            
            let mut metrics_map = HashMap::new();
            for metric_result in metrics_iter {
                if let Ok((session_id, metric)) = metric_result {
                    metrics_map
                        .entry(session_id)
                        .or_insert_with(Vec::new)
                        .push(metric);
                }
            }
            
            Ok(metrics_map)
        }).await.map_err(|e| anyhow::anyhow!("Failed to load metrics: {}", e))??;
        
        let mut metrics = self.metrics.write().await;
        *metrics = metrics_map;
        
        Ok(())
    }
    
    
    async fn save_metric_to_database(&self, session_id: &str, metric: &PerformanceMetrics) -> Result<()> {
        let db_path = self.db_path.clone();
        let session_id = session_id.to_string();
        let metric = metric.clone();
        
        tokio::task::spawn_blocking(move || -> Result<()> {
            let conn = Connection::open(&db_path)?;
            
            conn.execute(
                "INSERT INTO performance_metrics 
                 (session_id, timestamp, api_calls, total_tokens, input_tokens, 
                  output_tokens, latency, cost, model, endpoint, success, 
                  error_rate, cache_hits, cache_misses)
                 VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14)",
                params![
                    session_id,
                    metric.timestamp.to_rfc3339(),
                    metric.api_calls,
                    metric.total_tokens,
                    metric.input_tokens,
                    metric.output_tokens,
                    metric.latency,
                    metric.cost,
                    metric.model,
                    metric.endpoint,
                    metric.success,
                    metric.error_rate,
                    metric.cache_hits,
                    metric.cache_misses,
                ],
            )?;
            
            Ok(())
        }).await.map_err(|e| anyhow::anyhow!("Failed to save metric: {}", e))?
    }
    
    async fn cleanup_old_metrics(&self) -> Result<()> {
        let db_path = self.db_path.clone();
        
        tokio::task::spawn_blocking(move || -> Result<()> {
            let conn = Connection::open(&db_path)?;
            
            // Delete metrics older than 30 days
            conn.execute(
                "DELETE FROM performance_metrics 
                 WHERE timestamp < datetime('now', '-30 days')",
                [],
            )?;
            
            Ok(())
        }).await.map_err(|e| anyhow::anyhow!("Failed to cleanup metrics: {}", e))?
    }
}

#[tauri::command]
pub async fn get_performance_metrics(
    session_id: String,
    time_range: String,
    state: tauri::State<'_, PerformanceState>,
) -> Result<PerformanceData, String> {
    // Ensure metrics are loaded from database
    if let Err(e) = state.ensure_loaded().await {
        eprintln!("Failed to ensure metrics loaded: {}", e);
    }
    
    let metrics = state.metrics.read().await;
    let session_metrics = metrics.get(&session_id).cloned().unwrap_or_default();
    
    // Filter by time range
    let now = Utc::now();
    let cutoff = match time_range.as_str() {
        "1h" => now - Duration::hours(1),
        "24h" => now - Duration::hours(24),
        "7d" => now - Duration::days(7),
        "30d" => now - Duration::days(30),
        _ => now - Duration::hours(24),
    };
    
    let filtered_metrics: Vec<PerformanceMetrics> = session_metrics
        .into_iter()
        .filter(|m| m.timestamp > cutoff)
        .collect();
    
    let aggregated = calculate_aggregated_metrics(&filtered_metrics);
    
    Ok(PerformanceData {
        metrics: filtered_metrics,
        aggregated,
    })
}

#[tauri::command]
pub async fn record_performance_metric(
    session_id: String,
    metric: PerformanceMetrics,
    state: tauri::State<'_, PerformanceState>,
) -> Result<(), String> {
    // Save to database first
    if let Err(e) = state.save_metric_to_database(&session_id, &metric).await {
        eprintln!("Failed to save metric to database: {}", e);
    }
    
    let mut metrics = state.metrics.write().await;
    let session_id_clone = session_id.clone();
    metrics
        .entry(session_id)
        .or_insert_with(Vec::new)
        .push(metric);
    
    // Keep only last 10000 metrics per session to prevent memory issues
    if let Some(session_metrics) = metrics.get_mut(&session_id_clone) {
        if session_metrics.len() > 10000 {
            session_metrics.drain(0..1000);
        }
    }
    
    // Periodically cleanup old metrics
    static CLEANUP_COUNTER: std::sync::atomic::AtomicU32 = std::sync::atomic::AtomicU32::new(0);
    if CLEANUP_COUNTER.fetch_add(1, std::sync::atomic::Ordering::Relaxed) % 1000 == 0 {
        let state_clone = state.inner().clone();
        tokio::spawn(async move {
            if let Err(e) = state_clone.cleanup_old_metrics().await {
                eprintln!("Failed to cleanup old metrics: {}", e);
            }
        });
    }
    
    Ok(())
}

#[tauri::command]
pub async fn clear_performance_metrics(
    session_id: String,
    state: tauri::State<'_, PerformanceState>,
) -> Result<(), String> {
    let mut metrics = state.metrics.write().await;
    metrics.remove(&session_id);
    Ok(())
}

#[tauri::command]
pub async fn export_performance_report(
    session_id: String,
    state: tauri::State<'_, PerformanceState>,
) -> Result<String, String> {
    let metrics = state.metrics.read().await;
    let session_metrics = metrics.get(&session_id).cloned().unwrap_or_default();
    
    let report = serde_json::json!({
        "session_id": session_id,
        "generated_at": Utc::now(),
        "total_metrics": session_metrics.len(),
        "metrics": session_metrics,
        "summary": calculate_aggregated_metrics(&session_metrics),
    });
    
    serde_json::to_string_pretty(&report)
        .map_err(|e| format!("Failed to serialize report: {}", e))
}

fn calculate_aggregated_metrics(metrics: &[PerformanceMetrics]) -> AggregatedMetrics {
    if metrics.is_empty() {
        return AggregatedMetrics {
            total_calls: 0,
            total_tokens: 0,
            total_cost: 0.0,
            average_latency: 0.0,
            error_rate: 0.0,
            cache_hit_rate: 0.0,
            tokens_per_minute: 0.0,
            cost_per_hour: 0.0,
            model_usage: HashMap::new(),
            endpoint_usage: HashMap::new(),
            peak_usage_time: Utc::now(),
            bottlenecks: vec![],
        };
    }
    
    let total_calls = metrics.iter().map(|m| m.api_calls).sum::<u32>();
    let total_tokens = metrics.iter().map(|m| m.total_tokens).sum::<u32>();
    let total_cost = metrics.iter().map(|m| m.cost).sum::<f64>();
    let average_latency = metrics.iter().map(|m| m.latency).sum::<f64>() / metrics.len() as f64;
    
    let error_count = metrics.iter().filter(|m| !m.success).count();
    let error_rate = (error_count as f64 / metrics.len() as f64) * 100.0;
    
    let total_cache_hits = metrics.iter().map(|m| m.cache_hits).sum::<u32>();
    let total_cache_misses = metrics.iter().map(|m| m.cache_misses).sum::<u32>();
    let cache_hit_rate = if total_cache_hits + total_cache_misses > 0 {
        (total_cache_hits as f64 / (total_cache_hits + total_cache_misses) as f64) * 100.0
    } else {
        0.0
    };
    
    // Calculate time span
    let time_span = if metrics.len() > 1 {
        let first = metrics.first().unwrap().timestamp;
        let last = metrics.last().unwrap().timestamp;
        (last - first).num_minutes() as f64
    } else {
        1.0
    };
    
    let tokens_per_minute = if time_span > 0.0 {
        total_tokens as f64 / time_span
    } else {
        0.0
    };
    
    let cost_per_hour = if time_span > 0.0 {
        (total_cost / time_span) * 60.0
    } else {
        0.0
    };
    
    // Calculate model and endpoint usage
    let mut model_usage = HashMap::new();
    let mut endpoint_usage = HashMap::new();
    
    for metric in metrics {
        *model_usage.entry(metric.model.clone()).or_insert(0) += 1;
        *endpoint_usage.entry(metric.endpoint.clone()).or_insert(0) += 1;
    }
    
    // Find peak usage time
    let peak_usage_time = metrics
        .iter()
        .max_by_key(|m| m.api_calls)
        .map(|m| m.timestamp)
        .unwrap_or_else(Utc::now);
    
    // Identify bottlenecks
    let mut bottlenecks = vec![];
    
    if average_latency > 3000.0 {
        bottlenecks.push(Bottleneck {
            bottleneck_type: BottleneckType::Latency,
            severity: if average_latency > 5000.0 { Severity::High } else { Severity::Medium },
            description: format!("High average latency: {:.0}ms", average_latency),
            recommendation: "Consider implementing request batching, caching, or using edge locations".to_string(),
            impact: 80,
        });
    }
    
    if error_rate > 5.0 {
        bottlenecks.push(Bottleneck {
            bottleneck_type: BottleneckType::Error,
            severity: if error_rate > 10.0 { Severity::High } else { Severity::Medium },
            description: format!("High error rate: {:.1}%", error_rate),
            recommendation: "Review error logs, implement retry logic, and check rate limits".to_string(),
            impact: 90,
        });
    }
    
    if cost_per_hour > 10.0 {
        bottlenecks.push(Bottleneck {
            bottleneck_type: BottleneckType::Cost,
            severity: if cost_per_hour > 50.0 { Severity::High } else { Severity::Medium },
            description: format!("High cost per hour: ${:.2}", cost_per_hour),
            recommendation: "Use smaller models for simple tasks, implement token optimization".to_string(),
            impact: 70,
        });
    }
    
    if tokens_per_minute > 10000.0 {
        bottlenecks.push(Bottleneck {
            bottleneck_type: BottleneckType::Token,
            severity: Severity::Medium,
            description: format!("High token usage: {:.0} tokens/min", tokens_per_minute),
            recommendation: "Optimize prompts, use system messages effectively, implement summarization".to_string(),
            impact: 60,
        });
    }
    
    AggregatedMetrics {
        total_calls,
        total_tokens,
        total_cost,
        average_latency,
        error_rate,
        cache_hit_rate,
        tokens_per_minute,
        cost_per_hour,
        model_usage,
        endpoint_usage,
        peak_usage_time,
        bottlenecks,
    }
}