use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use tauri::State;
use tokio::sync::Mutex;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ThemeConfig {
    pub theme: String,
    pub color_scheme: String,
    pub font_size: u32,
    pub reduce_motion: bool,
    pub high_contrast: bool,
    pub color_blind_mode: String,
}

impl Default for ThemeConfig {
    fn default() -> Self {
        Self {
            theme: "system".to_string(),
            color_scheme: "default".to_string(),
            font_size: 16,
            reduce_motion: false,
            high_contrast: false,
            color_blind_mode: "none".to_string(),
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ColorScheme {
    pub primary: String,
    pub accent: String,
    pub background: String,
}

pub struct ThemeState {
    config: Mutex<ThemeConfig>,
    themes_dir: PathBuf,
}

impl ThemeState {
    pub fn new() -> Self {
        let themes_dir = dirs::config_dir()
            .map(|dir| dir.join("claudia").join("themes"))
            .unwrap_or_else(|| PathBuf::from("./themes"));
        
        // Create themes directory if it doesn't exist
        let _ = fs::create_dir_all(&themes_dir);
        
        Self {
            config: Mutex::new(Self::load_config(&themes_dir)),
            themes_dir,
        }
    }
    
    fn load_config(themes_dir: &PathBuf) -> ThemeConfig {
        let config_path = themes_dir.join("config.json");
        if let Ok(content) = fs::read_to_string(&config_path) {
            serde_json::from_str(&content).unwrap_or_default()
        } else {
            ThemeConfig::default()
        }
    }
    
    async fn save_config(&self, config: &ThemeConfig) -> Result<(), String> {
        let config_path = self.themes_dir.join("config.json");
        let content = serde_json::to_string_pretty(config)
            .map_err(|e| format!("Failed to serialize config: {}", e))?;
        fs::write(&config_path, content)
            .map_err(|e| format!("Failed to save config: {}", e))?;
        Ok(())
    }
}

impl Default for ThemeState {
    fn default() -> Self {
        Self::new()
    }
}

#[tauri::command]
pub async fn get_theme_config(state: State<'_, ThemeState>) -> Result<ThemeConfig, String> {
    let config = state.config.lock().await;
    Ok(config.clone())
}

#[tauri::command]
pub async fn set_theme_config(
    config: ThemeConfig,
    state: State<'_, ThemeState>,
) -> Result<(), String> {
    let mut current_config = state.config.lock().await;
    *current_config = config.clone();
    drop(current_config); // Release lock before saving
    state.save_config(&config).await?;
    Ok(())
}

#[tauri::command]
pub async fn get_available_color_schemes() -> Result<HashMap<String, ColorScheme>, String> {
    let mut schemes = HashMap::new();
    
    schemes.insert("default".to_string(), ColorScheme {
        primary: "#3B82F6".to_string(),
        accent: "#10B981".to_string(),
        background: "#FFFFFF".to_string(),
    });
    
    schemes.insert("ocean".to_string(), ColorScheme {
        primary: "#0EA5E9".to_string(),
        accent: "#06B6D4".to_string(),
        background: "#F0F9FF".to_string(),
    });
    
    schemes.insert("forest".to_string(), ColorScheme {
        primary: "#10B981".to_string(),
        accent: "#84CC16".to_string(),
        background: "#F0FDF4".to_string(),
    });
    
    schemes.insert("sunset".to_string(), ColorScheme {
        primary: "#F59E0B".to_string(),
        accent: "#EF4444".to_string(),
        background: "#FFF7ED".to_string(),
    });
    
    schemes.insert("midnight".to_string(), ColorScheme {
        primary: "#6366F1".to_string(),
        accent: "#8B5CF6".to_string(),
        background: "#1E1B4B".to_string(),
    });
    
    schemes.insert("aurora".to_string(), ColorScheme {
        primary: "#EC4899".to_string(),
        accent: "#A855F7".to_string(),
        background: "#FDF2F8".to_string(),
    });
    
    schemes.insert("monochrome".to_string(), ColorScheme {
        primary: "#6B7280".to_string(),
        accent: "#374151".to_string(),
        background: "#F9FAFB".to_string(),
    });
    
    Ok(schemes)
}

#[tauri::command]
pub async fn import_theme(
    name: String,
    scheme: ColorScheme,
    state: State<'_, ThemeState>,
) -> Result<(), String> {
    let theme_path = state.themes_dir.join(format!("{}.json", name));
    let content = serde_json::to_string_pretty(&scheme)
        .map_err(|e| format!("Failed to serialize theme: {}", e))?;
    fs::write(&theme_path, content)
        .map_err(|e| format!("Failed to save theme: {}", e))?;
    Ok(())
}

#[tauri::command]
pub async fn export_theme(
    name: String,
    state: State<'_, ThemeState>,
) -> Result<ColorScheme, String> {
    let theme_path = state.themes_dir.join(format!("{}.json", name));
    let content = fs::read_to_string(&theme_path)
        .map_err(|e| format!("Failed to read theme: {}", e))?;
    let scheme: ColorScheme = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse theme: {}", e))?;
    Ok(scheme)
}

#[tauri::command]
pub async fn list_custom_themes(state: State<'_, ThemeState>) -> Result<Vec<String>, String> {
    let mut themes = Vec::new();
    
    if let Ok(entries) = fs::read_dir(&state.themes_dir) {
        for entry in entries.flatten() {
            if let Some(name) = entry.file_name().to_str() {
                if name.ends_with(".json") && name != "config.json" {
                    themes.push(name.trim_end_matches(".json").to_string());
                }
            }
        }
    }
    
    Ok(themes)
}