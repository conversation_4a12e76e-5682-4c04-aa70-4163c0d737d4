use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::fs;
use std::path::Path;
use walkdir::WalkDir;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CodeFlowData {
    pub nodes: Vec<CodeNode>,
    pub links: Vec<CodeLink>,
    pub clusters: Vec<Cluster>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CodeNode {
    pub id: String,
    pub name: String,
    #[serde(rename = "type")]
    pub node_type: NodeType,
    pub path: String,
    pub lines: u32,
    pub complexity: u32,
    pub dependencies: Vec<String>,
    pub dependents: Vec<String>,
    pub metrics: NodeMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum NodeType {
    File,
    Function,
    Class,
    Module,
    Component,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CodeLink {
    pub source: String,
    pub target: String,
    #[serde(rename = "type")]
    pub link_type: LinkType,
    pub strength: f32,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>ialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LinkType {
    Import,
    Call,
    Extends,
    Implements,
    Uses,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeMetrics {
    pub lines_of_code: u32,
    pub cyclomatic_complexity: u32,
    pub maintainability_index: u32,
    pub test_coverage: Option<u32>,
    pub last_modified: i64,
    pub author: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Cluster {
    pub id: String,
    pub name: String,
    pub nodes: Vec<String>,
    #[serde(rename = "type")]
    pub cluster_type: ClusterType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ClusterType {
    Module,
    Feature,
    Layer,
}

#[tauri::command]
pub async fn get_code_flow_data(
    _session_id: String,
    project_path: String,
) -> Result<CodeFlowData, String> {
    analyze_project_structure(&project_path).await
}

async fn analyze_project_structure(project_path: &str) -> Result<CodeFlowData, String> {
    let path = Path::new(project_path);
    if !path.exists() {
        return Err("Project path does not exist".to_string());
    }

    let mut nodes = Vec::new();
    let mut links = Vec::new();
    let mut import_graph: HashMap<String, HashSet<String>> = HashMap::new();
    
    // Walk through project files
    for entry in WalkDir::new(project_path)
        .into_iter()
        .filter_map(|e| e.ok())
        .filter(|e| e.file_type().is_file())
        .filter(|e| is_code_file(e.path()))
    {
        let file_path = entry.path();
        let relative_path = file_path
            .strip_prefix(project_path)
            .unwrap_or(file_path)
            .to_string_lossy()
            .to_string();
        
        // Analyze file
        if let Ok(analysis) = analyze_file(file_path).await {
            let node_id = generate_node_id(&relative_path);
            
            let node = CodeNode {
                id: node_id.clone(),
                name: file_path
                    .file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string(),
                node_type: determine_node_type(file_path),
                path: relative_path,
                lines: analysis.lines,
                complexity: analysis.complexity,
                dependencies: analysis.imports.clone(),
                dependents: vec![], // Will be filled later
                metrics: NodeMetrics {
                    lines_of_code: analysis.lines,
                    cyclomatic_complexity: analysis.complexity,
                    maintainability_index: calculate_maintainability_index(
                        analysis.lines,
                        analysis.complexity,
                    ),
                    test_coverage: None, // Would need integration with test coverage tools
                    last_modified: get_last_modified(file_path),
                    author: get_git_author(file_path, project_path),
                },
            };
            
            nodes.push(node);
            import_graph.insert(node_id, analysis.imports.into_iter().collect());
        }
    }
    
    // Create links from import graph
    for (source_id, imports) in &import_graph {
        for import in imports {
            let target_id = generate_node_id(import);
            
            // Only create link if target exists in our nodes
            if nodes.iter().any(|n| n.id == target_id) {
                links.push(CodeLink {
                    source: source_id.clone(),
                    target: target_id.clone(),
                    link_type: LinkType::Import,
                    strength: 1.0,
                });
                
                // Update dependents
                if let Some(target_node) = nodes.iter_mut().find(|n| n.id == target_id) {
                    target_node.dependents.push(source_id.clone());
                }
            }
        }
    }
    
    // Create clusters based on directory structure
    let clusters = create_clusters(&nodes, project_path);
    
    Ok(CodeFlowData {
        nodes,
        links,
        clusters,
    })
}

#[derive(Debug)]
struct FileAnalysis {
    lines: u32,
    complexity: u32,
    imports: Vec<String>,
}

async fn analyze_file(path: &Path) -> Result<FileAnalysis, String> {
    let content = fs::read_to_string(path)
        .map_err(|e| format!("Failed to read file: {}", e))?;
    
    let lines = content.lines().count() as u32;
    let complexity = estimate_complexity(&content);
    let imports = extract_imports(&content, path);
    
    Ok(FileAnalysis {
        lines,
        complexity,
        imports,
    })
}

fn is_code_file(path: &Path) -> bool {
    if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
        matches!(
            ext,
            "js" | "jsx" | "ts" | "tsx" | "rs" | "py" | "java" | "cpp" | "c" | "go" | "rb" | "php"
        )
    } else {
        false
    }
}

fn determine_node_type(path: &Path) -> NodeType {
    if let Some(ext) = path.extension().and_then(|e| e.to_str()) {
        match ext {
            "jsx" | "tsx" => NodeType::Component,
            _ => NodeType::File,
        }
    } else {
        NodeType::File
    }
}

fn generate_node_id(path: &str) -> String {
    path.replace('/', "_")
        .replace('\\', "_")
        .replace('.', "_")
}

fn estimate_complexity(content: &str) -> u32 {
    // Simple complexity estimation based on control flow keywords
    let keywords = [
        "if", "else", "for", "while", "switch", "case", "catch", "&&", "||", "?", ":"
    ];
    
    let mut complexity = 1;
    for keyword in &keywords {
        complexity += content.matches(keyword).count() as u32;
    }
    
    complexity
}

fn extract_imports(content: &str, file_path: &Path) -> Vec<String> {
    let mut imports = Vec::new();
    
    // Extract based on file extension
    if let Some(ext) = file_path.extension().and_then(|e| e.to_str()) {
        match ext {
            "js" | "jsx" | "ts" | "tsx" => {
                // JavaScript/TypeScript imports
                for line in content.lines() {
                    if line.trim().starts_with("import") {
                        if let Some(from_idx) = line.find("from") {
                            if let Some(path) = line[from_idx + 4..]
                                .trim()
                                .trim_matches(|c| c == '\'' || c == '"' || c == ';')
                                .strip_prefix("./")
                                .or_else(|| line[from_idx + 4..]
                                    .trim()
                                    .trim_matches(|c| c == '\'' || c == '"' || c == ';')
                                    .strip_prefix("../"))
                            {
                                imports.push(path.to_string());
                            }
                        }
                    }
                }
            }
            "py" => {
                // Python imports
                for line in content.lines() {
                    if line.trim().starts_with("import") || line.trim().starts_with("from") {
                        let parts: Vec<&str> = line.split_whitespace().collect();
                        if parts.len() >= 2 {
                            imports.push(parts[1].replace('.', "/"));
                        }
                    }
                }
            }
            "rs" => {
                // Rust imports
                for line in content.lines() {
                    if line.trim().starts_with("use") {
                        let parts: Vec<&str> = line.split("::").collect();
                        if !parts.is_empty() {
                            imports.push(parts[0].replace("use ", "").trim().to_string());
                        }
                    }
                }
            }
            _ => {}
        }
    }
    
    imports
}

fn calculate_maintainability_index(lines: u32, complexity: u32) -> u32 {
    // Simplified maintainability index calculation
    let base = 171.0;
    let ln_lines = (lines as f64).ln();
    let ln_complexity = (complexity as f64).ln();
    
    let index = base - 5.2 * ln_lines - 0.23 * ln_complexity;
    let normalized = (index * 100.0 / 171.0).max(0.0).min(100.0);
    
    normalized as u32
}

fn get_last_modified(path: &Path) -> i64 {
    fs::metadata(path)
        .and_then(|m| m.modified())
        .map(|t| t.duration_since(std::time::UNIX_EPOCH).unwrap().as_secs() as i64)
        .unwrap_or(0)
}

fn get_git_author(_file_path: &Path, _project_path: &str) -> Option<String> {
    // In a real implementation, this would use git commands
    // For now, return None
    None
}

fn create_clusters(nodes: &[CodeNode], _project_path: &str) -> Vec<Cluster> {
    let mut clusters = Vec::new();
    let mut dir_nodes: HashMap<String, Vec<String>> = HashMap::new();
    
    // Group nodes by directory
    for node in nodes {
        if let Some(dir) = Path::new(&node.path).parent() {
            let dir_str = dir.to_string_lossy().to_string();
            dir_nodes
                .entry(dir_str)
                .or_insert_with(Vec::new)
                .push(node.id.clone());
        }
    }
    
    // Create clusters for directories with multiple files
    for (dir, node_ids) in dir_nodes {
        if node_ids.len() > 1 {
            let cluster_name = if dir.is_empty() {
                "Root".to_string()
            } else {
                dir.split('/').last().unwrap_or(&dir).to_string()
            };
            
            clusters.push(Cluster {
                id: format!("cluster_{}", dir.replace('/', "_")),
                name: cluster_name,
                nodes: node_ids,
                cluster_type: ClusterType::Module,
            });
        }
    }
    
    clusters
}