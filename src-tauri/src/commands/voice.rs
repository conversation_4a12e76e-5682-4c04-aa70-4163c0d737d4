use tauri::{command, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct VoicePermissionStatus {
    pub granted: bool,
    pub error: Option<String>,
}

#[command]
pub async fn check_microphone_permission() -> Result<VoicePermissionStatus, String> {
    // In Tauri, we rely on the webview's permission system
    // The actual permission check happens in the frontend
    Ok(VoicePermissionStatus {
        granted: true, // We'll let the frontend handle the actual check
        error: None,
    })
}

#[command]
pub async fn request_microphone_permission() -> Result<VoicePermissionStatus, String> {
    // For Tauri apps, microphone permissions are handled by the webview
    // We need to ensure the CSP allows media access
    Ok(VoicePermissionStatus {
        granted: true,
        error: None,
    })
}

#[command]
pub async fn get_voice_control_status() -> Result<bool, String> {
    // Check if voice control is available in this environment
    Ok(true)
}

#[command]
pub async fn enable_voice_debugging(app_handle: AppHandle) -> Result<(), String> {
    #[cfg(debug_assertions)]
    {
        // Enable devtools for debugging voice issues
        if let Some(window) = app_handle.get_webview_window("main") {
            window.open_devtools();
        }
    }
    Ok(())
}