# MCP Server Status Implementation

This document describes the implementation of the MCP server status checking functionality.

## Overview

The MCP Server Status implementation provides real-time monitoring and status checking for Model Context Protocol (MCP) servers. It includes:

- **Real process detection** for NPX-based and custom MCP servers
- **Caching mechanism** with configurable TTL for performance optimization
- **Background refresh** to keep status information current
- **Environment dependency checking** to identify missing variables
- **Enhanced UI integration** with status indicators and refresh controls

## Architecture

### Backend Components

#### 1. Status Cache System
- **Location**: `src-tauri/src/commands/mcp.rs`
- **Cache TTL**: 5 minutes (300 seconds)
- **Structure**: `HashMap<String, (ServerStatus, u64)>`
- **Thread-safe**: Uses `Arc<RwLock<>>`

#### 2. Process Detection
- **NPX Servers**: Detects Node.js processes with specific package names
- **Custom Servers**: Generic process detection using `ps aux` (Unix) or `wmic` (Windows)
- **Cross-platform**: Handles Windows, macOS, and Linux

#### 3. Environment Validation
- **Placeholder Detection**: Identifies `${VAR_NAME}` patterns
- **Environment Resolution**: Checks if required environment variables exist
- **Error Reporting**: Provides specific error messages for missing variables

### Frontend Components

#### 1. Enhanced Status Display
- **Location**: `src/components/MCPServerList.tsx`
- **Status Badges**: Running (green), Error (red), Stopped (yellow), Unknown (gray)
- **Detailed Information**: Shows error messages and last checked timestamps
- **Tooltips**: Hover information for error details

#### 2. Refresh Controls
- **Global Refresh**: Refreshes all server status
- **Individual Refresh**: Per-server status refresh buttons
- **Loading States**: Visual feedback during refresh operations
- **Auto-refresh**: Periodic background updates every 5 minutes

## API Endpoints

### mcp_get_server_status
- **Purpose**: Retrieves current status for all MCP servers
- **Caching**: Uses cached results when available
- **Response**: `HashMap<String, ServerStatus>`

### mcp_refresh_server_status
- **Purpose**: Forces refresh of all server status (clears cache)
- **Usage**: Manual refresh or when immediate updates are needed
- **Response**: `HashMap<String, ServerStatus>`

## Status Information

### ServerStatus Structure
```rust
pub struct ServerStatus {
    pub running: bool,           // Whether the server process is running
    pub error: Option<String>,   // Error message if any
    pub last_checked: Option<u64>, // Unix timestamp of last check
}
```

### Status Categories
1. **Running**: Process is actively running
2. **Stopped**: Process is not running but no errors detected
3. **Error**: Specific error condition (missing env vars, etc.)
4. **Unknown**: Status could not be determined

## Configuration

### Supported Server Types
- **NPX-based servers**: `npx @package/name`
- **Python servers**: `python -m package`
- **Custom commands**: Any executable with arguments
- **SSE servers**: Server-Sent Events transport

### Environment Variables
- **Placeholder format**: `${VARIABLE_NAME}`
- **Resolution**: Checks actual environment at runtime
- **Validation**: Reports missing required variables

## Performance Optimizations

### Caching Strategy
- **TTL-based**: 5-minute cache validity
- **Lazy loading**: Only checks status when requested
- **Background refresh**: Periodic updates to keep cache warm

### Process Detection
- **Efficient filtering**: Uses package names for NPX servers
- **Minimal system calls**: Batches process queries
- **Error handling**: Graceful fallback on system call failures

## UI Integration

### Status Indicators
- **Visual feedback**: Color-coded status badges
- **Contextual information**: Error messages and timestamps
- **Hover details**: Additional information on hover

### Refresh Controls
- **Header buttons**: Global refresh controls
- **Individual actions**: Per-server refresh buttons
- **Loading states**: Visual feedback during operations

## Error Handling

### Common Error Types
1. **Missing Environment Variables**: `Missing environment variable: VAR_NAME`
2. **Process Not Running**: `Process not running`
3. **System Call Failures**: Graceful fallback with warnings

### Error Recovery
- **Retry mechanism**: Automatic retries on system failures
- **Fallback behavior**: Continues operation with partial information
- **User feedback**: Clear error messages in UI

## Testing

### Manual Testing
1. **Start MCP servers**: Use `npx` to start configured servers
2. **Check status**: Verify running servers show "Running" status
3. **Stop servers**: Kill processes and verify "Stopped" status
4. **Environment errors**: Remove required env vars and check error status
5. **Refresh functionality**: Test manual and automatic refresh

### Integration Testing
1. **Full workflow**: Add server → Check status → Remove server
2. **Multiple servers**: Test with various server types
3. **Error conditions**: Test with missing dependencies
4. **Performance**: Test with large numbers of servers

## Deployment Notes

### System Requirements
- **Unix/Linux**: `ps` command available
- **Windows**: `wmic` command available
- **Node.js**: Required for NPX-based servers
- **Python**: Required for Python-based servers

### Configuration
- **Cache TTL**: Adjustable via `STATUS_CACHE_TTL` constant
- **Refresh interval**: Configurable in frontend (default: 5 minutes)
- **Process detection**: Customizable for different server types

## Future Enhancements

### Planned Features
1. **Health checks**: HTTP endpoint health monitoring
2. **Metrics collection**: Performance and usage statistics
3. **Alert system**: Notifications for server failures
4. **Auto-restart**: Automatic server recovery
5. **Configuration validation**: Pre-flight checks for server configs

### Technical Improvements
1. **Async process detection**: Non-blocking system calls
2. **Structured logging**: Better debugging and monitoring
3. **Configuration hot-reload**: Dynamic server configuration updates
4. **Plugin system**: Extensible server type support

## Troubleshooting

### Common Issues
1. **Status not updating**: Check cache TTL and refresh manually
2. **Process not detected**: Verify process name patterns
3. **Environment errors**: Check variable names and values
4. **Performance issues**: Monitor cache hit rates and refresh frequency

### Debug Information
- **Logs**: Check application logs for detailed error messages
- **Process list**: Manually verify running processes
- **Environment**: Check system environment variables
- **Cache state**: Monitor cache hit/miss ratios

## Conclusion

The MCP Server Status implementation provides a robust, performant solution for monitoring MCP servers with comprehensive error handling, caching, and user-friendly UI integration. The system is designed to be scalable and maintainable while providing real-time status information to users.