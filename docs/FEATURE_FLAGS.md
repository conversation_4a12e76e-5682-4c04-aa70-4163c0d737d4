# Feature Flags System

## Overview

Claudia Enhanced includes a comprehensive feature flags system that allows you to enable/disable features and configure their behavior. This is particularly useful for features that require external services or are still experimental.

## Accessing Feature Flags

1. Open Claudia Enhanced
2. Go to Settings (gear icon in the top bar)
3. Click on the "Features" tab

## Available Features

### 1. Collaborative Sessions
Real-time collaboration with team members.

**Settings:**
- **Enabled**: Toggle the feature on/off
- **Use Local Backend**: Use local development server instead of production
- **Local WebSocket URL**: WebSocket server URL (default: `ws://localhost:8080/ws`)
- **Local Share URL**: Share URL base (default: `http://localhost:3000/join`)

**Running a Demo Server:**
```bash
# Install dependencies (if not already installed)
npm install ws

# Run the demo server
node scripts/demo-collaboration-server.js

# Or with a custom port
WS_PORT=9090 node scripts/demo-collaboration-server.js
```

### 2. Voice Control
Control the app with voice commands.

**Requirements:**
- Google Chrome, Microsoft Edge, or Safari
- Microphone permissions

**Supported Commands:**
- "Run code review"
- "Show timeline"
- "Show settings"
- "Create checkpoint"
- "Send prompt [your message]"
- "Cancel execution"
- "Scroll to top/bottom"
- "Switch to opus/sonnet"

### 3. Code Flow Visualization
Visual representation of code execution flow.

### 4. Performance Profiler
Track API usage and performance metrics.

### 5. Smart Templates
AI-powered code templates and snippets.

### 6. Plugin System
Extend functionality with custom plugins.

### 7. Agent System
Specialized AI agents for different tasks.

### 8. MCP Marketplace
Browse and install MCP servers.

## Configuration Storage

Feature flags are stored in browser localStorage under the key `claudia-feature-flags`. They persist across sessions.

## Programmatic Access

You can also manage feature flags programmatically:

```typescript
import { getFeatureFlags, updateFeatureFlags, isFeatureEnabled } from '@/lib/featureFlags';

// Check if a feature is enabled
if (isFeatureEnabled('collaboration')) {
  // Feature is enabled
}

// Update feature flags
updateFeatureFlags({
  collaboration: {
    enabled: true,
    useLocalBackend: true
  }
});

// React hook usage
import { useFeatureFlags } from '@/lib/featureFlags';

function MyComponent() {
  const { flags, updateFlags, isEnabled } = useFeatureFlags();
  
  if (!isEnabled('voiceControl')) {
    return null; // Feature disabled
  }
  
  // Render component
}
```

## Troubleshooting

### Collaboration Not Working
1. Check if the feature is enabled in settings
2. Verify the WebSocket URL is correct
3. Ensure the backend server is running
4. Check browser console for errors
5. Try using local backend mode

### Voice Control Not Supported
1. Use a supported browser (Chrome, Edge, Safari)
2. Grant microphone permissions when prompted
3. Check if the feature is enabled in settings
4. Ensure you're using HTTPS or localhost

## Development Notes

When adding new features:

1. Add the feature to `defaultFeatureFlags` in `/src/lib/featureFlags.ts`
2. Add UI controls in `/src/components/FeatureFlagsSettings.tsx`
3. Check feature status before rendering components:
   ```typescript
   if (!flags.myFeature.enabled) {
     return null;
   }
   ```

## Security Considerations

- Feature flags are stored client-side and can be modified by users
- Don't use feature flags for security-critical decisions
- Always validate permissions server-side