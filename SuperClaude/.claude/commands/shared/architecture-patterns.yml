# Architecture Patterns & Design Knowledge
# Extracted architectural patterns for system design and development

@include universal-constants.yml#Universal_Legend

## Domain-Driven Design (DDD) Patterns

```yaml
DDD_Building_Blocks:
  Entities:
    Definition: "Objects w/ unique identity that persist over time"
    Characteristics: ["Unique identity", "Mutable state", "Business behavior"]
    Implementation: ["ID field", "Equality by ID", "Lifecycle management"]
    Examples: ["User", "Order", "Product", "Account"]
    
  Value_Objects:
    Definition: "Immutable objects defined by their attributes"
    Characteristics: ["No identity", "Immutable", "Replaceable"]
    Implementation: ["Equality by value", "No setters", "Validation in constructor"]
    Examples: ["Email", "Money", "Address", "DateRange"]
    
  Aggregates:
    Definition: "Consistency boundaries w/ aggregate roots"
    Characteristics: ["Transaction boundary", "Consistency rules", "Access via root"]
    Implementation: ["Aggregate root", "Internal entities", "Business invariants"]
    Examples: ["Order w/ LineItems", "Customer w/ Addresses"]
    
  Domain_Services:
    Definition: "Business logic that doesn't belong in entities"
    Characteristics: ["Stateless", "Domain operations", "Cross-entity logic"]
    Implementation: ["Pure functions", "Domain interfaces", "Business rules"]
    Examples: ["TransferService", "PricingService", "ValidationService"]
    
  Repositories:
    Definition: "Abstract data access for aggregates"
    Characteristics: ["Collection-like interface", "Persistence abstraction", "Aggregate-focused"]
    Implementation: ["Interface in domain", "Implementation in infrastructure", "Unit of work"]
    Examples: ["UserRepository", "OrderRepository", "ProductCatalog"]
    
  Domain_Events:
    Definition: "Capture significant business events"
    Characteristics: ["Past tense", "Immutable", "Business significance"]
    Implementation: ["Event publishing", "Event handlers", "Eventual consistency"]
    Examples: ["UserRegistered", "OrderPlaced", "PaymentProcessed"]
    
  Factories:
    Definition: "Complex object creation logic"
    Characteristics: ["Encapsulate creation", "Ensure invariants", "Hide complexity"]
    Implementation: ["Creation methods", "Builder patterns", "Validation"]
    Examples: ["OrderFactory", "UserFactory", "AggregateFactory"]
    
  Application_Services:
    Definition: "Orchestrate domain operations for use cases"
    Characteristics: ["Thin coordination layer", "Transaction boundaries", "DTO translation"]
    Implementation: ["Use case methods", "Domain orchestration", "Infrastructure coordination"]
    Examples: ["UserRegistrationService", "OrderProcessingService"]
```

## DDD Strategic Patterns

```yaml
Strategic_Design:
  Bounded_Contexts:
    Definition: "Clear boundaries for domain models"
    Purpose: ["Model isolation", "Team autonomy", "Technology independence"]
    Implementation: ["Context maps", "Anti-corruption layers", "Published language"]
    Examples: ["Sales Context", "Inventory Context", "Billing Context"]
    
  Context_Mapping:
    Patterns:
      Shared_Kernel: "Shared code between contexts"
      Customer_Supplier: "Upstream/downstream relationship"
      Conformist: "Downstream conforms to upstream"
      Anti_Corruption_Layer: "Translation layer for external systems"
      Open_Host_Service: "Published API for multiple consumers"
      Published_Language: "Common schema/protocol"
      Separate_Ways: "No integration between contexts"
      Big_Ball_of_Mud: "Legacy system integration"
    
  Ubiquitous_Language:
    Definition: "Common language between domain experts & developers"
    Implementation: ["Domain glossary", "Code naming", "Documentation"]
    Maintenance: ["Regular refinement", "Feedback loops", "Continuous alignment"]
    
  Event_Driven_Architecture:
    Patterns: ["Domain events", "Event sourcing", "CQRS", "Saga patterns"]
    Implementation: ["Event store", "Event handlers", "Read models", "Process managers"]
    Benefits: ["Loose coupling", "Scalability", "Audit trail", "Temporal decoupling"]
```

## API Design Patterns

```yaml
REST_API_Patterns:
  Resource_Design:
    Principles: ["Resource-oriented URLs", "Nouns not verbs", "Hierarchical structure"]
    URL_Structure: ["Collection: /users", "Resource: /users/123", "Sub-resource: /users/123/orders"]
    HTTP_Verbs: ["GET (read)", "POST (create)", "PUT (update/replace)", "PATCH (partial update)", "DELETE (remove)"]
    
  Response_Design:
    Status_Codes:
      Success: ["200 OK", "201 Created", "202 Accepted", "204 No Content"]
      Client_Error: ["400 Bad Request", "401 Unauthorized", "403 Forbidden", "404 Not Found", "409 Conflict"]
      Server_Error: ["500 Internal Server Error", "502 Bad Gateway", "503 Service Unavailable"]
    
    Response_Format:
      JSON_Structure: ["Consistent format", "Error objects", "Metadata inclusion"]
      Pagination: ["Offset/limit", "Cursor-based", "Link headers"]
      Filtering: ["Query parameters", "Field selection", "Search syntax"]
      
  API_Evolution:
    Versioning_Strategies:
      URL_Versioning: "/v1/users", "/v2/users"
      Header_Versioning: "Accept: application/vnd.api+json;version=1"
      Parameter_Versioning: "/users?version=1"
    
    Backward_Compatibility: ["Additive changes", "Optional fields", "Deprecation notices"]
    
  Security_Patterns:
    Authentication: ["JWT tokens", "OAuth 2.0", "API keys", "Basic auth"]
    Authorization: ["Role-based", "Permission-based", "Resource-based", "Attribute-based"]
    Rate_Limiting: ["Fixed window", "Sliding window", "Token bucket", "Leaky bucket"]
    
  HATEOAS:
    Definition: "Hypermedia as the Engine of Application State"
    Implementation: ["Link relations", "Resource navigation", "State transitions"]
    Benefits: ["Discoverability", "Loose coupling", "Evolvability"]
```

## GraphQL Patterns

```yaml
GraphQL_Design:
  Schema_Design:
    Type_System: ["Scalar types", "Object types", "Interface types", "Union types", "Enum types"]
    Field_Design: ["Nullable vs non-null", "Field arguments", "Default values"]
    Schema_Stitching: ["Multiple services", "Schema federation", "Gateway patterns"]
    
  Query_Patterns:
    Query_Structure: ["Nested queries", "Field selection", "Fragment usage"]
    Variables: ["Typed variables", "Default values", "Directive usage"]
    Batching: ["DataLoader patterns", "Query batching", "Request coalescing"]
    
  Mutation_Patterns:
    Mutation_Design: ["Input types", "Payload types", "Error handling"]
    Optimistic_Updates: ["Client-side updates", "Rollback strategies"]
    Bulk_Operations: ["Multiple mutations", "Transaction boundaries"]
    
  Subscription_Patterns:
    Real_Time: ["WebSocket connections", "Event-driven updates", "Subscription filtering"]
    Scalability: ["Connection management", "Memory usage", "Resource cleanup"]
    
  Performance_Optimization:
    N_Plus_1_Prevention: ["DataLoader", "Query optimization", "Eager loading"]
    Query_Complexity: ["Depth limiting", "Cost analysis", "Query timeout"]
    Caching: ["Field-level caching", "Query result caching", "CDN integration"]
```

## Microservices Architecture Patterns

```yaml
Service_Design:
  Service_Boundaries:
    Principles: ["Single responsibility", "Business capability alignment", "Data ownership"]
    Decomposition: ["Domain-driven boundaries", "Team topology", "Data flow analysis"]
    Size_Guidelines: ["Small enough to rewrite", "Large enough to be useful", "Team ownership"]
    
  Communication_Patterns:
    Synchronous: ["HTTP/REST", "gRPC", "GraphQL"]
    Asynchronous: ["Message queues", "Event streaming", "Pub/Sub"]
    Data_Consistency: ["Eventual consistency", "Saga patterns", "Distributed transactions"]
    
  Data_Management:
    Database_Per_Service: ["Data isolation", "Technology choice", "Schema evolution"]
    Data_Synchronization: ["Event sourcing", "Change data capture", "API composition"]
    
  Deployment_Patterns:
    Containerization: ["Docker", "Container orchestration", "Service mesh"]
    CI_CD: ["Pipeline per service", "Independent deployment", "Blue-green deployment"]
    
Service_Discovery:
  Patterns: ["Service registry", "Client-side discovery", "Server-side discovery"]
  Implementation: ["Consul", "Eureka", "Kubernetes DNS", "API Gateway"]
  
Circuit_Breaker:
  Pattern: "Prevent cascade failures by failing fast"
  States: ["Closed (normal)", "Open (failing)", "Half-open (testing)"]
  Implementation: ["Failure threshold", "Timeout", "Recovery testing"]
  
Bulkhead_Pattern:
  Purpose: "Isolate resources to prevent total system failure"
  Implementation: ["Thread pools", "Connection pools", "Resource isolation"]
```

## Event-Driven Architecture Patterns

```yaml
Event_Patterns:
  Event_Types:
    Domain_Events: "Business-significant occurrences"
    Integration_Events: "Cross-service communication"
    System_Events: "Technical/infrastructure events"
    
  Event_Design:
    Structure: ["Event ID", "Timestamp", "Event type", "Payload", "Metadata"]
    Naming: ["Past tense", "Business language", "Specific actions"]
    Versioning: ["Schema evolution", "Backward compatibility", "Event migration"]
    
  Event_Sourcing:
    Concept: "Store events as primary source of truth"
    Implementation: ["Event store", "Event replay", "Snapshots", "Projections"]
    Benefits: ["Audit trail", "Temporal queries", "Debugging", "Analytics"]
    
  CQRS:
    Pattern: "Command Query Responsibility Segregation"
    Implementation: ["Separate models", "Read/write databases", "Event-driven sync"]
    Benefits: ["Optimized reads", "Scalable writes", "Complex queries"]
    
  Saga_Pattern:
    Purpose: "Manage distributed transactions"
    Types: ["Orchestration", "Choreography"]
    Implementation: ["Compensation actions", "State machines", "Event coordination"]
```

## Layered Architecture Patterns

```yaml
Clean_Architecture:
  Layers:
    Domain: "Core business logic & entities"
    Application: "Use cases & orchestration"
    Infrastructure: "External concerns & frameworks"
    Presentation: "UI/API & user interfaces"
    
  Dependency_Rules:
    Direction: "Dependencies point inward toward domain"
    Abstraction: "Inner layers define interfaces"
    Implementation: "Outer layers provide implementations"
    
Hexagonal_Architecture:
  Core: "Application core w/ business logic"
  Ports: "Interfaces for external communication"
  Adapters: "Implementations of port interfaces"
  Benefits: ["Testability", "Technology independence", "Maintainability"]
  
Onion_Architecture:
  Center: "Domain model"
  Layers: ["Domain services", "Application services", "Infrastructure"]
  Principles: ["Dependency inversion", "Separation of concerns", "Testability"]
```

## Scalability Patterns

```yaml
Horizontal_Scaling:
  Load_Balancing:
    Types: ["Round robin", "Least connections", "IP hash", "Geographic"]
    Implementation: ["Load balancers", "Service mesh", "DNS-based"]
    
  Database_Scaling:
    Read_Replicas: ["Master-slave replication", "Read-only queries", "Consistency trade-offs"]
    Sharding: ["Horizontal partitioning", "Shard keys", "Cross-shard queries"]
    
Vertical_Scaling:
  Resource_Optimization: ["CPU scaling", "Memory scaling", "Storage scaling"]
  Limits: ["Hardware constraints", "Cost implications", "Single point of failure"]
  
Caching_Strategies:
  Levels:
    Application: ["In-memory cache", "Application state", "Computed results"]
    Database: ["Query result cache", "Connection pooling", "Statement caching"]
    CDN: ["Static content", "Geographic distribution", "Edge caching"]
    
  Patterns:
    Cache_Aside: "Application manages cache"
    Write_Through: "Write to cache & database"
    Write_Behind: "Asynchronous database writes"
    Refresh_Ahead: "Proactive cache refresh"
```

## Integration Patterns

```yaml
Integration_Styles:
  File_Transfer: ["Batch processing", "Scheduled transfers", "File formats"]
  Shared_Database: ["Common schema", "Data ownership", "Consistency issues"]
  Remote_Procedure: ["Synchronous calls", "Strong coupling", "Blocking behavior"]
  Messaging: ["Asynchronous", "Loose coupling", "Event-driven"]
  
Message_Patterns:
  Point_to_Point: ["Queue-based", "Single consumer", "Load balancing"]
  Publish_Subscribe: ["Topic-based", "Multiple consumers", "Event broadcasting"]
  Request_Reply: ["Correlation IDs", "Response routing", "Timeout handling"]
  
API_Gateway:
  Functions: ["Request routing", "Authentication", "Rate limiting", "Response transformation"]
  Benefits: ["Single entry point", "Cross-cutting concerns", "Protocol translation"]
  Patterns: ["Backend for frontend", "Micro-gateway", "Service mesh integration"]
```

## Quality Attribute Patterns

```yaml
Performance_Patterns:
  Response_Time: ["Caching", "CDN", "Database optimization", "Algorithm optimization"]
  Throughput: ["Load balancing", "Horizontal scaling", "Resource pooling"]
  Resource_Utilization: ["Connection pooling", "Thread management", "Memory optimization"]
  
Reliability_Patterns:
  Fault_Tolerance: ["Circuit breaker", "Bulkhead", "Timeout", "Retry with backoff"]
  Recovery: ["Health checks", "Graceful degradation", "Failover", "Self-healing"]
  Monitoring: ["Metrics collection", "Alerting", "Distributed tracing", "Log aggregation"]
  
Security_Patterns:
  Authentication: ["Multi-factor", "Single sign-on", "Token-based", "Certificate-based"]
  Authorization: ["RBAC", "ABAC", "OAuth", "JWT"]
  Data_Protection: ["Encryption at rest", "Encryption in transit", "Key management"]
  
Maintainability_Patterns:
  Modularity: ["Loose coupling", "High cohesion", "Interface segregation"]
  Testability: ["Dependency injection", "Mock objects", "Test doubles"]
  Documentation: ["Living documentation", "Architecture decision records", "API documentation"]
```

## PRD Templates

```yaml
PRD_Templates:
  Executive_Overview:
    Problem_Statement: "Clear description of problem being solved"
    Solution_Overview: "High-level approach to solution"
    Expected_Impact: "Business value & measurable outcomes"
    Key_Stakeholders: "Primary users, decision makers, affected teams"
    
  Goals_Success_Metrics:
    Primary_Objectives: "Must-have goals for success"
    Secondary_Goals: "Nice-to-have improvements"
    Success_KPIs: "Measurable key performance indicators"
    Measurement_Plan: "How & when metrics will be tracked"
    
  User_Stories_Requirements:
    User_Personas: "Target user profiles & characteristics"
    User_Journeys: "Key user workflows & interactions"
    Functional_Requirements: "Core system capabilities"
    Non_Functional_Requirements: "Performance, security, scalability needs"
    Acceptance_Criteria: "Definition of done for features"
    
  Technical_Specifications:
    Architecture_Overview: "High-level system design"
    Technology_Choices: "Selected frameworks, languages, tools"
    Integration_Points: "External systems & APIs"
    Security_Requirements: "Auth, data protection, compliance needs"
    Performance_Targets: "Response times, throughput, availability"
    
  Timeline_Risks:
    Development_Phases: "Major milestones & deliverables"
    Dependencies_Blockers: "External dependencies & potential blockers"
    Risk_Assessment: "Technical, resource & timeline risks"
    Mitigation_Strategies: "Plans to address identified risks"
    
  Templates_By_Type:
    Feature_PRD: "New feature development template"
    API_PRD: "API product requirements template"
    Integration_PRD: "System integration requirements"
    Migration_PRD: "System migration & modernization"
```

## API Design Patterns

```yaml
API_Design_Patterns:
  REST_Design:
    Resource_Identification:
      URI_Design: "Nouns not verbs | /users/123 not /getUser/123"
      Hierarchy: "Logical resource relationships | /users/123/orders/456"
      Consistency: "Consistent naming conventions | plural nouns"
      
    HTTP_Methods:
      GET: "Retrieve resources | Safe & idempotent | No side effects"
      POST: "Create new resources | Non-idempotent | Returns 201 Created"
      PUT: "Update entire resource | Idempotent | Returns 200 or 204"
      PATCH: "Partial resource update | May be idempotent | Returns 200"
      DELETE: "Remove resources | Idempotent | Returns 204 No Content"
      
    Response_Patterns:
      Status_Codes: "200 OK | 201 Created | 400 Bad Request | 401 Unauthorized | 404 Not Found | 500 Internal Error"
      Error_Format: "Consistent error structure | Code, message, details"
      Pagination: "Offset/limit or cursor-based | Include metadata"
      
    Content_Negotiation:
      Accept_Headers: "application/json | application/xml | text/html"
      Versioning: "URI path (/v1/) | Header (Accept: application/vnd.api+json;version=1)"
      
  GraphQL_Design:
    Schema_Design:
      Types: "Strong typing | Scalar, Object, Interface, Union, Enum"
      Queries: "Read operations | Nested field selection | Efficient data fetching"
      Mutations: "Write operations | Input types | Clear return types"
      Subscriptions: "Real-time updates | Event-driven | Resource efficient"
      
    Performance_Patterns:
      DataLoader: "Batch & cache database queries | Solve N+1 problem"
      Query_Complexity: "Depth limiting | Cost analysis | Rate limiting"
      Caching: "Field-level caching | Query result caching"
      
  Authentication_Authorization:
    JWT_Patterns:
      Structure: "Header.Payload.Signature | Stateless tokens"
      Claims: "Standard (iss, exp, aud) | Custom business claims"
      Security: "Strong secrets | Token rotation | Expiry management"
      
    OAuth2_Flow:
      Authorization_Code: "Web applications | Server-side token exchange"
      Client_Credentials: "Service-to-service | Machine authentication"
      Resource_Owner: "Username/password | Not recommended for new APIs"
      
    API_Keys:
      Usage: "Simple authentication | Rate limiting | Usage tracking"
      Security: "Rotate regularly | Environment-specific | Never in code"
      
  Rate_Limiting:
    Strategies:
      Fixed_Window: "Simple implementation | Reset at fixed intervals"
      Sliding_Window: "More accurate | Higher memory usage"
      Token_Bucket: "Burst handling | Smooth rate limiting"
      
    Implementation:
      Headers: "X-RateLimit-Limit | X-RateLimit-Remaining | X-RateLimit-Reset"
      Response: "429 Too Many Requests | Retry-After header"
      
  API_Documentation:
    OpenAPI_Specification:
      Structure: "Paths, components, security, info"
      Examples: "Request/response examples | Error scenarios"
      Validation: "Schema validation | Parameter constraints"
      
    Documentation_Standards:
      Completeness: "All endpoints documented | Examples provided"
      Clarity: "Clear descriptions | Use cases explained"
      Maintenance: "Keep docs synchronized with code"
```

## DDD Patterns

```yaml
DDD_Patterns:
  Strategic_Design:
    Domain_Modeling:
      Ubiquitous_Language: "Shared vocabulary between domain experts & developers"
      Bounded_Context: "Explicit boundaries where model is defined & applicable"
      Context_Mapping: "Relationships between bounded contexts"
      
    Context_Boundaries:
      Identification: "Language changes | Team ownership | Data ownership"
      Integration_Patterns: "Shared kernel | Customer/supplier | Conformist"
      Anti_Corruption_Layer: "Protect domain from external influences"
      
  Tactical_Design:
    Building_Blocks:
      Entities: "Objects with unique identity | Mutable | Business behavior"
      Value_Objects: "Immutable objects defined by attributes | No identity"
      Aggregates: "Consistency boundaries | Transaction boundaries | Access via root"
      Domain_Services: "Business logic that doesn't belong in entities"
      
    Aggregate_Design:
      Root_Entity: "Single entry point | Maintains invariants"
      Size_Guidelines: "Small aggregates | Minimize transaction scope"
      Reference_Style: "Reference by ID across aggregates"
      
    Repository_Pattern:
      Purpose: "Encapsulate data access logic | Domain-focused interface"
      Interface: "Domain layer defines interface | Infrastructure implements"
      Collections: "Act like in-memory collections | Hide persistence details"
      
  Event_Driven_DDD:
    Domain_Events:
      Definition: "Something important that happened in the domain"
      Characteristics: "Immutable | Past tense | Rich with business context"
      Publishing: "Aggregate publishes events | Infrastructure handles delivery"
      
    Event_Sourcing:
      Concept: "Store events, not current state | Rebuild state from events"
      Benefits: "Audit trail | Temporal queries | Replay capabilities"
      Challenges: "Event versioning | Snapshot strategies | Query complexity"
      
    CQRS_Pattern:
      Separation: "Command side (writes) separate from query side (reads)"
      Benefits: "Optimized models | Independent scaling | Clear responsibility"
      Implementation: "Separate models | Event synchronization | Eventual consistency"
      
  Implementation_Patterns:
    Layered_Architecture:
      Domain_Layer: "Core business logic | No dependencies on other layers"
      Application_Layer: "Use cases | Orchestrates domain objects"
      Infrastructure_Layer: "External concerns | Database, web, messaging"
      
    Hexagonal_Architecture:
      Core_Principle: "Domain at center | Adapters for external concerns"
      Ports: "Interfaces defined by domain | Input/output boundaries"
      Adapters: "Implementations of ports | Framework-specific code"
```

---
*Architecture Patterns v2 - Comprehensive architectural knowledge patterns for SuperClaude design commands*
