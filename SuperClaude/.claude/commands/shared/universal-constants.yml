# Universal Constants & Shared Values
# Single source of truth for all legends, symbols, paths, and common constants

## Universal_Legend
| Symbol | Meaning | | Abbrev | Meaning |
|--------|---------|---|--------|---------|
| → | leads to | | cfg | configuration |
| & | and/with | | impl | implementation |
| w/ | with | | perf | performance |
| @ | at/located | | ops | operations |
| > | greater than | | val | validation |
| ∀ | for all/every | | req | requirements |
| ∃ | exists/there is | | deps | dependencies |
| ∴ | therefore | | env | environment |
| ∵ | because | | db | database |
| ≡ | equivalent | | api | interface |
| ≈ | approximately | | docs | documentation |
| 📁 | directory/path | | std | standard |
| 🔢 | number/count | | def | default |
| 📝 | text/string | | ctx | context |
| ⚙ | setting/config | | err | error |
| 🎛 | control/flags | | exec | execution |
| 🔧 | configuration | | qual | quality |
| 📋 | group/category | | rec | recovery |
| 🚨 | critical/urgent | | sev | severity |
| ⚠ | warning/caution | | resp | response |
| 🔄 | retry/recovery | | esc | escalation |
| ✅ | success/fixed | | tok | token |
| ❌ | failure/error | | opt | optimization |
| ℹ | information | | UX | user experience |
| ⚡ | fast/quick | | UI | user interface |
| 🐌 | slow/delayed | | C | critical |
| ✨ | complete/done | | H | high |
| 📖 | read operation | | M | medium |
| ✏ | edit operation | | L | low |
| 🗑 | delete operation | | |

## Universal Symbols & Process Flow

```yaml
Process_Flow:
  Leads_To: "→"
  Combine: "&"
  With: "w/"
  At_Location: "@"
  Greater_Than: ">"
  For_All: "∀"
  Exists: "∃"
  Therefore: "∴"
  Because: "∵"
  Equivalent: "≡"
  Approximately: "≈"
  
Status_Indicators:
  Success: "✅"
  Failure: "❌"
  Warning: "⚠"
  Information: "ℹ"
  Critical: "🚨"
  Fast: "⚡"
  Slow: "🐌"
  Progress: "🔄"
  Complete: "✨"
  
File_Operations:
  Read: "📖"
  Write: "📝"
  Edit: "✏"
  Delete: "🗑"
  Copy: "📋"
  Move: "➡"
  Create: "➕"
  Directory: "📁"
  
Control_Elements:
  Controls: "🎛"
  Configuration: "🔧"
  Settings: "⚙"
  Group: "📋"
  Count: "🔢"
  Text: "📝"
```

## Standard Paths & Directories

```yaml
Documentation_Paths:
  Claude_Docs: ".claudedocs/"
  Reports: ".claudedocs/reports/"
  Metrics: ".claudedocs/metrics/"
  Summaries: ".claudedocs/summaries/"
  Checkpoints: ".claudedocs/checkpoints/"
  Tasks: ".claudedocs/tasks/"
  Task_Pending: ".claudedocs/tasks/pending/"
  Task_In_Progress: ".claudedocs/tasks/in-progress/"
  Task_Completed: ".claudedocs/tasks/completed/"
  Task_Cancelled: ".claudedocs/tasks/cancelled/"
  
Project_Documentation:
  Documentation: "docs/"
  API_Docs: "docs/api/"
  User_Docs: "docs/user/"
  Developer_Docs: "docs/dev/"
  
Build_Artifacts:
  Build_Dir: "build/"
  Dist_Dir: "dist/"
  Output_Dir: "out/"
  Next_Dir: ".next/"
  Temp_Dir: "tmp/"
  Cache_Dir: ".cache/"
  Node_Modules: "node_modules/"
  
Git_Paths:
  Git_Dir: ".git/"
  Git_Hooks: ".git/hooks/"
  Git_Config: ".git/config"
  Gitignore: ".gitignore"
```

## Standard Abbreviations & Technical Terms

```yaml
Technical_Terms:
  Configuration: "cfg"
  Implementation: "impl"
  Performance: "perf"
  Validation: "val"
  Requirements: "req"
  Dependencies: "deps"
  Environment: "env"
  Database: "db"
  Interface: "api"
  Documentation: "docs"
  Operations: "ops"
  Standard: "std"
  Default: "def"
  Context: "ctx"
  Error: "err"
  Recovery: "rec"
  Execution: "exec"
  Token: "tok"
  Optimization: "opt"
  Quality: "qual"
  Severity: "sev"
  Response: "resp"
  Escalation: "esc"
  
Action_Abbreviations:
  Analyze: "anlz"
  Build: "bld"
  Deploy: "dply"
  Test: "tst"
  Configure: "cfg"
  Implement: "impl"
  Validate: "val"
  Execute: "exec"
  Optimize: "opt"
  
Severity_Levels:
  Critical: "C"
  High: "H"
  Medium: "M"
  Low: "L"
```

## Standard Time & Size Limits

```yaml
Time_Limits:
  Quick_Operation: "Fast"
  Standard_Operation: "Moderate duration"
  Long_Operation: "Extended duration"
  Critical_Timeout: "Maximum allowed duration"
  Session_Timeout: "Extended session limit"
  
Token_Limits:
  Small_Response: "Minimal usage"
  Medium_Response: "Moderate usage"
  Large_Response: "Extensive usage"
  Context_Warning: "High context usage"
  Context_Critical: "Very high context usage"
  
File_Size_Limits:
  Small_File: "Minimal size"
  Medium_File: "Moderate size"
  Large_File: "Large size"
  Huge_File: "Very large size"
  Max_Analysis: "Maximum analyzable size"
  
Retry_Limits:
  Default_Retries: "Multiple attempts"
  Network_Retries: "Several attempts"
  File_Lock_Retries: "Multiple attempts"
  MCP_Server_Retries: "Multiple attempts"
  Max_Consecutive_Failures: "Several failures"
```

## Standard Priority & Status Values

```yaml
Priority_Levels:
  Critical: "critical"
  High: "high"
  Medium: "medium"
  Low: "low"
  
Severity_Numbers:
  Critical: "Maximum severity"
  High: "High severity"
  Medium: "Medium severity"
  Low: "Low severity"
  
Status_Values:
  Pending: "pending"
  In_Progress: "in_progress"
  Completed: "completed"
  Failed: "failed"
  Cancelled: "cancelled"
  Paused: "paused"
```

## Standard Tool Names & Commands

```yaml
Native_Tools:
  File_Tools: ["Read", "Write", "Edit", "MultiEdit", "Glob"]
  System_Tools: ["Bash", "LS"]
  Search_Tools: ["Grep", "Task"]
  Notebook_Tools: ["NotebookRead", "NotebookEdit"]
  Web_Tools: ["WebFetch", "WebSearch"]
  Task_Tools: ["TodoRead", "TodoWrite"]
  
MCP_Servers:
  Context7: "mcp__context7__*"
  Sequential: "mcp__sequential-thinking__*"
  Magic: "mcp__magic__*"
  Puppeteer: "mcp__puppeteer__*"
  
Universal_Flags:
  Planning: "--plan"
  Thinking: ["--think", "--think-hard", "--ultrathink"]
  Compression: ["--uc", "--ultracompressed"]
  MCP_Control: ["--c7", "--seq", "--magic", "--pup", "--all-mcp", "--no-mcp"]
  Execution: ["--dry-run", "--watch", "--interactive", "--force"]
  Quality: ["--tdd", "--iterate", "--threshold", "--validate", "--security"]
  
Common_Commands:
  Git_Commands: ["status", "add", "commit", "push", "pull", "checkout", "branch", "merge"]
  Build_Commands: ["build", "test", "lint", "format", "typecheck"]
  Package_Commands: ["install", "update", "audit", "outdated"]
```

## Standard_Messages_Templates

```yaml
Success_Messages:
  Operation_Complete: "✅ {operation} completed successfully"
  File_Created: "📝 Created: {file_path}"
  File_Updated: "✏ Updated: {file_path}"
  Task_Complete: "✨ Task completed: {task_title}"
  
Warning_Messages:
  Performance_Warning: "⚠ Operation taking longer than expected"
  Context_Warning: "⚠ Context size approaching limit"
  Token_Warning: "⚠ High token usage detected"
  Fallback_Warning: "⚠ Using fallback method"
  
Error_Messages:
  File_Not_Found: "❌ File not found: {file_path}"
  Permission_Denied: "❌ Permission denied: {operation}"
  Operation_Failed: "❌ {operation} failed: {reason}"
  Timeout_Error: "❌ Operation timed out after {duration}"
  
Info_Messages:
  Operation_Started: "▶ Starting {operation}"
  Checkpoint_Created: "💾 Checkpoint created: {checkpoint_id}"
  Optimization_Applied: "⚡ Optimization applied: {optimization}"
  Fallback_Used: "🔄 Using alternative approach: {method}"
  
Report_References:
  Report_Saved: "📄 Report saved to: {path}"
  Metrics_Updated: "📊 Metrics updated: {path}"
  Log_Entry: "📝 Logged to: {path}"
  Checkpoint_Reference: "🔖 Checkpoint: {id}"
```

## File Naming Conventions

```yaml
Report_Files:
  Analysis_Report: "analysis-{type}-{timestamp}.md"
  Performance_Report: "performance-{date}.md"
  Security_Report: "security-scan-{timestamp}.md"
  Daily_Summary: "daily-summary-{YYYY-MM-DD}.md"
  Weekly_Trends: "weekly-trends-{YYYY-WW}.md"
  Monthly_Insights: "monthly-insights-{YYYY-MM}.md"
  
Task_Files:
  Task_Format: "{type}-{id}-{slug}.md"
  Task_ID_Format: "YYYYMMDD-HHMMSS"
  Task_Types: ["feature", "bugfix", "refactor", "docs", "test"]
  
Git_Branches:
  Task_Branch: "task/{id}-{slug}"
  Feature_Branch: "feature/{name}"
  Bugfix_Branch: "bugfix/{name}"
  Release_Branch: "release/{version}"
  
Log_Files:
  Performance_Log: "performance-{YYYY-MM-DD}.jsonl"
  Error_Log: "errors-{YYYY-MM-DD}.log"
  Audit_Log: "audit-{YYYY-MM-DD}.log"
  Debug_Log: "debug-{timestamp}.log"
```

## Environment & Framework Constants

```yaml
Environment_Types:
  Development: "dev"
  Testing: "test"
  Staging: "staging"
  Production: "prod"
  Local: "local"
  
File_Extensions:
  Markdown: [".md", ".markdown"]
  Code: [".js", ".ts", ".tsx", ".jsx", ".py", ".go", ".rs", ".cpp", ".c", ".java"]
  Config: [".json", ".yml", ".yaml", ".toml", ".ini", ".env"]
  Documentation: [".md", ".rst", ".txt", ".adoc"]
  
Supported_Frameworks:
  Frontend: ["React", "Vue", "Angular", "Svelte", "Next.js", "Nuxt.js"]
  Backend: ["Express", "FastAPI", "Django", "Rails", "Spring", "Gin"]
  Database: ["PostgreSQL", "MySQL", "MongoDB", "Redis", "SQLite"]
  Testing: ["Jest", "Mocha", "Pytest", "JUnit", "Cypress", "Playwright"]
```

## Reference System

```yaml
File_Organization:
  Constants_File: "shared/universal-constants.yml"
  Patterns_Directory: "shared/*.yml"
  Commands_Directory: "commands/*.md"
  
Common_References:
  Legend: "See Universal Legend section above"
  Symbols: "See Process Flow section above"
  Paths: "See Documentation Paths section above"
  Limits: "See Time Limits section above"
  Messages: "See Success Messages section above"
  Flags: "@include universal-constants.yml#Universal_Flags"
  
Usage_Examples:
  Command_Header: |
    @include universal-constants.yml#Universal_Legend
    @include universal-constants.yml#Process_Flow
  Report_Location: |
    Reports: @include universal-constants.yml#Documentation_Paths.Reports
  Success_Format: |
    @include universal-constants.yml#Success_Messages.Operation_Complete
```

---
*Universal Constants v2 - Single source of truth for all SuperClaude shared values*
