# Pull Request

## Legend
| Symbol | Meaning | | Abbrev | Meaning |
|--------|---------|---|--------|---------|
| → | leads to | | cfg | configuration |
| ✓ | completed | | PR | pull request |

## Summary
Brief description of changes

## Type of Change
- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🔧 Configuration change
- [ ] 🎨 Code style/formatting change
- [ ] ♻️ Refactoring (no functional changes)
- [ ] ⚡ Performance improvement
- [ ] 🔒 Security fix

## Changes Made
- Change 1
- Change 2
- Change 3

## Testing
- [ ] Tested install.sh on clean system
- [ ] Verified slash commands work in Claude Code
- [ ] Checked YAML syntax validity
- [ ] Tested persona functionality
- [ ] Tested MCP integration
- [ ] Manual testing performed
- [ ] All existing tests pass

## Related Issues
Fixes #(issue number)
Closes #(issue number)
Related to #(issue number)

## Screenshots (if applicable)
<!-- Add screenshots to demonstrate visual changes -->

## Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Additional Notes
<!-- Any additional information, concerns, or notes for reviewers -->

## Breaking Changes
<!-- If this is a breaking change, describe what users need to do to migrate -->

---
**Ready for review!** 🚀