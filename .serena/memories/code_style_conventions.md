# Code Style and Conventions

## TypeScript/React Conventions

### Component Structure
- Components use functional components with TypeScript
- Props interfaces are defined with `interface ComponentNameProps`
- Components are exported with named exports
- State management uses React hooks (useState, useEffect, etc.)
- Custom hooks are in `src/hooks/` directory

### Naming Conventions
- Components: PascalCase (e.g., `SmartTemplates`, `FeatureFlagsSettings`)
- Files: Component files match component names (e.g., `SmartTemplates.tsx`)
- Interfaces: PascalCase with descriptive names
- Props interfaces: ComponentName + "Props" suffix
- Custom hooks: camelCase with "use" prefix (e.g., `useFeatureFlags`)

### Type System
- Strict TypeScript with explicit types
- Interface definitions for all props and data structures
- Z<PERSON> for runtime validation where needed
- Avoid `any` type - use proper typing

### UI Components
- Uses shadcn/ui components from `@/components/ui/`
- Tailwind CSS for styling (v4)
- Lucide React for icons
- Framer Motion for animations
- Class variance authority (CVA) for component variants

### File Organization
- Components are self-contained in single files
- Related components grouped in subdirectories
- Shared utilities in `src/lib/`
- Type definitions inline or in dedicated files

## Rust Conventions

### Backend Structure
- Commands in `src-tauri/src/commands/`
- Process management in `src-tauri/src/process/`
- Uses Tauri 2 patterns for IPC

### Error Handling
- Proper error types and Result returns
- Graceful error handling with user-friendly messages

## General Practices
- No console.log in production code
- Proper error boundaries for React components
- Accessibility considerations (ARIA labels, keyboard navigation)
- Responsive design with Tailwind utilities
- Dark/light theme support via CSS variables