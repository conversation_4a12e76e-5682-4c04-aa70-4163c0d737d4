# Claudia Enhanced Project Overview

**Purpose**: A powerful desktop GUI application for Claude Code, providing a visual interface for managing Claude Code sessions, creating custom agents, tracking usage, and offering advanced features.

**Tech Stack**:
- Frontend: React 18 + TypeScript + Vite 6
- Backend: Rust with Tauri 2
- UI Framework: Tailwind CSS v4 + shadcn/ui components
- Database: SQLite (via rusqlite)
- Package Manager: Bun
- State Management: React hooks and context
- Build Tools: Vite for frontend, Cargo for Rust backend

**Key Features**:
1. Project & Session Management
2. CC Agents (Custom AI agents)
3. AI-Powered Code Review
4. Voice Control Integration
5. Real-time Collaboration
6. Smart Templates System
7. Visual Code Flow Representation
8. Performance Profiler
9. Advanced Theme System
10. Plugin Manager
11. MCP Server Management
12. Feature Flags System

**Project Structure**:
```
claudia/
├── src/                   # React frontend
│   ├── components/        # UI components (50+ components)
│   ├── lib/               # API client & utilities
│   └── assets/            # Static assets
├── src-tauri/             # Rust backend
│   ├── src/
│   │   ├── commands/      # Tauri command handlers
│   │   ├── checkpoint/    # Timeline management
│   │   └── process/       # Process management
│   └── tests/             # Rust test suite
└── public/                # Public assets
```

**Current State**: Enhanced version with 8 major new features added on top of the base Claudia app.