# Suggested Commands for Claudia Enhanced Development

## Development Commands

### Frontend Development
```bash
# Start development server (frontend + backend)
bun run tauri dev

# Run frontend only
bun run dev

# Build the application
bun run tauri build

# Type checking
bunx tsc --noEmit
```

### Backend Development (Rust)
```bash
# Run Rust tests
cd src-tauri && cargo test

# Format Rust code
cd src-tauri && cargo fmt

# Check Rust code
cd src-tauri && cargo check
```

### Build Executables
```bash
# Build for current platform
bun run build:executables:current

# Build for specific platforms
bun run build:executables:linux
bun run build:executables:macos
bun run build:executables:windows
```

### Project Management
```bash
# Install dependencies
bun install

# Build production version
bun run build
bun run build:strict  # With TypeScript strict checking
```

### System Utilities (Darwin/macOS)
- `git` - Version control
- `ls` - List directory contents
- `cd` - Change directory
- `grep` / `rg` (ripgrep) - Search in files
- `find` - Find files and directories

## Important Notes
- Always run `bun run tauri dev` for full development experience
- Use `bunx tsc --noEmit` to check TypeScript types before committing
- Format Rust code with `cargo fmt` before committing
- Run tests with `cargo test` to ensure backend functionality