# Task Completion Checklist

When completing a development task in Claudia Enhanced, ensure the following:

## Frontend (TypeScript/React)
1. **Type Checking**: Run `bunx tsc --noEmit` to ensure no TypeScript errors
2. **Component Testing**: Test the component in development mode (`bun run tauri dev`)
3. **Responsive Design**: Verify component works on different screen sizes
4. **Theme Support**: Check component appearance in both light and dark themes
5. **Accessibility**: Ensure proper ARIA labels and keyboard navigation

## Backend (Rust)
1. **Format Code**: Run `cd src-tauri && cargo fmt`
2. **Check Code**: Run `cd src-tauri && cargo check`
3. **Run Tests**: Execute `cd src-tauri && cargo test`
4. **Error Handling**: Verify proper error messages and recovery

## Integration
1. **Test Full App**: Run `bun run tauri dev` and test the complete flow
2. **Cross-Platform**: If possible, test on different OS platforms
3. **Performance**: Check for any performance regressions
4. **Memory Leaks**: Monitor for memory issues in long-running sessions

## Code Quality
1. **Follow Conventions**: Ensure code follows established patterns
2. **No Console Logs**: Remove any debug console.log statements
3. **Error Boundaries**: Add error boundaries for new components if needed
4. **Documentation**: Update comments for complex logic

## Before Committing
1. Run TypeScript check: `bunx tsc --noEmit`
2. Format Rust code: `cd src-tauri && cargo fmt`
3. Run Rust tests: `cd src-tauri && cargo test`
4. Test the feature end-to-end in the app
5. Verify no regression in existing features

## Git Workflow
- Create meaningful commit messages
- Keep commits focused on single features/fixes
- Update documentation if adding new features