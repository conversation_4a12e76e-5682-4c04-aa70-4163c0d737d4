# MCP Servers Configuration

This directory contains configuration files for three powerful MCP (Model Context Protocol) servers that enhance AI capabilities.

## Available Servers

### 1. Multi Fetch MCP Server
**File:** `mcp-mult-fetch.json`
**Package:** `@lmcc-dev/mult-fetch-mcp-server`

**Features:**
- Versatile web content fetching tool
- Multiple modes: browser-based and Node.js-based fetching
- Multiple formats: HTML, JSON, Markdown, Text
- Intelligent proxy detection
- Content chunking for large pages
- Bilingual support (English/Chinese)
- Smart content extraction using Mozilla's Readability
- Browser automation with scrolling, cookie management

**Use Cases:**
- Web scraping and content extraction
- Fetching documentation from websites
- Converting web content to different formats
- Handling dynamic content that requires browser rendering

### 2. Context7 MCP Server
**File:** `mcp-context7.json`
**Package:** `@upstash/context7-mcp`

**Features:**
- Up-to-date code documentation for LLMs
- Version-specific documentation and code examples
- Fetches current information directly from source
- Eliminates outdated training data issues
- Prevents API hallucinations

**Use Cases:**
- Getting current documentation for libraries and frameworks
- Ensuring code examples are up-to-date
- Accessing version-specific API information
- Improving code generation accuracy

### 3. Memory MCP Server
**File:** `mcp-memory.json`
**Package:** `@modelcontextprotocol/server-memory`

**Features:**
- Persistent memory using local knowledge graph
- Entity and relationship management
- Cross-chat information retention
- Observation tracking
- Graph-based data storage

**Use Cases:**
- Remembering user preferences across sessions
- Building knowledge about projects and people
- Maintaining context in long-term interactions
- Creating persistent AI assistants

## Installation Options

### Option 1: All Servers (Recommended)
Use the combined configuration file:
```json
// Use mcp-servers-config.json
```

### Option 2: Individual Servers
Choose specific servers based on your needs:
- For web fetching: `mcp-mult-fetch.json`
- For documentation: `mcp-context7.json`
- For memory: `mcp-memory.json`

## Setup Instructions

### For Claude Desktop
1. Locate your Claude Desktop configuration file:
   - **macOS:** `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Windows:** `%APPDATA%/Claude/claude_desktop_config.json`

2. Copy the contents of your chosen configuration file into the Claude config

3. Restart Claude Desktop

### For Cursor IDE
1. Go to: Settings → Cursor Settings → MCP → Add new global MCP server
2. Paste the configuration into `~/.cursor/mcp.json`
3. Restart Cursor

### For VS Code
1. Add configuration to your VS Code settings JSON
2. Use the `mcp` key in settings
3. Restart VS Code

## Environment Variables

### Multi Fetch Server
- `MCP_LANG`: Set language ("en" or "zh")

### Memory Server
- `MEMORY_FILE_PATH`: Custom path for memory storage file

## Usage Examples

### Multi Fetch Server
```
// Fetch webpage content
"Fetch the content from https://example.com in markdown format"

// Extract main article content
"Extract the main article content from this news website"
```

### Context7 Server
```
// Get up-to-date documentation
"Create a Next.js middleware with JWT validation. use context7"

// Version-specific examples
"Show me React 18 hooks examples. use context7"
```

### Memory Server
```
// Store information
"Remember that John works at Anthropic and prefers morning meetings"

// Retrieve information
"What do you remember about John?"
```

## Troubleshooting

1. **Server not starting:** Ensure Node.js is installed and npx is available
2. **Permission issues:** Check file permissions for configuration files
3. **Network issues:** Verify internet connection for package downloads
4. **Memory issues:** Check disk space for memory server storage

## Advanced Configuration

### Custom Installation Paths
Replace `npx` commands with full paths if needed:
```json
{
  "command": "/usr/local/bin/node",
  "args": ["/path/to/server"]
}
```

### Docker Installation
For containerized environments:
```json
{
  "command": "docker",
  "args": ["run", "-i", "--rm", "mcp/server-name"]
}
```

## Support

- Multi Fetch: [GitHub Repository](https://github.com/lmcc-dev/mult-fetch-mcp-server)
- Context7: [GitHub Repository](https://github.com/upstash/context7)
- Memory: [GitHub Repository](https://github.com/modelcontextprotocol/servers/tree/main/src/memory)

For issues, please refer to the respective GitHub repositories or the MCP documentation.