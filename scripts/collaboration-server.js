const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

app.use(cors());
app.use(express.json());

// Store active sessions and collaborators
const sessions = new Map();
const collaborators = new Map();

// Session structure
class Session {
  constructor(id, projectPath, ownerId) {
    this.id = id;
    this.code = this.generateCode();
    this.projectPath = projectPath;
    this.ownerId = ownerId;
    this.collaborators = new Set([ownerId]);
    this.created = new Date();
    this.lastActivity = new Date();
  }

  generateCode() {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  addCollaborator(userId) {
    this.collaborators.add(userId);
    this.lastActivity = new Date();
  }

  removeCollaborator(userId) {
    this.collaborators.delete(userId);
    this.lastActivity = new Date();
  }

  toJSON() {
    return {
      id: this.id,
      code: this.code,
      projectPath: this.projectPath,
      collaboratorCount: this.collaborators.size,
      created: this.created,
      lastActivity: this.lastActivity
    };
  }
}

// Collaborator structure
class Collaborator {
  constructor(id, sessionId, name, email, role) {
    this.id = id;
    this.sessionId = sessionId;
    this.name = name;
    this.email = email;
    this.role = role;
    this.status = 'online';
    this.color = this.generateColor();
    this.cursor = null;
    this.ws = null;
  }

  generateColor() {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', 
      '#98D8C8', '#6C5CE7', '#FD79A8', '#FDCB6E'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      role: this.role,
      status: this.status,
      color: this.color,
      cursor: this.cursor
    };
  }
}

// WebSocket connection handler
wss.on('connection', (ws, req) => {
  const sessionId = req.url.split('/').pop();
  const userId = uuidv4();
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      handleWebSocketMessage(sessionId, userId, data, ws);
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  });

  ws.on('close', () => {
    handleCollaboratorDisconnect(sessionId, userId);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
});

function handleWebSocketMessage(sessionId, userId, data, ws) {
  const session = sessions.get(sessionId);
  if (!session) return;

  switch (data.type) {
    case 'join':
      handleJoinSession(sessionId, userId, data, ws);
      break;
    case 'cursor-update':
      handleCursorUpdate(sessionId, userId, data);
      break;
    case 'chat-message':
      handleChatMessage(sessionId, userId, data);
      break;
    case 'code-change':
      handleCodeChange(sessionId, userId, data);
      break;
  }
}

function handleJoinSession(sessionId, userId, data, ws) {
  const session = sessions.get(sessionId);
  if (!session) return;

  // Create collaborator
  const collaborator = new Collaborator(
    userId,
    sessionId,
    data.name || 'Anonymous',
    data.email || '<EMAIL>',
    data.role || 'viewer'
  );
  collaborator.ws = ws;

  collaborators.set(userId, collaborator);
  session.addCollaborator(userId);

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'welcome',
    userId,
    sessionId,
    collaborators: Array.from(collaborators.values())
      .filter(c => c.sessionId === sessionId)
      .map(c => c.toJSON())
  }));

  // Broadcast new collaborator to others
  broadcastToSession(sessionId, {
    type: 'collaborator-joined',
    collaborator: collaborator.toJSON()
  }, userId);
}

function handleCursorUpdate(sessionId, userId, data) {
  const collaborator = collaborators.get(userId);
  if (!collaborator) return;

  collaborator.cursor = data.cursor;

  broadcastToSession(sessionId, {
    type: 'cursor-update',
    userId,
    cursor: data.cursor
  }, userId);
}

function handleChatMessage(sessionId, userId, data) {
  const collaborator = collaborators.get(userId);
  if (!collaborator) return;

  const message = {
    id: uuidv4(),
    userId,
    userName: collaborator.name,
    message: data.message.message,
    timestamp: new Date(),
    type: data.message.type || 'text'
  };

  broadcastToSession(sessionId, {
    type: 'chat-message',
    message
  });
}

function handleCodeChange(sessionId, userId, data) {
  broadcastToSession(sessionId, {
    type: 'code-change',
    userId,
    change: data.change
  }, userId);
}

function handleCollaboratorDisconnect(sessionId, userId) {
  const collaborator = collaborators.get(userId);
  if (!collaborator) return;

  const session = sessions.get(sessionId);
  if (session) {
    session.removeCollaborator(userId);
    
    // Clean up empty sessions
    if (session.collaborators.size === 0) {
      sessions.delete(sessionId);
    }
  }

  collaborators.delete(userId);

  // Broadcast disconnect
  broadcastToSession(sessionId, {
    type: 'collaborator-left',
    collaboratorId: userId,
    collaboratorName: collaborator.name
  });
}

function broadcastToSession(sessionId, message, excludeUserId = null) {
  const sessionCollaborators = Array.from(collaborators.values())
    .filter(c => c.sessionId === sessionId && c.id !== excludeUserId);

  sessionCollaborators.forEach(collaborator => {
    if (collaborator.ws && collaborator.ws.readyState === WebSocket.OPEN) {
      collaborator.ws.send(JSON.stringify(message));
    }
  });
}

// REST API endpoints
app.post('/api/sessions', (req, res) => {
  const { projectPath, ownerId } = req.body;
  const sessionId = uuidv4();
  
  const session = new Session(sessionId, projectPath, ownerId);
  sessions.set(sessionId, session);

  const port = process.env.PORT || 3030;
  const wsUrl = `ws://localhost:${port}/${sessionId}`;
  const shareUrl = `http://localhost:${port}/join/${session.code}`;

  res.json({
    sessionId,
    sessionCode: session.code,
    wsUrl,
    shareUrl
  });
});

app.get('/api/sessions/:code', (req, res) => {
  const { code } = req.params;
  
  const session = Array.from(sessions.values())
    .find(s => s.code === code);

  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  res.json(session.toJSON());
});

app.post('/api/sessions/:sessionId/invite', (req, res) => {
  const { sessionId } = req.params;
  const { email, role } = req.body;

  const session = sessions.get(sessionId);
  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  // In a real implementation, send email invitation
  console.log(`Invitation sent to ${email} for session ${sessionId} with role ${role}`);

  res.json({ success: true });
});

app.delete('/api/sessions/:sessionId/collaborators/:collaboratorId', (req, res) => {
  const { sessionId, collaboratorId } = req.params;

  const session = sessions.get(sessionId);
  if (!session) {
    return res.status(404).json({ error: 'Session not found' });
  }

  handleCollaboratorDisconnect(sessionId, collaboratorId);
  res.json({ success: true });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    sessions: sessions.size,
    collaborators: collaborators.size
  });
});

// Start server
const PORT = process.env.PORT || 3030;
server.listen(PORT, () => {
  console.log(`Collaboration server running on port ${PORT}`);
  console.log(`WebSocket URL: ws://localhost:${PORT}/{sessionId}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});