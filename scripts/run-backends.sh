#!/bin/bash

# <PERSON>ript to run both workflow automation and collaboration backends

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting Claudia Backend Services${NC}"
echo "=================================="

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Function to install dependencies
install_deps() {
    echo -e "${YELLOW}Installing Node.js dependencies...${NC}"
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}npm is not installed. Please install Node.js first.${NC}"
        exit 1
    fi
    
    npm install express ws cors uuid
}

# Check and install dependencies
if [ ! -d "node_modules" ]; then
    install_deps
fi

# Kill any existing processes on our ports
echo -e "${YELLOW}Cleaning up existing processes...${NC}"
lsof -ti:3030 | xargs kill -9 2>/dev/null || true

# Check if collaboration server port is available
if ! check_port 3030; then
    echo -e "${RED}Port 3030 is still in use. Please free it up and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}Starting Collaboration Server on port 3030...${NC}"

# Start collaboration server in background
PORT=3030 node scripts/collaboration-server.js &
COLLAB_PID=$!

# Wait a moment for server to start
sleep 2

# Check if collaboration server started successfully
if kill -0 $COLLAB_PID 2>/dev/null; then
    echo -e "${GREEN}✓ Collaboration server started successfully (PID: $COLLAB_PID)${NC}"
else
    echo -e "${RED}✗ Failed to start collaboration server${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}Backend Services Status:${NC}"
echo "========================"
echo -e "${GREEN}✓ Collaboration Server:${NC} http://localhost:3030"
echo -e "${GREEN}✓ WebSocket Endpoint:${NC} ws://localhost:3030/{sessionId}"
echo ""
echo -e "${YELLOW}To stop services:${NC}"
echo "  Press Ctrl+C or run: kill $COLLAB_PID"
echo ""
echo -e "${GREEN}Services are ready! You can now use collaboration features in Claudia.${NC}"

# Wait for interrupt
trap 'echo -e "\n${YELLOW}Stopping services...${NC}"; kill $COLLAB_PID 2>/dev/null; exit 0' INT

# Keep script running
wait