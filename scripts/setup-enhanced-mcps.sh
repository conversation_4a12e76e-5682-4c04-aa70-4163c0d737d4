#!/bin/bash

# Enhanced MCP Servers Setup Script for <PERSON> En<PERSON>ced
# This script installs a comprehensive set of productive MCP servers

set -e

echo "🚀 Setting up Enhanced MCP Servers for <PERSON> Enhanced"
echo "========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm/npx
    if ! command -v npx &> /dev/null; then
        print_error "npx is not available. Please ensure npm is properly installed."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Install MCP server function
install_mcp_server() {
    local server_name="$1"
    local package_name="$2"
    local description="$3"
    
    print_status "Installing $server_name..."
    echo "  Description: $description"
    
    if npm list -g "$package_name" &> /dev/null; then
        print_warning "$server_name is already installed globally"
    else
        if npm install -g "$package_name"; then
            print_success "$server_name installed successfully"
        else
            print_error "Failed to install $server_name"
            return 1
        fi
    fi
}

# Main installation function
install_enhanced_servers() {
    print_status "Installing enhanced MCP servers..."
    echo ""
    
    # Core Development Servers
    echo "📁 Installing Core Development Servers..."
    install_mcp_server "Filesystem Server" "@modelcontextprotocol/server-filesystem" "Secure file system operations"
    install_mcp_server "Git Server" "@modelcontextprotocol/server-git" "Git version control operations"
    install_mcp_server "GitHub Server" "@modelcontextprotocol/server-github" "GitHub repository integration"
    echo ""
    
    # AI Enhancement Servers
    echo "🧠 Installing AI Enhancement Servers..."
    install_mcp_server "Memory Server" "@modelcontextprotocol/server-memory" "Persistent memory storage"
    install_mcp_server "Sequential Thinking" "@modelcontextprotocol/server-sequential-thinking" "Advanced reasoning capabilities"
    install_mcp_server "Context7" "@upstash/context7-mcp" "Up-to-date code documentation"
    echo ""
    
    # Web & Data Servers
    echo "🌐 Installing Web & Data Servers..."
    install_mcp_server "Fetch Server" "@modelcontextprotocol/server-fetch" "HTTP client for API calls"
    install_mcp_server "Multi Fetch" "@lmcc-dev/mult-fetch-mcp-server" "Advanced web content fetching"
    install_mcp_server "Brave Search" "@modelcontextprotocol/server-brave-search" "Web search capabilities"
    install_mcp_server "Puppeteer Server" "@modelcontextprotocol/server-puppeteer" "Browser automation"
    echo ""
    
    # Database Servers
    echo "🗄️ Installing Database Servers..."
    install_mcp_server "SQLite Server" "@modelcontextprotocol/server-sqlite" "Local database operations"
    install_mcp_server "PostgreSQL Server" "@modelcontextprotocol/server-postgres" "PostgreSQL database operations"
    install_mcp_server "MongoDB Server" "@modelcontextprotocol/server-mongodb" "MongoDB database operations"
    install_mcp_server "Redis Server" "@modelcontextprotocol/server-redis" "Redis cache operations"
    install_mcp_server "Elasticsearch Server" "@modelcontextprotocol/server-elasticsearch" "Search and analytics"
    echo ""
    
    # Cloud Services
    echo "☁️ Installing Cloud Services..."
    install_mcp_server "AWS Server" "@modelcontextprotocol/server-aws" "AWS cloud services"
    install_mcp_server "Azure Server" "@modelcontextprotocol/server-azure" "Microsoft Azure services"
    install_mcp_server "GCP Server" "@modelcontextprotocol/server-gcp" "Google Cloud Platform services"
    echo ""
    
    # DevOps & Infrastructure
    echo "🐳 Installing DevOps & Infrastructure..."
    install_mcp_server "Docker Server" "@modelcontextprotocol/server-docker" "Container management"
    install_mcp_server "Kubernetes Server" "@modelcontextprotocol/server-kubernetes" "Kubernetes operations"
    install_mcp_server "Terraform Server" "@modelcontextprotocol/server-terraform" "Infrastructure as code"
    install_mcp_server "Ansible Server" "@modelcontextprotocol/server-ansible" "Automation and configuration"
    install_mcp_server "Vault Server" "@modelcontextprotocol/server-vault" "Secrets management"
    echo ""
    
    # Productivity & Communication
    echo "💼 Installing Productivity & Communication..."
    install_mcp_server "Slack Server" "@modelcontextprotocol/server-slack" "Team communication"
    install_mcp_server "Gmail Server" "@modelcontextprotocol/server-gmail" "Email management"
    install_mcp_server "Google Drive Server" "@modelcontextprotocol/server-gdrive" "Cloud file storage"
    install_mcp_server "Notion Server" "@modelcontextprotocol/server-notion" "Documentation and project management"
    install_mcp_server "Linear Server" "@modelcontextprotocol/server-linear" "Project management"
    install_mcp_server "Jira Server" "@modelcontextprotocol/server-jira" "Issue tracking"
    install_mcp_server "Confluence Server" "@modelcontextprotocol/server-confluence" "Knowledge management"
    echo ""
    
    # AI & Development Tools
    echo "🤖 Installing AI & Development Tools..."
    install_mcp_server "Anthropic Server" "@modelcontextprotocol/server-anthropic" "Anthropic AI services"
    install_mcp_server "OpenAI Server" "@modelcontextprotocol/server-openai" "OpenAI services"
    install_mcp_server "EverArt Server" "@modelcontextprotocol/server-everart" "AI image generation"
    install_mcp_server "Sentry Server" "@modelcontextprotocol/server-sentry" "Error tracking"
    echo ""
    
    # Utility Servers
    echo "⏰ Installing Utility Servers..."
    install_mcp_server "Time Server" "@modelcontextprotocol/server-time" "Time and date operations"
    echo ""
}

# Create comprehensive environment template
create_enhanced_env_template() {
    print_status "Creating comprehensive environment variables template..."
    
    cat > .env.mcp.enhanced << 'EOF'
# Enhanced MCP Server Environment Variables Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# CORE DEVELOPMENT SERVICES
# =============================================================================

# GitHub Integration (Required for GitHub MCP)
GITHUB_PERSONAL_ACCESS_TOKEN=your_github_token_here

# =============================================================================
# WEB & SEARCH SERVICES
# =============================================================================

# Brave Search (Required for Brave Search MCP)
BRAVE_API_KEY=your_brave_api_key_here

# Multi Fetch Configuration
MCP_LANG=en

# =============================================================================
# CLOUD SERVICES
# =============================================================================

# AWS Services
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# Microsoft Azure
AZURE_CLIENT_ID=your_azure_client_id
AZURE_CLIENT_SECRET=your_azure_client_secret
AZURE_TENANT_ID=your_azure_tenant_id

# Google Cloud Platform
GOOGLE_APPLICATION_CREDENTIALS=path_to_service_account_json

# =============================================================================
# DATABASE SERVICES
# =============================================================================

# PostgreSQL
POSTGRES_CONNECTION_STRING=postgresql://user:pass@localhost:5432/db

# MongoDB
MONGODB_URI=mongodb://localhost:27017/mydb

# Redis
REDIS_URL=redis://localhost:6379

# Elasticsearch
ELASTICSEARCH_URL=http://localhost:9200

# =============================================================================
# PRODUCTIVITY & COMMUNICATION
# =============================================================================

# Slack Integration
SLACK_BOT_TOKEN=xoxb-your-bot-token-here

# Gmail Integration
GMAIL_CREDENTIALS=path_to_gmail_credentials_json

# Google Drive
GOOGLE_DRIVE_CREDENTIALS=path_to_google_drive_credentials_json

# Notion
NOTION_API_KEY=your_notion_api_key

# Linear
LINEAR_API_KEY=your_linear_api_key

# Atlassian Services
JIRA_URL=https://your-org.atlassian.net
JIRA_USERNAME=your_jira_username
JIRA_API_TOKEN=your_jira_api_token

CONFLUENCE_URL=https://your-org.atlassian.net/wiki
CONFLUENCE_USERNAME=your_confluence_username
CONFLUENCE_API_TOKEN=your_confluence_api_token

# =============================================================================
# DEVOPS & INFRASTRUCTURE
# =============================================================================

# Kubernetes
KUBECONFIG=path_to_kubeconfig

# HashiCorp Vault
VAULT_ADDR=https://vault.example.com
VAULT_TOKEN=your_vault_token_here

# =============================================================================
# AI SERVICES
# =============================================================================

# Anthropic Claude
ANTHROPIC_API_KEY=your_anthropic_api_key

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# EverArt (AI Image Generation)
EVERART_API_KEY=your_everart_key_here

# =============================================================================
# MONITORING & ERROR TRACKING
# =============================================================================

# Sentry
SENTRY_AUTH_TOKEN=your_sentry_auth_token
SENTRY_ORG=your_sentry_org

# =============================================================================
# MEMORY & STORAGE
# =============================================================================

# Memory Server
MEMORY_FILE_PATH=./memory.json

EOF

    print_success "Enhanced environment template created: .env.mcp.enhanced"
}

# Create configuration selector script
create_config_selector() {
    print_status "Creating MCP configuration selector..."
    
    cat > select-mcp-config.sh << 'EOF'
#!/bin/bash

# MCP Configuration Selector
# Choose which MCP servers to enable based on your needs

echo "🔧 MCP Configuration Selector"
echo "============================="
echo ""
echo "Select your usage profile:"
echo "1) Essential (3 servers) - Basic productivity"
echo "2) Developer (10 servers) - Software development"
echo "3) Enterprise (15 servers) - Team collaboration"
echo "4) Full Stack (all servers) - Maximum capabilities"
echo "5) Custom - Choose specific servers"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "Creating essential configuration..."
        jq '.mcpServers | with_entries(select(.key | IN("memory", "context7", "mult-fetch-mcp-server")))' mcp-servers-config.json > mcp-essential.json
        echo "✅ Essential config saved to mcp-essential.json"
        ;;
    2)
        echo "Creating developer configuration..."
        jq '.mcpServers | with_entries(select(.key | IN("memory", "context7", "mult-fetch-mcp-server", "filesystem", "git", "github", "sqlite", "fetch", "time", "sequential-thinking")))' mcp-servers-config.json > mcp-developer.json
        echo "✅ Developer config saved to mcp-developer.json"
        ;;
    3)
        echo "Creating enterprise configuration..."
        jq '.mcpServers | with_entries(select(.key | IN("memory", "context7", "mult-fetch-mcp-server", "filesystem", "git", "github", "sqlite", "fetch", "time", "sequential-thinking", "slack", "jira", "confluence", "linear", "sentry")))' mcp-servers-config.json > mcp-enterprise.json
        echo "✅ Enterprise config saved to mcp-enterprise.json"
        ;;
    4)
        echo "Using full stack configuration..."
        cp mcp-servers-config.json mcp-fullstack.json
        echo "✅ Full stack config saved to mcp-fullstack.json"
        ;;
    5)
        echo "Custom configuration builder coming soon..."
        echo "For now, manually edit mcp-servers-config.json"
        ;;
    *)
        echo "Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
echo "📋 Next steps:"
echo "1. Copy your selected config to your Claude Desktop settings"
echo "2. Set up required environment variables from .env.mcp.enhanced"
echo "3. Restart Claude Desktop"
echo ""
EOF

    chmod +x select-mcp-config.sh
    print_success "Configuration selector created: select-mcp-config.sh"
}

# Display enhanced setup instructions
show_enhanced_setup_instructions() {
    echo ""
    echo "🎉 Enhanced MCP Servers Installation Complete!"
    echo "==============================================="
    echo ""
    echo "📊 Installed Server Categories:"
    echo "• Core Development (filesystem, git, github)"
    echo "• AI Enhancement (memory, sequential-thinking, context7)"
    echo "• Web & Data (fetch, mult-fetch, brave-search, puppeteer)"
    echo "• Databases (sqlite, postgresql, mongodb, redis, elasticsearch)"
    echo "• Cloud Services (aws, azure, gcp)"
    echo "• DevOps (docker, kubernetes, terraform, ansible, vault)"
    echo "• Productivity (slack, gmail, drive, notion, linear, jira)"
    echo "• AI Services (anthropic, openai, everart)"
    echo "• Monitoring (sentry)"
    echo "• Utilities (time)"
    echo ""
    echo "🛠️ Configuration Options:"
    echo "1. Run './select-mcp-config.sh' to choose a preset configuration"
    echo "2. Use 'mcp-servers-config.json' for all servers (advanced users)"
    echo "3. Manually edit configurations for custom setups"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Copy .env.mcp.enhanced to .env and fill in your API keys"
    echo "2. Choose your configuration level using the selector script"
    echo "3. Copy chosen config to Claude Desktop settings"
    echo "4. Restart Claude Desktop and test connections"
    echo ""
    echo "📚 Documentation:"
    echo "   • Server configs: mcp-servers-config.json"
    echo "   • Environment vars: .env.mcp.enhanced"
    echo "   • Quick setup: ./select-mcp-config.sh"
    echo ""
    echo "🔑 Priority API Keys (for maximum benefit):"
    echo "   • GITHUB_PERSONAL_ACCESS_TOKEN (development workflow)"
    echo "   • BRAVE_API_KEY (web search capabilities)"
    echo "   • ANTHROPIC_API_KEY (enhanced AI reasoning)"
    echo ""
    echo "💡 Pro Tips:"
    echo "   • Start with Essential config, then upgrade as needed"
    echo "   • Only configure servers you'll actually use"
    echo "   • Keep your API keys secure and rotate them regularly"
    echo ""
}

# Main execution
main() {
    echo "Starting enhanced MCP servers installation..."
    echo ""
    
    check_prerequisites
    echo ""
    
    install_enhanced_servers
    echo ""
    
    create_enhanced_env_template
    echo ""
    
    create_config_selector
    echo ""
    
    show_enhanced_setup_instructions
}

# Handle script interruption
trap 'echo "\n\n❌ Installation interrupted. You can run this script again to continue."; exit 1' INT

# Run main function
main

echo "✅ Enhanced setup script completed successfully!"
echo "🚀 Ready to supercharge your AI development workflow!"