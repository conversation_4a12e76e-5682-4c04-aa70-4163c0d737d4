#!/usr/bin/env node

/**
 * Demo WebSocket server for Claudia Enhanced collaborative sessions
 * This is a simple mock server for development and testing
 * 
 * Usage: node scripts/demo-collaboration-server.js
 */

const WebSocket = require('ws');
const http = require('http');
const url = require('url');

const PORT = process.env.WS_PORT || 8080;
const sessions = new Map();

// Create HTTP server
const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/plain' });
  res.end('Claudia Enhanced Collaboration Server - Demo Mode\n');
});

// Create WebSocket server
const wss = new WebSocket.Server({ server });

console.log(`🚀 Demo collaboration server starting on port ${PORT}...`);

wss.on('connection', (ws, req) => {
  const pathname = url.parse(req.url).pathname;
  const sessionCode = pathname.split('/').pop();
  
  console.log(`✅ New connection for session: ${sessionCode}`);
  
  // Add to session
  if (!sessions.has(sessionCode)) {
    sessions.set(sessionCode, new Set());
  }
  sessions.get(sessionCode).add(ws);
  
  // Send welcome message
  ws.send(JSON.stringify({
    type: 'system',
    message: 'Connected to demo collaboration server',
    timestamp: new Date().toISOString()
  }));
  
  // Handle messages
  ws.on('message', (message) => {
    console.log(`📨 Received: ${message}`);
    
    try {
      const data = JSON.parse(message);
      
      // Broadcast to all clients in the same session
      const sessionClients = sessions.get(sessionCode);
      if (sessionClients) {
        sessionClients.forEach((client) => {
          if (client !== ws && client.readyState === WebSocket.OPEN) {
            client.send(JSON.stringify(data));
          }
        });
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });
  
  // Handle disconnect
  ws.on('close', () => {
    console.log(`👋 Connection closed for session: ${sessionCode}`);
    
    // Remove from session
    const sessionClients = sessions.get(sessionCode);
    if (sessionClients) {
      sessionClients.delete(ws);
      
      // Clean up empty sessions
      if (sessionClients.size === 0) {
        sessions.delete(sessionCode);
      }
    }
  });
  
  // Handle errors
  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
});

server.listen(PORT, () => {
  console.log(`✨ Demo collaboration server running on:`);
  console.log(`   HTTP: http://localhost:${PORT}`);
  console.log(`   WebSocket: ws://localhost:${PORT}/ws/[session-code]`);
  console.log('');
  console.log('📝 Note: This is a demo server for development only.');
  console.log('   It does not persist data or implement authentication.');
  console.log('');
  console.log('Press Ctrl+C to stop the server.');
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n🛑 Shutting down server...');
  
  // Close all connections
  wss.clients.forEach((client) => {
    client.close();
  });
  
  server.close(() => {
    console.log('👋 Server stopped.');
    process.exit(0);
  });
});