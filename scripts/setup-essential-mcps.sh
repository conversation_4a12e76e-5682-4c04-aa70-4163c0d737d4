#!/bin/bash

# Essential MCP Servers Setup Script for <PERSON> Enhanced
# This script installs the most commonly used and beneficial MCP servers

set -e

echo "🚀 Setting up Essential MCP Servers for <PERSON> Enhanced"
echo "======================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    # Check npm/npx
    if ! command -v npx &> /dev/null; then
        print_error "npx is not available. Please ensure npm is properly installed."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Install MCP server function
install_mcp_server() {
    local server_name="$1"
    local package_name="$2"
    local description="$3"
    
    print_status "Installing $server_name..."
    echo "  Description: $description"
    
    if npm list -g "$package_name" &> /dev/null; then
        print_warning "$server_name is already installed globally"
    else
        if npm install -g "$package_name"; then
            print_success "$server_name installed successfully"
        else
            print_error "Failed to install $server_name"
            return 1
        fi
    fi
}

# Test MCP server installation
test_mcp_server() {
    local server_name="$1"
    local test_command="$2"
    
    print_status "Testing $server_name installation..."
    
    if eval "$test_command" &> /dev/null; then
        print_success "$server_name is working correctly"
    else
        print_warning "$server_name test failed - may need additional configuration"
    fi
}

# Main installation function
install_essential_servers() {
    print_status "Installing essential MCP servers..."
    echo ""
    
    # Core Development Servers
    echo "📁 Installing Core Development Servers..."
    install_mcp_server "Filesystem Server" "@modelcontextprotocol/server-filesystem" "Secure file system operations"
    install_mcp_server "Git Server" "@modelcontextprotocol/server-git" "Git version control operations"
    install_mcp_server "GitHub Server" "@modelcontextprotocol/server-github" "GitHub repository integration"
    echo ""
    
    # AI Enhancement Servers
    echo "🧠 Installing AI Enhancement Servers..."
    install_mcp_server "Memory Server" "@modelcontextprotocol/server-memory" "Persistent memory storage"
    install_mcp_server "Sequential Thinking" "@modelcontextprotocol/server-sequential-thinking" "Advanced reasoning capabilities"
    echo ""
    
    # Web & Data Servers
    echo "🌐 Installing Web & Data Servers..."
    install_mcp_server "Fetch Server" "@modelcontextprotocol/server-fetch" "HTTP client for API calls"
    install_mcp_server "Brave Search" "@modelcontextprotocol/server-brave-search" "Web search capabilities"
    install_mcp_server "Puppeteer Server" "@modelcontextprotocol/server-puppeteer" "Browser automation"
    echo ""
    
    # Database Servers
    echo "🗄️ Installing Database Servers..."
    install_mcp_server "SQLite Server" "@modelcontextprotocol/server-sqlite" "Local database operations"
    echo ""
    
    # DevOps Servers
    echo "🐳 Installing DevOps Servers..."
    install_mcp_server "Docker Server" "@modelcontextprotocol/server-docker" "Container management"
    echo ""
    
    # Utility Servers
    echo "⏰ Installing Utility Servers..."
    install_mcp_server "Time Server" "@modelcontextprotocol/server-time" "Time and date operations"
    echo ""
}

# Create environment template
create_env_template() {
    print_status "Creating environment variables template..."
    
    cat > .env.mcp.template << 'EOF'
# MCP Server Environment Variables Template
# Copy this file to .env and fill in your actual values

# GitHub Integration (Required for GitHub MCP)
GITHUB_PERSONAL_ACCESS_TOKEN=your_github_token_here

# Brave Search (Required for Brave Search MCP)
BRAVE_API_KEY=your_brave_api_key_here

# AWS Services (Optional - for AWS integrations)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# Google Drive (Optional - for Google Drive MCP)
GOOGLE_DRIVE_CREDENTIALS=path_to_service_account_json

# Slack (Optional - for Slack MCP)
SLACK_BOT_TOKEN=xoxb-your-bot-token-here

# PostgreSQL (Optional - for PostgreSQL MCP)
POSTGRES_CONNECTION_STRING=postgresql://user:pass@localhost:5432/db

# Kubernetes (Optional - for Kubernetes MCP)
KUBECONFIG=path_to_kubeconfig

# HashiCorp Vault (Optional - for Vault MCP)
VAULT_ADDR=https://vault.example.com
VAULT_TOKEN=your_vault_token_here

# EverArt (Optional - for AI image generation)
EVERART_API_KEY=your_everart_key_here
EOF

    print_success "Environment template created: .env.mcp.template"
}

# Display setup instructions
show_setup_instructions() {
    echo ""
    echo "🎉 Essential MCP Servers Installation Complete!"
    echo "============================================="
    echo ""
    echo "📋 Next Steps:"
    echo "1. Copy .env.mcp.template to .env and fill in your API keys"
    echo "2. Open Claudia Enhanced and navigate to MCP Manager"
    echo "3. Configure the installed servers with your credentials"
    echo "4. Test connections to ensure everything is working"
    echo ""
    echo "📚 For detailed setup instructions, see:"
    echo "   docs/MCP_INTEGRATION_GUIDE.md"
    echo ""
    echo "🔑 Required API Keys:"
    echo "   • GitHub Personal Access Token (for GitHub integration)"
    echo "   • Brave Search API Key (for web search)"
    echo "   • Additional keys for optional services"
    echo ""
    echo "💡 Pro Tip: Start with GitHub and Brave Search for maximum benefit!"
    echo ""
}

# Main execution
main() {
    echo "Starting MCP servers installation..."
    echo ""
    
    check_prerequisites
    echo ""
    
    install_essential_servers
    echo ""
    
    create_env_template
    echo ""
    
    show_setup_instructions
}

# Handle script interruption
trap 'echo "\n\n❌ Installation interrupted. You can run this script again to continue."; exit 1' INT

# Run main function
main

echo "✅ Setup script completed successfully!"