<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Claudia - <PERSON> Code Session Browser</title>
    <script>
      // Apply theme before rendering to prevent flash
      (function() {
        try {
          const savedPrefs = localStorage.getItem('quick-customization');
          let theme = 'system';
          
          if (savedPrefs) {
            const prefs = JSON.parse(savedPrefs);
            theme = prefs.theme || 'system';
          }
          
          const root = document.documentElement;
          
          if (theme === 'dark') {
            root.classList.add('dark');
          } else if (theme === 'light') {
            root.classList.add('light');
          } else {
            // System preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            root.classList.add(prefersDark ? 'dark' : 'light');
          }
        } catch (e) {
          // Default to system preference on error
          const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          document.documentElement.classList.add(prefersDark ? 'dark' : 'light');
        }
      })();
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
