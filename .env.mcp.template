# MCP Server Environment Variables Template
# Copy this file to .env.mcp and fill in the values

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GITHUB_TOKEN=your_github_personal_access_token_here

# Communication Platforms
SLACK_TOKEN=your_slack_bot_token_here
DISCORD_TOKEN=your_discord_bot_token_here

# Google Services
GOOGLE_DRIVE_CLIENT_ID=your_google_drive_client_id_here
GOOGLE_DRIVE_CLIENT_SECRET=your_google_drive_client_secret_here
GOOGLE_CLOUD_PROJECT_ID=your_google_cloud_project_id_here
GOOGLE_APPLICATION_CREDENTIALS=path_to_google_service_account_json

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here
AWS_REGION=us-east-1

# Azure Configuration
AZURE_SUBSCRIPTION_ID=your_azure_subscription_id_here
AZURE_TENANT_ID=your_azure_tenant_id_here
AZURE_CLIENT_ID=your_azure_client_id_here
AZURE_CLIENT_SECRET=your_azure_client_secret_here

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/claudia_enhanced
MONGODB_URI=mongodb://localhost:27017/claudia_enhanced
REDIS_URL=redis://localhost:6379

# Search Services
BRAVE_SEARCH_API_KEY=your_brave_search_api_key_here
SERPER_API_KEY=your_serper_api_key_here

# Other Services
EVERART_API_KEY=your_everart_api_key_here
CONTEXT7_API_KEY=your_context7_api_key_here
WOLFRAM_APP_ID=your_wolfram_app_id_here
NOTION_API_KEY=your_notion_api_key_here
LINEAR_API_KEY=your_linear_api_key_here

# MCP Server Specific Configuration
MCP_SERVER_PORT=3000
MCP_MARKETPLACE_API_URL=https://api.mcp-marketplace.example.com
MCP_REGISTRY_URL=https://registry.mcp.dev

# Feature Flags
ENABLE_VOICE_CONTROL=true
ENABLE_COLLABORATIVE_SESSIONS=true
ENABLE_PERFORMANCE_PROFILING=true
ENABLE_PLUGIN_SYSTEM=true

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/claudia-enhanced/mcp.log

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=60000

# File System Paths (for MCP filesystem server)
ALLOWED_DIRECTORIES=/Users/<USER>/Documents,/Users/<USER>/Projects

# Additional MCP Server Environment Variables
MCP_SWIFT_SDK_PATH=/path/to/swift-sdk
MCP_PUPPETEER_HEADLESS=true
MCP_MEMORY_STORAGE_PATH=/path/to/memory/storage
MCP_MULT_FETCH_TIMEOUT=30000
MCP_SERENA_PROJECT_PATH=/path/to/serena/projects