{"mcpServers": {"mult-fetch-mcp-server": {"command": "npx", "args": ["@lmcc-dev/mult-fetch-mcp-server"], "env": {"MCP_LANG": "en"}, "description": "A versatile MCP-compliant web content fetching tool that supports multiple modes (browser/node), formats (HTML/JSON/Markdown/Text), and intelligent proxy detection"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "description": "Up-to-date code documentation for LLMs and AI code editors - fetches version-specific documentation and code examples"}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "./memory.json"}, "description": "Knowledge Graph Memory Server - persistent memory using a local knowledge graph for remembering information across chats"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/claudia-enhanced"], "description": "Secure file system operations with read/write access to project directory"}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "description": "Git version control operations for repository management"}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "${GITHUB_PERSONAL_ACCESS_TOKEN}"}, "description": "GitHub repository integration for issues, PRs, and repository operations"}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "description": "Local SQLite database operations for data storage and queries"}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "${BRAVE_API_KEY}"}, "description": "Web search capabilities using Brave Search API"}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "description": "HTTP client for making API calls and web requests"}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "description": "Browser automation for web scraping and testing"}, "time": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-time"], "description": "Time and date operations with timezone support"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "Advanced reasoning capabilities for complex problem solving"}, "docker": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-docker"], "description": "Docker container management and operations"}, "slack": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "${SLACK_BOT_TOKEN}"}, "description": "Slack integration for team communication and notifications"}, "gmail": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gmail"], "env": {"GMAIL_CREDENTIALS": "${GMAIL_CREDENTIALS}"}, "description": "Gmail integration for email management"}, "google-drive": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gdrive"], "env": {"GOOGLE_DRIVE_CREDENTIALS": "${GOOGLE_DRIVE_CREDENTIALS}"}, "description": "Google Drive integration for cloud file storage"}, "postgresql": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "${POSTGRES_CONNECTION_STRING}"}, "description": "PostgreSQL database operations and queries"}, "kubernetes": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-kubernetes"], "env": {"KUBECONFIG": "${KUBECONFIG}"}, "description": "Kubernetes cluster management and operations"}, "aws": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-aws"], "env": {"AWS_REGION": "${AWS_REGION}", "AWS_ACCESS_KEY_ID": "${AWS_ACCESS_KEY_ID}", "AWS_SECRET_ACCESS_KEY": "${AWS_SECRET_ACCESS_KEY}"}, "description": "AWS cloud services integration"}, "everart": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everart"], "env": {"EVERART_API_KEY": "${EVERART_API_KEY}"}, "description": "AI image generation and manipulation"}, "sentry": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sentry"], "env": {"SENTRY_AUTH_TOKEN": "${SENTRY_AUTH_TOKEN}", "SENTRY_ORG": "${SENTRY_ORG}"}, "description": "Error tracking and performance monitoring"}, "linear": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-linear"], "env": {"LINEAR_API_KEY": "${LINEAR_API_KEY}"}, "description": "Linear project management and issue tracking"}, "notion": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-notion"], "env": {"NOTION_API_KEY": "${NOTION_API_KEY}"}, "description": "Notion workspace integration for documentation and project management"}, "jira": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-jira"], "env": {"JIRA_URL": "${JIRA_URL}", "JIRA_USERNAME": "${JIRA_USERNAME}", "JIRA_API_TOKEN": "${JIRA_API_TOKEN}"}, "description": "Atlassian Jira integration for issue tracking and project management"}, "confluence": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-confluence"], "env": {"CONFLUENCE_URL": "${CONFLUENCE_URL}", "CONFLUENCE_USERNAME": "${CONFLUENCE_USERNAME}", "CONFLUENCE_API_TOKEN": "${CONFLUENCE_API_TOKEN}"}, "description": "Atlassian Confluence integration for documentation and knowledge management"}, "anthropic": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-anthropic"], "env": {"ANTHROPIC_API_KEY": "${ANTHROPIC_API_KEY}"}, "description": "Anthropic AI services integration for advanced AI capabilities"}, "openai": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-openai"], "env": {"OPENAI_API_KEY": "${OPENAI_API_KEY}"}, "description": "OpenAI services integration for AI capabilities"}, "redis": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-redis"], "env": {"REDIS_URL": "${REDIS_URL}"}, "description": "Redis cache and data structure operations"}, "mongodb": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-mongodb"], "env": {"MONGODB_URI": "${MONGODB_URI}"}, "description": "MongoDB database operations and queries"}, "elasticsearch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-elasticsearch"], "env": {"ELASTICSEARCH_URL": "${ELASTICSEARCH_URL}"}, "description": "Elasticsearch search and analytics operations"}, "azure": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-azure"], "env": {"AZURE_CLIENT_ID": "${AZURE_CLIENT_ID}", "AZURE_CLIENT_SECRET": "${AZURE_CLIENT_SECRET}", "AZURE_TENANT_ID": "${AZURE_TENANT_ID}"}, "description": "Microsoft Azure cloud services integration"}, "gcp": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-gcp"], "env": {"GOOGLE_APPLICATION_CREDENTIALS": "${GOOGLE_APPLICATION_CREDENTIALS}"}, "description": "Google Cloud Platform services integration"}, "vault": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-vault"], "env": {"VAULT_ADDR": "${VAULT_ADDR}", "VAULT_TOKEN": "${VAULT_TOKEN}"}, "description": "HashiCorp Vault secrets management"}, "terraform": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-terraform"], "description": "Terraform infrastructure as code operations"}, "ansible": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-ansible"], "description": "Ansible automation and configuration management"}, "serena": {"command": "uvx", "args": ["--from", "git+https://github.com/oraios/serena", "serena-mcp-server", "--context", "ide-assistant", "--project", "/Users/<USER>/claudia-enhanced"], "description": "Semantic retrieval and editing capabilities for code - coding agent toolkit with global/local symbol search and code analysis"}}}